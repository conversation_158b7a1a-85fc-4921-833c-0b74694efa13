{"name": "tempfile", "version": "2.0.0", "description": "Get a random temporary file path", "license": "MIT", "repository": "sindresorhus/tempfile", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["tmp", "temp", "temporary", "tempfile", "file", "path", "random", "rand", "uuid"], "dependencies": {"temp-dir": "^1.0.0", "uuid": "^3.0.1"}, "devDependencies": {"ava": "*", "xo": "*"}}