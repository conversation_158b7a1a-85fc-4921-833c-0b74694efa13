{"name": "pngquant-bin", "version": "6.0.1", "description": "`pngquant` wrapper that makes it seamlessly available as a local dependency", "license": "GPL-3.0+", "repository": "imagemin/pngquant-bin", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON><PERSON><PERSON>", "url": "github.com/shinnn"}], "bin": {"pngquant": "cli.js"}, "engines": {"node": ">=10"}, "scripts": {"postinstall": "node lib/install.js", "test": "xo && ava --timeout=120s"}, "files": ["cli.js", "index.js", "lib", "vendor/source"], "keywords": ["imagemin", "compress", "image", "img", "minify", "optimize", "png", "pngquant"], "dependencies": {"bin-build": "^3.0.0", "bin-wrapper": "^4.0.1", "execa": "^4.0.0"}, "devDependencies": {"ava": "^3.8.0", "bin-check": "^4.0.1", "compare-size": "^3.0.0", "tempy": "^0.5.0", "xo": "^0.30.0"}}