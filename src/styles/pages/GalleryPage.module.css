/* GalleryPage.module.css */
.galleryPage {
  position: relative;
}

/* Hero Section */
.heroSection {
  position: relative;
  height: 780px;
  width: 100%;
  background-color: #000000;
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  overflow: hidden;
  margin-bottom: 90px;
}

.heroBackground {
  width: 100%;
  height: 522px;
  position: absolute;
  left: 0;
  top: 0;
  object-fit: cover;
  opacity: 0.9;
  background: linear-gradient(to left, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3));
}

.gradientOverlay {
  background: linear-gradient(0deg, rgba(18, 18, 18, 1) 0%, rgba(0, 0, 0, 1) 100%);
  width: 100vw;
  height: 85px;
  position: absolute;
  left: 0;
  top: 485px;
  filter: blur(20px);
  flex-shrink: 0;
}

.contentContainer {
  position: relative;
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 80px;
}

.heroContent {
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: absolute;
  left: 80px;
  top: 239px;
  width: 100%;
  max-width: 1280px;
}

.galleryLabel {
  color: var(--secondary-color, #ff3241);
  text-align: left;
  font-size: 12px;
  line-height: 140%;
  letter-spacing: 0.4px;
  font-weight: 500;
  margin-bottom: 5px;
  text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.5);
}

.heroTitle {
  color: #ffffff;
  text-align: left;
  font-size: 58px;
  line-height: 120%;
  letter-spacing: -0.5px;
  font-weight: 500;
  width: 959px;
  margin-bottom: 20px;
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
}

.heroSubtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 18px;
  line-height: 150%;
  font-weight: 400;
  max-width: 600px;
  margin-top: 16px;
  text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Gallery Section */
.gallerySection {
  padding: 80px 0 120px;
  background-color: #121212;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 80px;
}

.galleryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  grid-auto-rows: 200px;
}

.galleryItem {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background-color: #1a1a1a;
}

.galleryItem:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.galleryItem.small {
  grid-row: span 1;
}

.galleryItem.medium {
  grid-row: span 2;
}

.galleryItem.large {
  grid-row: span 3;
  grid-column: span 2;
}

.galleryImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.galleryItem:hover .galleryImage {
  transform: scale(1.05);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  display: flex;
  align-items: flex-end;
  padding: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.galleryItem:hover .imageOverlay {
  opacity: 1;
}

.imageCategory {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: rgba(255, 50, 65, 0.8);
  padding: 6px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

/* Category specific styling */
.galleryItem.mission .imageCategory {
  background: rgba(255, 50, 65, 0.8);
}

.galleryItem.team .imageCategory {
  background: rgba(187, 222, 208, 0.8);
  color: #121212;
}

.galleryItem.payload .imageCategory {
  background: rgba(135, 194, 170, 0.8);
  color: #121212;
}

.galleryItem.activity .imageCategory {
  background: rgba(255, 255, 255, 0.8);
  color: #121212;
}

/* CTA Section */
.ctaSection {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  padding: 80px 0;
  text-align: center;
}

.ctaContent {
  max-width: 600px;
  margin: 0 auto;
}

.ctaTitle {
  color: #ffffff;
  font-size: 42px;
  font-weight: 500;
  line-height: 120%;
  margin-bottom: 20px;
}

.ctaDescription {
  color: rgba(255, 255, 255, 0.8);
  font-size: 18px;
  line-height: 150%;
  margin-bottom: 40px;
}

.ctaButtons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton,
.secondaryButton {
  padding: 16px 32px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-block;
}

.primaryButton {
  background: linear-gradient(135deg, #ff3241 0%, #e02030 100%);
  color: #ffffff;
  border: none;
}

.primaryButton:hover {
  background: linear-gradient(135deg, #e02030 0%, #cc1c2a 100%);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(255, 50, 65, 0.3);
}

.secondaryButton {
  background: transparent;
  color: #ffffff;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.secondaryButton:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.footerMargin {
  height: 80px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contentContainer,
  .container {
    padding: 0 40px;
  }
  
  .heroContent {
    left: 40px;
  }
  
  .heroTitle {
    font-size: 48px;
    width: 100%;
    max-width: 800px;
  }
  
  .galleryGrid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
  }
  
  .galleryItem.large {
    grid-column: span 1;
    grid-row: span 2;
  }
}

@media (max-width: 768px) {
  .heroSection {
    height: 600px;
    margin-bottom: 60px;
  }
  
  .heroContent {
    top: 180px;
    left: 20px;
    padding: 0 20px;
  }
  
  .contentContainer,
  .container {
    padding: 0 20px;
  }
  
  .heroTitle {
    font-size: 36px;
  }
  
  .heroSubtitle {
    font-size: 16px;
  }
  
  .galleryGrid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
  }
  
  .galleryItem.large,
  .galleryItem.medium {
    grid-column: span 1;
    grid-row: span 1;
  }
  
  .ctaTitle {
    font-size: 32px;
  }
  
  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 28px;
  }
  
  .galleryGrid {
    grid-template-columns: 1fr;
  }
  
  .gallerySection {
    padding: 60px 0 80px;
  }
  
  .ctaSection {
    padding: 60px 0;
  }
}
