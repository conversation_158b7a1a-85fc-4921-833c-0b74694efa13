/* GalleryPage.module.css */
.galleryPage {
  position: relative;
}

/* Hero Section */
.heroSection {
  position: relative;
  height: 780px;
  width: 100%;
  background-color: #000000;
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  overflow: hidden;
  margin-bottom: 90px;
}

.heroBackground {
  width: 100%;
  height: 522px;
  position: absolute;
  left: 0;
  top: 0;
  object-fit: cover;
  opacity: 0.9;
  background: linear-gradient(to left, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3));
}

.gradientOverlay {
  background: linear-gradient(0deg, rgba(18, 18, 18, 1) 0%, rgba(0, 0, 0, 1) 100%);
  width: 100vw;
  height: 85px;
  position: absolute;
  left: 0;
  top: 485px;
  filter: blur(20px);
  flex-shrink: 0;
}

.contentContainer {
  position: relative;
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 80px;
}

.heroContent {
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: absolute;
  left: 80px;
  top: 239px;
  width: 100%;
  max-width: 1280px;
}

.galleryLabel {
  color: var(--secondary-color, #ff3241);
  text-align: left;
  font-size: 12px;
  line-height: 140%;
  letter-spacing: 0.4px;
  font-weight: 500;
  margin-bottom: 5px;
  text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.5);
}

.heroTitle {
  color: #ffffff;
  text-align: left;
  font-size: 58px;
  line-height: 120%;
  letter-spacing: -0.5px;
  font-weight: 500;
  width: 959px;
  margin-bottom: 20px;
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
}

.heroSubtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 18px;
  line-height: 150%;
  font-weight: 400;
  max-width: 600px;
  margin-top: 16px;
  text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Gallery Section */
.gallerySection {
  padding: 60px 0;
  background-color: #121212;
}

.gallerySection:nth-child(odd) {
  background-color: #0f0f0f;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 80px;
}

.sectionTitle {
  color: #ffffff;
  font-size: 32px;
  font-weight: 500;
  margin-bottom: 40px;
  text-align: center;
  position: relative;
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #ff3241 0%, #e02030 100%);
  border-radius: 2px;
}

/* Bento Grid Layout */
.bentoGrid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: repeat(6, 120px);
  gap: 16px;
  margin-bottom: 40px;
}

.bentoItem {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background-color: #1a1a1a;
}

.bentoItem:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

/* Bento Grid Positions */
.bentoItem.hero {
  grid-column: span 6;
  grid-row: span 3;
}

.bentoItem.large {
  grid-column: span 4;
  grid-row: span 2;
}

.bentoItem.medium {
  grid-column: span 3;
  grid-row: span 2;
}

.bentoItem.small {
  grid-column: span 2;
  grid-row: span 1;
}

.bentoImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.bentoItem:hover .bentoImage {
  transform: scale(1.08);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.8) 100%
  );
  display: flex;
  align-items: flex-end;
  padding: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.bentoItem:hover .imageOverlay {
  opacity: 1;
}

.imageCategory {
  color: #ffffff;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  padding: 8px 16px;
  border-radius: 25px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Category specific styling */
.imageCategory {
  background: rgba(255, 50, 65, 0.9);
}

/* CTA Section */
.ctaSection {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  padding: 80px 0;
  text-align: center;
}

.ctaContent {
  max-width: 600px;
  margin: 0 auto;
}

.ctaTitle {
  color: #ffffff;
  font-size: 42px;
  font-weight: 500;
  line-height: 120%;
  margin-bottom: 20px;
}

.ctaDescription {
  color: rgba(255, 255, 255, 0.8);
  font-size: 18px;
  line-height: 150%;
  margin-bottom: 40px;
}

.ctaButtons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton,
.secondaryButton {
  padding: 16px 32px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-block;
}

.primaryButton {
  background: linear-gradient(135deg, #ff3241 0%, #e02030 100%);
  color: #ffffff;
  border: none;
}

.primaryButton:hover {
  background: linear-gradient(135deg, #e02030 0%, #cc1c2a 100%);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(255, 50, 65, 0.3);
}

.secondaryButton {
  background: transparent;
  color: #ffffff;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.secondaryButton:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.footerMargin {
  height: 80px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contentContainer,
  .container {
    padding: 0 40px;
  }

  .heroContent {
    left: 40px;
  }

  .heroTitle {
    font-size: 48px;
    width: 100%;
    max-width: 800px;
  }

  .sectionTitle {
    font-size: 28px;
    margin-bottom: 30px;
  }

  .bentoGrid {
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: repeat(8, 100px);
    gap: 12px;
  }

  .bentoItem.hero {
    grid-column: span 4;
    grid-row: span 2;
  }

  .bentoItem.large {
    grid-column: span 3;
    grid-row: span 2;
  }

  .bentoItem.medium {
    grid-column: span 2;
    grid-row: span 2;
  }

  .bentoItem.small {
    grid-column: span 2;
    grid-row: span 1;
  }
}

@media (max-width: 768px) {
  .heroSection {
    height: 600px;
    margin-bottom: 60px;
  }

  .heroContent {
    top: 180px;
    left: 20px;
    padding: 0 20px;
  }

  .contentContainer,
  .container {
    padding: 0 20px;
  }

  .heroTitle {
    font-size: 36px;
  }

  .heroSubtitle {
    font-size: 16px;
  }

  .sectionTitle {
    font-size: 24px;
    margin-bottom: 25px;
  }

  .gallerySection {
    padding: 40px 0;
  }

  .bentoGrid {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(12, 80px);
    gap: 8px;
  }

  .bentoItem.hero {
    grid-column: span 4;
    grid-row: span 2;
  }

  .bentoItem.large {
    grid-column: span 2;
    grid-row: span 2;
  }

  .bentoItem.medium {
    grid-column: span 2;
    grid-row: span 1;
  }

  .bentoItem.small {
    grid-column: span 1;
    grid-row: span 1;
  }

  .imageOverlay {
    padding: 12px;
  }

  .imageCategory {
    font-size: 10px;
    padding: 6px 12px;
  }

  .ctaTitle {
    font-size: 32px;
  }

  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }

  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 28px;
  }

  .sectionTitle {
    font-size: 20px;
    margin-bottom: 20px;
  }

  .bentoGrid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(16, 60px);
    gap: 6px;
  }

  .bentoItem.hero {
    grid-column: span 2;
    grid-row: span 3;
  }

  .bentoItem.large {
    grid-column: span 2;
    grid-row: span 2;
  }

  .bentoItem.medium {
    grid-column: span 1;
    grid-row: span 2;
  }

  .bentoItem.small {
    grid-column: span 1;
    grid-row: span 1;
  }

  .gallerySection {
    padding: 30px 0;
  }

  .ctaSection {
    padding: 60px 0;
  }
}
