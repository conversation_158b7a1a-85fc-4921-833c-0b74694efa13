/* GalleryPage.module.css */
.galleryPage {
  position: relative;
}

/* Hero Section - Exactly matching About Page */
.heroSection {
  position: relative;
  height: 780px;
  width: 100%;
  background-color: #000000;
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  overflow: hidden;
  margin-bottom: 90px;
}

.heroBackground {
  width: 100%;
  height: 522px;
  position: absolute;
  left: 0;
  top: 0;
  object-fit: cover;
  opacity: 0.9; /* Reduced opacity by 10% */
  background: linear-gradient(to left, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)); /* Darkened overlay */
}

.gradientOverlay {
  background: linear-gradient(0deg, rgba(18, 18, 18, 1) 0%, rgba(0, 0, 0, 1) 100%);
  width: 100vw; /* Full width of the screen */
  height: 85px;
  position: absolute;
  left: 0;
  top: 485px;
  filter: blur(20px); /* Changed from 25px to 20px */
  flex-shrink: 0;
}

.contentContainer {
  position: relative;
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 80px;
}

.heroContent {
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: absolute;
  left: 80px;
  top: 239px;
  width: 100%;
  max-width: 1280px;
}

.galleryLabel {
  color: var(--secondary-color, #ff3241);
  text-align: left;
  font-size: 12px;
  line-height: 140%;
  letter-spacing: 0.4px;
  font-weight: 500; /* Increased from 400 to 500 */
  margin-bottom: 5px;
  text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.5); /* Added text shadow for better contrast */
}

.heroTitle {
  color: #ffffff;
  text-align: left;
  font-size: 58px;
  line-height: 120%;
  letter-spacing: -0.5px;
  font-weight: 500;
  width: 959px;
  margin-bottom: 20px;
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5); /* Added text shadow for better contrast */
}

.bottomContainer {
  display: flex;
  flex-direction: column;
  gap: 80px;
  align-items: flex-end;
  justify-content: flex-start;
  width: 100%;
  max-width: 1280px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 602px;
}

.descriptionContainer {
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: flex-end;
  justify-content: flex-start;
  align-self: stretch;
}

.descriptionText {
  color: #ffffff; /* Changed from #eeeeee to pure white for better contrast */
  text-align: right;
  font-size: 23px;
  line-height: 140%;
  font-weight: 500;
  width: 100%;
  max-width: 600px;
  margin-bottom: 10px;
  text-shadow: 0px 1px 3px rgba(0, 0, 0, 0.6); /* Added text shadow for better contrast */
}

/* Gallery Section */
.gallerySection {
  padding: 60px 0;
  background-color: #121212;
}

.gallerySection:nth-child(odd) {
  background-color: #0f0f0f;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 80px;
}

.sectionTitle {
  color: #ffffff;
  font-size: 32px;
  font-weight: 500;
  margin-bottom: 40px;
  text-align: center;
  position: relative;
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #ff3241 0%, #e02030 100%);
  border-radius: 2px;
}

/* Bento Grid Layout */
.bentoGrid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: repeat(6, 120px);
  gap: 16px;
  margin-bottom: 40px;
}

.bentoItem {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background-color: #1a1a1a;
  user-select: none;
}

.bentoItem:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

/* Bento Grid Positions */
.bentoItem.hero {
  grid-column: span 6;
  grid-row: span 3;
}

.bentoItem.large {
  grid-column: span 4;
  grid-row: span 2;
}

.bentoItem.medium {
  grid-column: span 3;
  grid-row: span 2;
}

.bentoItem.small {
  grid-column: span 2;
  grid-row: span 1;
}

.bentoImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.bentoItem:hover .bentoImage {
  transform: scale(1.08);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.8) 100%
  );
  display: flex;
  align-items: flex-end;
  padding: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.bentoItem:hover .imageOverlay {
  opacity: 1;
}

.imageCategory {
  color: #ffffff;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  padding: 8px 16px;
  border-radius: 25px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Category specific styling */
.imageCategory {
  background: rgba(255, 50, 65, 0.9);
}

/* CTA Section */
.ctaSection {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  padding: 80px 0;
  text-align: center;
}

.ctaContent {
  max-width: 600px;
  margin: 0 auto;
}

.ctaTitle {
  color: #ffffff;
  font-size: 42px;
  font-weight: 500;
  line-height: 120%;
  margin-bottom: 20px;
}

.ctaDescription {
  color: rgba(255, 255, 255, 0.8);
  font-size: 18px;
  line-height: 150%;
  margin-bottom: 40px;
}

.ctaButtons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton,
.secondaryButton {
  padding: 16px 32px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-block;
}

.primaryButton {
  background: linear-gradient(135deg, #ff3241 0%, #e02030 100%);
  color: #ffffff;
  border: none;
}

.primaryButton:hover {
  background: linear-gradient(135deg, #e02030 0%, #cc1c2a 100%);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(255, 50, 65, 0.3);
}

.secondaryButton {
  background: transparent;
  color: #ffffff;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.secondaryButton:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.footerMargin {
  height: 80px;
}

/* Slideshow Styles */
.slideshowOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.slideshowContainer {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px 20px;
}

.closeButton {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #ffffff;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  z-index: 10001;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.navButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #ffffff;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  z-index: 10001;
}

.navButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-50%) scale(1.1);
}

.slideshowImageContainer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 90vw;
  max-height: 70vh;
  margin-bottom: 20px;
}

.slideshowImage {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.slideshowInfo {
  text-align: center;
  margin-bottom: 20px;
  color: #ffffff;
}

.slideshowTitle {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #ffffff;
}

.slideshowCounter {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.thumbnailContainer {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding: 10px 0;
  max-width: 90vw;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.thumbnailContainer::-webkit-scrollbar {
  height: 4px;
}

.thumbnailContainer::-webkit-scrollbar-track {
  background: transparent;
}

.thumbnailContainer::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.thumbnail {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail:hover {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.activeThumbnail {
  border-color: #ff3241;
  transform: scale(1.1);
}

.activeThumbnail:hover {
  border-color: #ff3241;
}

/* Hero Section Responsive Design - Matching About Page */
@media (max-width: 1200px) {
  .heroTitle {
    font-size: 48px;
    width: 800px;
  }

  .contentContainer {
    padding: 0 40px;
  }

  .heroContent {
    left: 40px;
  }
}

@media (max-width: 991px) {
  .heroSection {
    height: auto;
    padding-bottom: 400px;
  }

  .heroTitle {
    font-size: 36px;
    width: 100%;
    max-width: 700px;
  }

  .bottomContainer {
    position: relative;
    top: auto;
    margin-top: 350px;
  }
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: 32px;
  }

  .descriptionText {
    font-size: 18px;
  }

  .contentContainer {
    padding: 0 20px;
  }

  .heroContent {
    left: 20px;
  }
}

@media (max-width: 576px) {
  .heroTitle {
    font-size: 28px;
  }

  .descriptionText {
    font-size: 16px;
  }
}

/* Gallery Sections Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 0 40px;
  }

  .sectionTitle {
    font-size: 28px;
    margin-bottom: 30px;
  }

  .bentoGrid {
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: repeat(8, 100px);
    gap: 12px;
  }

  .bentoItem.hero {
    grid-column: span 4;
    grid-row: span 2;
  }

  .bentoItem.large {
    grid-column: span 3;
    grid-row: span 2;
  }

  .bentoItem.medium {
    grid-column: span 2;
    grid-row: span 2;
  }

  .bentoItem.small {
    grid-column: span 2;
    grid-row: span 1;
  }
}

@media (max-width: 768px) {
  .sectionTitle {
    font-size: 24px;
    margin-bottom: 25px;
  }

  .gallerySection {
    padding: 40px 0;
  }

  .container {
    padding: 0 20px;
  }

  .bentoGrid {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(12, 80px);
    gap: 8px;
  }

  .bentoItem.hero {
    grid-column: span 4;
    grid-row: span 2;
  }

  .bentoItem.large {
    grid-column: span 2;
    grid-row: span 2;
  }

  .bentoItem.medium {
    grid-column: span 2;
    grid-row: span 1;
  }

  .bentoItem.small {
    grid-column: span 1;
    grid-row: span 1;
  }

  .imageOverlay {
    padding: 12px;
  }

  .imageCategory {
    font-size: 10px;
    padding: 6px 12px;
  }

  .ctaTitle {
    font-size: 32px;
  }

  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }

  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
  }

  /* Slideshow responsive */
  .slideshowContainer {
    padding: 40px 10px 10px;
  }

  .closeButton {
    width: 40px;
    height: 40px;
    top: 15px;
    right: 15px;
  }

  .navButton {
    width: 50px;
    height: 50px;
  }

  .slideshowTitle {
    font-size: 20px;
  }

  .slideshowCounter {
    font-size: 14px;
  }

  .thumbnail {
    width: 50px;
    height: 50px;
  }

  .slideshowImageContainer {
    max-height: 60vh;
  }
}

@media (max-width: 480px) {
  .sectionTitle {
    font-size: 20px;
    margin-bottom: 20px;
  }

  .bentoGrid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(16, 60px);
    gap: 6px;
  }

  .bentoItem.hero {
    grid-column: span 2;
    grid-row: span 3;
  }

  .bentoItem.large {
    grid-column: span 2;
    grid-row: span 2;
  }

  .bentoItem.medium {
    grid-column: span 1;
    grid-row: span 2;
  }

  .bentoItem.small {
    grid-column: span 1;
    grid-row: span 1;
  }

  .gallerySection {
    padding: 30px 0;
  }

  .ctaSection {
    padding: 60px 0;
  }

  /* Slideshow mobile responsive */
  .slideshowContainer {
    padding: 30px 5px 5px;
  }

  .closeButton {
    width: 35px;
    height: 35px;
    top: 10px;
    right: 10px;
  }

  .navButton {
    width: 45px;
    height: 45px;
  }

  .slideshowTitle {
    font-size: 18px;
  }

  .slideshowCounter {
    font-size: 12px;
  }

  .thumbnail {
    width: 40px;
    height: 40px;
  }

  .slideshowImageContainer {
    max-height: 50vh;
    margin-bottom: 15px;
  }

  .slideshowInfo {
    margin-bottom: 15px;
  }

  .thumbnailContainer {
    gap: 6px;
    max-width: 95vw;
  }
}
