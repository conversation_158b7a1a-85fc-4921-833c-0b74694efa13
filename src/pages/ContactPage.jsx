import React, { useEffect } from 'react';
import styles from '../styles/pages/ContactPage.module.css';
import ContactHeroSection from '../components/ContactHeroSection';
import ContactSecondSection from '../components/ContactSecondSection';
import ContactFormSection from '../components/ContactFormSection';
import ContactPartnershipsSection from '../components/ContactPartnershipsSection';
import ContactLastSection from '../components/ContactLastSection';
import SEO from '../components/SEO';

const ContactPage = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Structured data for Contact page
  const contactStructuredData = {
    "@context": "https://schema.org",
    "@type": "ContactPage",
    "name": "Contact ResearchSat",
    "description": "Get in touch with ResearchSat for space biology research collaborations, mission planning, and partnership opportunities.",
    "url": "https://researchsat.space/contact",
    "mainEntity": {
      "@type": "Organization",
      "name": "ResearchSat",
      "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "email": "<EMAIL>",
        "availableLanguage": "English"
      }
    }
  };

  // Breadcrumbs for Contact page
  const breadcrumbs = [
    { name: "Home", url: "https://researchsat.space" },
    { name: "Contact", url: "https://researchsat.space/contact" }
  ];

  return (
    <>
      <SEO
        title="Contact Us - Space Biology Research Partnerships"
        description="Get in touch with ResearchSat for space biology research collaborations, custom mission planning, partnership opportunities, and microgravity experiment consultations."
        keywords={[
          'contact researchsat',
          'space research partnerships',
          'microgravity collaboration',
          'satellite mission planning',
          'space biology consulting',
          'research partnerships',
          'space technology contact',
          'orbital research collaboration'
        ]}
        canonical="https://researchsat.space/contact"
        structuredData={contactStructuredData}
        breadcrumbs={breadcrumbs}
        ogType="website"
      />

      <div className={styles.contactPage}>
        {/* Hero Section */}
      <ContactHeroSection />

      {/* Second Section */}
      <ContactSecondSection />

      {/* Contact Form Section */}
      <ContactFormSection />

      {/* Partnerships Section */}
      <ContactPartnershipsSection />

      {/* Contact Last Section */}
      <ContactLastSection />

        {/* Add margin above footer */}
        <div className={styles.footerMargin}></div>
      </div>
    </>
  );
};

export default ContactPage;
