import React, { useEffect } from 'react';
import SEO from '../components/SEO';
import CareersHeroSection from '../components/CareersHeroSection';
import CareersSecondSection from '../components/CareersSecondSection';
import CareersThirdSection from '../components/CareersThirdSection';
import CareersFourthSection from '../components/CareersFourthSection';
import styles from '../styles/pages/CareersPage.module.css';

const CareersPage = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Structured data for Careers page
  const careersStructuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Careers at ResearchSat",
    "description": "Join ResearchSat's innovative team and help advance space biology research and microgravity technology.",
    "url": "https://researchsat.space/careers",
    "mainEntity": {
      "@type": "Organization",
      "name": "ResearchSat",
      "hiringOrganization": {
        "@type": "Organization",
        "name": "ResearchSat",
        "sameAs": "https://researchsat.space"
      }
    }
  };

  // Breadcrumbs for Careers page
  const breadcrumbs = [
    { name: "Home", url: "https://researchsat.space" },
    { name: "Careers", url: "https://researchsat.space/careers" }
  ];

  return (
    <div className={styles.careersPage}>
      <SEO
        title="Careers - Join Our Space Biology Research Team"
        description="Join ResearchSat's innovative team and help advance space biology research, microgravity technology, and satellite mission development. Explore career opportunities."
        keywords={[
          'researchsat careers',
          'space research jobs',
          'microgravity technology careers',
          'satellite engineering jobs',
          'space biology careers',
          'aerospace engineering positions',
          'space technology jobs',
          'research scientist positions'
        ]}
        canonical="https://researchsat.space/careers"
        structuredData={careersStructuredData}
        breadcrumbs={breadcrumbs}
        ogType="website"
      />

      <CareersHeroSection />
      <CareersSecondSection />
      <CareersThirdSection />
      <CareersFourthSection />
    </div>
  );
};

export default CareersPage;
