import React, { useEffect } from 'react';

// Components
import Header from '../components/Header';
import SpaceSection from '../components/SpaceSection';
import BenefitsSection from '../components/BenefitsSection';
import FeaturesSection from '../components/FeaturesSection';
import PartnershipsSection from '../components/PartnershipsSection';
import PartnershipsSectionContainer from '../components/PartnershipsSectionContainer';
import NewsSection from '../components/NewsSection';
import BookMission from '../components/BookMission';
import SEO from '../components/SEO';

const HomePage = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Enhanced structured data for homepage
  const homepageStructuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "ResearchSat",
    "url": "https://researchsat.space",
    "description": "Leading provider of microgravity research solutions and space biology services",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://researchsat.space/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "ResearchSat",
      "logo": "https://researchsat.space/src/assets/images/new-logo.svg"
    }
  };

  // Breadcrumbs for homepage
  const breadcrumbs = [
    { name: "Home", url: "https://researchsat.space" }
  ];

  return (
    <>
      <SEO
        title="Space Biology & Microgravity Research Solutions"
        description="Unlock the potential of microgravity environments to advance life-science technologies and therapeutics with ResearchSat's custom satellite solutions and space research services."
        keywords={[
          'microgravity research',
          'space biology',
          'satellite payloads',
          'life sciences',
          'space medicine',
          'protein crystallization',
          'cell culture space',
          'ISS research',
          'orbital experiments',
          'space biotechnology'
        ]}
        canonical="https://researchsat.space"
        structuredData={homepageStructuredData}
        breadcrumbs={breadcrumbs}
        ogType="website"
      />
      {/* Header */}
      <Header />

      {/* Space Section */}
      <SpaceSection />

      {/* Benefits Section */}
      <BenefitsSection />

      {/* Features Section */}
      <FeaturesSection />

      {/* Partnerships Section */}
      <PartnershipsSection />

      {/* Partnership Opportunities Section */}
      <PartnershipsSectionContainer />

      {/* News Section */}
      <NewsSection />

      {/* Book a Mission Section */}
      <BookMission />
    </>
  );
};

export default HomePage;
