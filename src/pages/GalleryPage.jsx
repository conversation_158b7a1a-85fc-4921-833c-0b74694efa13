import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { Helmet } from 'react-helmet-async';
import SEO from '../components/SEO';
import OptimizedImage from '../components/OptimizedImage';
import { preloadCritical, preloadImages } from '../utils/imagePreloader';
import styles from '../styles/pages/GalleryPage.module.css';
import heroBackground from '../assets/images/gallery/galleryhr.jpeg';

// Import gallery images from gallery folder
import galimg001 from '../assets/images/gallery/galimg001.jpeg';
import galimg002 from '../assets/images/gallery/galimg002.jpg';
import galimg003 from '../assets/images/gallery/galimg003.jpg';
import galimg004 from '../assets/images/gallery/galimg004.jpg';
import galimg005 from '../assets/images/gallery/galimg005.jpg';
import galimg006 from '../assets/images/gallery/galimg006.png';
import galimg007 from '../assets/images/gallery/galimg007.png';
import galimg008 from '../assets/images/gallery/galimg008.JPG';
import galimg009 from '../assets/images/gallery/galimg009.png';
import figure001 from '../assets/images/gallery/Figure001.png';
import figure020 from '../assets/images/gallery/Figure020.png';
import titaniumBox from '../assets/images/gallery/Titanium_Box_croped.png';
import abhrMob from '../assets/images/gallery/abhrMob.png';
import serv1 from '../assets/images/gallery/serv1.jpg';
import img11 from '../assets/images/gallery/11.jpg';
import img20220919_190558 from '../assets/images/gallery/20220919_190558.jpg';
import img20220919_194504 from '../assets/images/gallery/20220919_194504.jpg';
import img20220920_001428 from '../assets/images/gallery/20220920_001428.jpg';
import img20220920_001539 from '../assets/images/gallery/20220920_001539.jpg';
import img20220920_001809 from '../assets/images/gallery/20220920_001809.jpg';
import img20220920_001901 from '../assets/images/gallery/20220920_001901.jpg';
import aicRstPayloads from '../assets/images/gallery/AIC & RST Payloads.jpg';
import cubesatGalaxy from '../assets/images/gallery/CubeSat_galaxyfixed.png';
import dsc00382 from '../assets/images/gallery/DSC00382.JPG';
import engineers from '../assets/images/gallery/Engineers.jpg';
import img0225 from '../assets/images/gallery/IMG_0225.jpg';
import img0762 from '../assets/images/gallery/IMG_0762.jpg';
import img0787 from '../assets/images/gallery/IMG_0787.jpg';
import img1517 from '../assets/images/gallery/IMG_1517.jpg';
import img1536 from '../assets/images/gallery/IMG_1536.jpg';
import img1556 from '../assets/images/gallery/IMG_1556.jpg';
import img2571 from '../assets/images/gallery/IMG_2571.jpg';
import img3282 from '../assets/images/gallery/IMG_3282.jpg';
import img3356 from '../assets/images/gallery/IMG_3356 (1).jpg';
import img4752 from '../assets/images/gallery/IMG_4752.jpg';
import img4837 from '../assets/images/gallery/IMG_4837.png';
import img4950 from '../assets/images/gallery/IMG_4950.jpg';
import img6948 from '../assets/images/gallery/IMG_6948 (1).jpg';
import img6949 from '../assets/images/gallery/IMG_6949.jpg';
import img6984 from '../assets/images/gallery/IMG_6984.jpg';
import image11022022 from '../assets/images/gallery/Image_ 11022022_19_19_16(1).jpg';
import image20221102 from '../assets/images/gallery/Image_20221102_232544_334.jpeg';
import image20221118 from '../assets/images/gallery/Image_20221118_145359_870 (4).jpeg';
import microfluidicChip from '../assets/images/gallery/Microfluidic chip flowrate experiment.jpg';
import missionDashboard from '../assets/images/gallery/Mission_Dashboard_Screenshot.jpg';
import p1 from '../assets/images/gallery/P1.jpg';
import p23 from '../assets/images/gallery/P23.jpg';
import p6 from '../assets/images/gallery/P6.jpg';
import suborbitalExpress from '../assets/images/gallery/SubOrbital-Express-3-launch.webp';
import v2 from '../assets/images/gallery/V 2.jpg';
import whatsapp1 from '../assets/images/gallery/WhatsApp Image 2023-12-08 at 16.52.08_e4be6a91.jpg';
import whatsapp2 from '../assets/images/gallery/WhatsApp Image 2023-12-08 at 16.53.24_6b5e22fa.jpg';
import whatsapp3 from '../assets/images/gallery/WhatsApp Image 2023-12-08 at 23.55.09_6e58b7b8.jpg';
import whatsapp4 from '../assets/images/gallery/WhatsApp Image 2024-02-25 at 15.08.07_a9e86f05.jpg';
import whatsapp5 from '../assets/images/gallery/WhatsApp Image 2024-02-25 at 15.08.34_531215bc.jpg';
import whatsapp6 from '../assets/images/gallery/WhatsApp Image 2024-02-25 at 15.08.44_27d716e0.jpg';
import whatsapp7 from '../assets/images/gallery/WhatsApp Image 2024-02-27 at 11.24.18_2185ba9e.jpg';
import whatsapp8 from '../assets/images/gallery/WhatsApp Image 2024-08-02 at 17.05.54_60d4f3d5.jpg';
import dsc7330 from '../assets/images/gallery/_DSC7330.JPG';
import dsc7331 from '../assets/images/gallery/_DSC7331.JPG';
import dsc7380 from '../assets/images/gallery/_DSC7380.JPG';
import image009 from '../assets/images/gallery/image009.png';
import image010 from '../assets/images/gallery/image010.jpg';

const GalleryPage = () => {
  const [isSlideshow, setIsSlideshow] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [allImages, setAllImages] = useState([]);
  const [imagesLoaded, setImagesLoaded] = useState(false);
  const [visibleSections, setVisibleSections] = useState(new Set(['mission'])); // Start with first section visible

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Structured data for Gallery page
  const galleryStructuredData = {
    "@context": "https://schema.org",
    "@type": "ImageGallery",
    "name": "ResearchSat Gallery - Space Biology Research Images",
    "description": "Explore our comprehensive gallery showcasing space missions, research team, payloads, and scientific activities in microgravity research.",
    "url": "https://researchsat.space/gallery",
    "publisher": {
      "@type": "Organization",
      "name": "ResearchSat"
    },
    "image": [
      "https://researchsat.space/src/assets/images/gallery/galleryhr.jpeg"
    ]
  };

  // Breadcrumbs for Gallery page
  const breadcrumbs = [
    { name: "Home", url: "https://researchsat.space" },
    { name: "Gallery", url: "https://researchsat.space/gallery" }
  ];

  // Memoized image sections for better performance
  const missionSection = useMemo(() => [
    { src: suborbitalExpress, alt: 'SubOrbital Express 3 Launch', position: 'hero', priority: true },
    { src: figure001, alt: 'Scientific Figure and Data Analysis', position: 'large', priority: true },
    { src: cubesatGalaxy, alt: 'CubeSat Galaxy Mission', position: 'medium', priority: false },
    { src: missionDashboard, alt: 'Mission Dashboard Screenshot', position: 'small', priority: false },
    { src: img20220920_001428, alt: 'Late Night Mission Operations', position: 'medium', priority: false },
    { src: figure020, alt: 'Mission Data and Results', position: 'small', priority: false },
    { src: img20220919_190558, alt: 'Mission Preparation - September 2022', position: 'medium', priority: false },
    { src: img20220920_001901, alt: 'Mission Monitoring', position: 'small', priority: false },
    { src: image11022022, alt: 'Mission Event - November 2022', position: 'large', priority: false },
    { src: whatsapp7, alt: 'Mission Planning - February 2024', position: 'small', priority: false }
  ], []);

  const teamSection = useMemo(() => [
    { src: engineers, alt: 'Engineering Team', position: 'hero', priority: false },
    { src: dsc7330, alt: 'Professional Photography Session', position: 'large', priority: false },
    { src: galimg008, alt: 'Team Collaboration Session', position: 'medium', priority: false },
    { src: dsc7380, alt: 'Professional Team Photography', position: 'medium', priority: false },
    { src: img20220919_194504, alt: 'Team Working Session', position: 'small', priority: false },
    { src: dsc7331, alt: 'Team Portrait Session', position: 'small', priority: false },
    { src: whatsapp3, alt: 'Late Night Work Session', position: 'large', priority: false },
    { src: image20221102, alt: 'Team Collaboration', position: 'small', priority: false },
    { src: whatsapp1, alt: 'Team Communication - December 2023', position: 'medium', priority: false },
    { src: img0225, alt: 'Team Member Portrait', position: 'small', priority: false },
    { src: img0762, alt: 'Team Working', position: 'small', priority: false },
    { src: img1517, alt: 'Team Discussion', position: 'medium', priority: false }
  ], []);

  const payloadSection = useMemo(() => [
    { src: aicRstPayloads, alt: 'AIC & RST Payloads', position: 'hero', priority: false },
    { src: titaniumBox, alt: 'Titanium Payload Container', position: 'large', priority: false },
    { src: microfluidicChip, alt: 'Microfluidic Chip Flowrate Experiment', position: 'large', priority: false },
    { src: abhrMob, alt: 'Mobile Research Platform', position: 'medium', priority: false },
    { src: v2, alt: 'Version 2 Development', position: 'medium', priority: false },
    { src: img20220920_001809, alt: 'Technical Equipment Check', position: 'small', priority: false },
    { src: whatsapp5, alt: 'Equipment Testing - February 2024', position: 'small', priority: false },
    { src: img0787, alt: 'Payload Development', position: 'medium', priority: false },
    { src: img1556, alt: 'Payload Testing', position: 'small', priority: false },
    { src: img2571, alt: 'Payload Assembly', position: 'small', priority: false }
  ], []);

  const activitySection = useMemo(() => [
    { src: galimg001, alt: 'Research Laboratory Setup', position: 'hero', priority: false },
    { src: galimg007, alt: 'Laboratory Research Process', position: 'large', priority: false },
    { src: image20221118, alt: 'Research Progress - November 2022', position: 'large', priority: false },
    { src: galimg006, alt: 'Space Research Equipment', position: 'medium', priority: false },
    { src: galimg009, alt: 'Advanced Research Facility', position: 'medium', priority: false },
    { src: img20220920_001539, alt: 'Mission Control Center', position: 'medium', priority: false },
    { src: p6, alt: 'Project Documentation P6', position: 'large', priority: false },
    { src: img4837, alt: 'Technical Documentation', position: 'medium', priority: false },
    { src: serv1, alt: 'Space Services and Operations', position: 'medium', priority: false },
    { src: img11, alt: 'Research Equipment Setup', position: 'small', priority: false },
    { src: dsc00382, alt: 'Professional Mission Photography', position: 'small', priority: false },
    { src: img6984, alt: 'Research Facility', position: 'small', priority: false },
    { src: p1, alt: 'Project Documentation P1', position: 'small', priority: false },
    { src: p23, alt: 'Project Documentation P23', position: 'small', priority: false },
    { src: whatsapp2, alt: 'Project Updates - December 2023', position: 'small', priority: false },
    { src: whatsapp4, alt: 'Research Progress - February 2024', position: 'medium', priority: false },
    { src: whatsapp6, alt: 'Technical Setup - February 2024', position: 'small', priority: false },
    { src: whatsapp8, alt: 'Recent Developments - August 2024', position: 'large', priority: false },
    { src: image009, alt: 'Technical Documentation Image', position: 'small', priority: false },
    { src: image010, alt: 'Research Documentation', position: 'medium', priority: false },
    { src: galimg002, alt: 'Laboratory Equipment', position: 'medium', priority: false },
    { src: galimg003, alt: 'Research Setup', position: 'small', priority: false },
    { src: galimg004, alt: 'Technical Work', position: 'small', priority: false },
    { src: galimg005, alt: 'Research Process', position: 'medium', priority: false },
    { src: img1536, alt: 'Research Activity', position: 'small', priority: false },
    { src: img3282, alt: 'Laboratory Work', position: 'small', priority: false },
    { src: img3356, alt: 'Technical Setup', position: 'medium', priority: false },
    { src: img4752, alt: 'Research Documentation', position: 'small', priority: false },
    { src: img4950, alt: 'Laboratory Process', position: 'medium', priority: false },
    { src: img6948, alt: 'Research Equipment', position: 'small', priority: false },
    { src: img6949, alt: 'Technical Work', position: 'small', priority: false }
  ], []);

  // Combine all images for slideshow with memoization
  const allImagesMemo = useMemo(() => [
    ...missionSection,
    ...teamSection,
    ...payloadSection,
    ...activitySection
  ], [missionSection, teamSection, payloadSection, activitySection]);

  useEffect(() => {
    setAllImages(allImagesMemo);
  }, [allImagesMemo]);

  // Preload critical images (hero and first visible images)
  useEffect(() => {
    const criticalImages = [
      heroBackground,
      ...missionSection.filter(img => img.priority).map(img => img.src)
    ];

    preloadCritical(criticalImages).then(() => {
      setImagesLoaded(true);
    });
  }, [missionSection]);

  // Intersection Observer for section visibility
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '100px 0px', // Start loading 100px before section enters viewport
      threshold: 0.1
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const sectionName = entry.target.dataset.section;
          if (sectionName && !visibleSections.has(sectionName)) {
            setVisibleSections(prev => new Set([...prev, sectionName]));

            // Preload images for this section
            let sectionImages = [];
            switch (sectionName) {
              case 'team':
                sectionImages = teamSection.map(img => img.src);
                break;
              case 'payload':
                sectionImages = payloadSection.map(img => img.src);
                break;
              case 'activity':
                sectionImages = activitySection.map(img => img.src);
                break;
            }

            if (sectionImages.length > 0) {
              preloadImages(sectionImages, { maxConcurrent: 2 });
            }
          }
        }
      });
    }, observerOptions);

    // Observe section elements
    const sections = document.querySelectorAll('[data-section]');
    sections.forEach(section => observer.observe(section));

    return () => observer.disconnect();
  }, [visibleSections, teamSection, payloadSection, activitySection]);

  // Slideshow functions
  const openSlideshow = useCallback((imageIndex, sectionImages, sectionStartIndex = 0) => {
    const globalIndex = sectionStartIndex + imageIndex;
    setCurrentImageIndex(globalIndex);
    setIsSlideshow(true);
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
  }, []);

  const closeSlideshow = useCallback(() => {
    setIsSlideshow(false);
    document.body.style.overflow = 'unset'; // Restore scrolling
  }, []);

  const nextImage = useCallback(() => {
    setCurrentImageIndex((prev) => (prev + 1) % allImages.length);
  }, [allImages.length]);

  const prevImage = useCallback(() => {
    setCurrentImageIndex((prev) => (prev - 1 + allImages.length) % allImages.length);
  }, [allImages.length]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (!isSlideshow) return;

      switch (e.key) {
        case 'ArrowRight':
          nextImage();
          break;
        case 'ArrowLeft':
          prevImage();
          break;
        case 'Escape':
          closeSlideshow();
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [isSlideshow, nextImage, prevImage, closeSlideshow]);

  // Helper function to get section start index
  const getSectionStartIndex = (sectionName) => {
    switch (sectionName) {
      case 'mission':
        return 0;
      case 'team':
        return missionSection.length;
      case 'payload':
        return missionSection.length + teamSection.length;
      case 'activity':
        return missionSection.length + teamSection.length + payloadSection.length;
      default:
        return 0;
    }
  };

  return (
    <>
      <SEO
        title="Gallery - Space Biology Research Images"
        description="Explore ResearchSat's comprehensive gallery showcasing space missions, research team, cutting-edge payloads, and groundbreaking microgravity research activities."
        keywords={[
          'space research gallery',
          'microgravity experiments photos',
          'satellite missions images',
          'space biology photos',
          'research team gallery',
          'space technology images',
          'orbital research pictures',
          'space laboratory photos'
        ]}
        canonical="https://researchsat.space/gallery"
        structuredData={galleryStructuredData}
        breadcrumbs={breadcrumbs}
        ogType="website"
        ogImage={heroBackground}
      />

      <div className={styles.galleryPage}>
        {/* Hero Section */}
        <section className={styles.heroSection}>
          {/* Hero Background */}
          <img
            src={heroBackground}
            alt="Space background"
            className={styles.heroBackground}
          />

          {/* Gradient Overlay */}
          <div className={styles.gradientOverlay}></div>

          {/* Hero Content */}
          <div className={styles.contentContainer}>
            <div className={styles.heroContent}>
              <div className={styles.galleryLabel}>_Gallery</div>
              <h1 className={styles.heroTitle}>
                Discover our world through images
              </h1>
            </div>
          </div>

          {/* Bottom Content */}
          <div className={styles.bottomContainer}>
            <div className={styles.descriptionContainer}>
              <p className={styles.descriptionText}>
                Explore our journey through space research, showcasing past missions, our dedicated team, and groundbreaking activities.
              </p>
              <div style={{ textAlign: 'right' }}>
                <a href="/book-mission" style={{ textDecoration: 'none' }}>
                  <span style={{
                    background: 'linear-gradient(180deg, #BBDED0 0%, #87C2AA 100%)',
                    WebkitBackgroundClip: 'text',
                    backgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    fontSize: '16px',
                    fontWeight: 500,
                    lineHeight: '120%',
                    letterSpacing: '0.25px',
                    fontFamily: 'Poppins, sans-serif'
                  }}>
                    ...explore our missions
                  </span>
                </a>
              </div>
            </div>
          </div>
        </section>

        {/* Mission Section */}
        <section className={styles.gallerySection} data-section="mission">
          <div className={styles.container}>
            <h2 className={styles.sectionTitle}>Space Missions</h2>
            <div className={styles.bentoGrid}>
              {missionSection.map((image, index) => (
                <div
                  key={`mission-${index}`}
                  className={`${styles.bentoItem} ${styles[image.position]}`}
                  onClick={() => openSlideshow(index, missionSection, getSectionStartIndex('mission'))}
                >
                  <OptimizedImage
                    src={image.src}
                    alt={image.alt}
                    className={styles.bentoImage}
                    loading={image.priority ? "eager" : "lazy"}
                    priority={image.priority}
                    sizes={
                      image.position === 'hero' ? '(max-width: 768px) 100vw, 50vw' :
                      image.position === 'large' ? '(max-width: 768px) 100vw, 33vw' :
                      image.position === 'medium' ? '(max-width: 768px) 50vw, 25vw' :
                      '(max-width: 768px) 50vw, 16vw'
                    }
                  />
                  <div className={styles.imageOverlay}>
                    <span className={styles.imageCategory}>Mission</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className={styles.gallerySection} data-section="team">
          <div className={styles.container}>
            <h2 className={styles.sectionTitle}>Our Team</h2>
            <div className={styles.bentoGrid}>
              {visibleSections.has('team') && teamSection.map((image, index) => (
                <div
                  key={`team-${index}`}
                  className={`${styles.bentoItem} ${styles[image.position]}`}
                  onClick={() => openSlideshow(index, teamSection, getSectionStartIndex('team'))}
                >
                  <OptimizedImage
                    src={image.src}
                    alt={image.alt}
                    className={styles.bentoImage}
                    loading="lazy"
                    sizes={
                      image.position === 'hero' ? '(max-width: 768px) 100vw, 50vw' :
                      image.position === 'large' ? '(max-width: 768px) 100vw, 33vw' :
                      image.position === 'medium' ? '(max-width: 768px) 50vw, 25vw' :
                      '(max-width: 768px) 50vw, 16vw'
                    }
                  />
                  <div className={styles.imageOverlay}>
                    <span className={styles.imageCategory}>Team</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Payload Section */}
        <section className={styles.gallerySection} data-section="payload">
          <div className={styles.container}>
            <h2 className={styles.sectionTitle}>Payloads & Equipment</h2>
            <div className={styles.bentoGrid}>
              {visibleSections.has('payload') && payloadSection.map((image, index) => (
                <div
                  key={`payload-${index}`}
                  className={`${styles.bentoItem} ${styles[image.position]}`}
                  onClick={() => openSlideshow(index, payloadSection, getSectionStartIndex('payload'))}
                >
                  <OptimizedImage
                    src={image.src}
                    alt={image.alt}
                    className={styles.bentoImage}
                    loading="lazy"
                    sizes={
                      image.position === 'hero' ? '(max-width: 768px) 100vw, 50vw' :
                      image.position === 'large' ? '(max-width: 768px) 100vw, 33vw' :
                      image.position === 'medium' ? '(max-width: 768px) 50vw, 25vw' :
                      '(max-width: 768px) 50vw, 16vw'
                    }
                  />
                  <div className={styles.imageOverlay}>
                    <span className={styles.imageCategory}>Payload</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Activity Section */}
        <section className={styles.gallerySection} data-section="activity">
          <div className={styles.container}>
            <h2 className={styles.sectionTitle}>Research Activities</h2>
            <div className={styles.bentoGrid}>
              {visibleSections.has('activity') && activitySection.map((image, index) => (
                <div
                  key={`activity-${index}`}
                  className={`${styles.bentoItem} ${styles[image.position]}`}
                  onClick={() => openSlideshow(index, activitySection, getSectionStartIndex('activity'))}
                >
                  <OptimizedImage
                    src={image.src}
                    alt={image.alt}
                    className={styles.bentoImage}
                    loading="lazy"
                    sizes={
                      image.position === 'hero' ? '(max-width: 768px) 100vw, 50vw' :
                      image.position === 'large' ? '(max-width: 768px) 100vw, 33vw' :
                      image.position === 'medium' ? '(max-width: 768px) 50vw, 25vw' :
                      '(max-width: 768px) 50vw, 16vw'
                    }
                  />
                  <div className={styles.imageOverlay}>
                    <span className={styles.imageCategory}>Activity</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className={styles.ctaSection}>
          <div className={styles.container}>
            <div className={styles.ctaContent}>
              <h2 className={styles.ctaTitle}>Ready to be part of our story?</h2>
              <p className={styles.ctaDescription}>
                Join us in advancing space research and be featured in our next gallery showcase.
              </p>
              <div className={styles.ctaButtons}>
                <a href="/book-mission" className={styles.primaryButton}>
                  Book Mission
                </a>
                <a href="/contact" className={styles.secondaryButton}>
                  Contact Us
                </a>
              </div>
            </div>
          </div>
        </section>

        {/* Add margin above footer */}
        <div className={styles.footerMargin}></div>

        {/* Fullscreen Slideshow */}
        {isSlideshow && allImages.length > 0 && (
          <div className={styles.slideshowOverlay} onClick={closeSlideshow}>
            <div className={styles.slideshowContainer} onClick={(e) => e.stopPropagation()}>
              {/* Close Button */}
              <button className={styles.closeButton} onClick={closeSlideshow}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>

              {/* Previous Button */}
              <button className={styles.navButton} onClick={prevImage} style={{ left: '20px' }}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>

              {/* Next Button */}
              <button className={styles.navButton} onClick={nextImage} style={{ right: '20px' }}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>

              {/* Current Image */}
              <div className={styles.slideshowImageContainer}>
                <OptimizedImage
                  src={allImages[currentImageIndex]?.src}
                  alt={allImages[currentImageIndex]?.alt}
                  className={styles.slideshowImage}
                  loading="eager"
                  priority={true}
                  sizes="90vw"
                  placeholder={false}
                />
              </div>

              {/* Image Info */}
              <div className={styles.slideshowInfo}>
                <h3 className={styles.slideshowTitle}>{allImages[currentImageIndex]?.alt}</h3>
                <p className={styles.slideshowCounter}>
                  {currentImageIndex + 1} of {allImages.length}
                </p>
              </div>

              {/* Thumbnail Navigation */}
              <div className={styles.thumbnailContainer}>
                {allImages.map((image, index) => (
                  <div
                    key={index}
                    className={`${styles.thumbnail} ${index === currentImageIndex ? styles.activeThumbnail : ''}`}
                    onClick={() => setCurrentImageIndex(index)}
                  >
                    <img src={image.src} alt={image.alt} />
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default GalleryPage;
