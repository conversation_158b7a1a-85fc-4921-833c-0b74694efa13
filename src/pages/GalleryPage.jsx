import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import styles from '../styles/pages/GalleryPage.module.css';
import heroBackground from '../assets/images/gallery/galleryhr.jpeg';

// Import gallery images from gallery folder
import galimg001 from '../assets/images/gallery/galimg001.jpeg';
import galimg006 from '../assets/images/gallery/galimg006.png';
import galimg007 from '../assets/images/gallery/galimg007.png';
import galimg008 from '../assets/images/gallery/galimg008.JPG';
import galimg009 from '../assets/images/gallery/galimg009.png';
import figure001 from '../assets/images/gallery/Figure001.png';
import figure020 from '../assets/images/gallery/Figure020.png';
import titaniumBox from '../assets/images/gallery/Titanium_Box_croped.png';
import abhrMob from '../assets/images/gallery/abhrMob.png';
import serv1 from '../assets/images/gallery/serv1.jpg';
import img11 from '../assets/images/gallery/11.jpg';
import img20220919_190558 from '../assets/images/gallery/20220919_190558.jpg';
import img20220919_194504 from '../assets/images/gallery/20220919_194504.jpg';
import img20220920_001428 from '../assets/images/gallery/20220920_001428.jpg';
import img20220920_001539 from '../assets/images/gallery/20220920_001539.jpg';
import img20220920_001809 from '../assets/images/gallery/20220920_001809.jpg';
import img20220920_001901 from '../assets/images/gallery/20220920_001901.jpg';
import aicRstPayloads from '../assets/images/gallery/AIC & RST Payloads.jpg';
import cubesatGalaxy from '../assets/images/gallery/CubeSat_galaxyfixed.png';
import dsc00382 from '../assets/images/gallery/DSC00382.JPG';
import engineers from '../assets/images/gallery/Engineers.jpg';
import img4837 from '../assets/images/gallery/IMG_4837.png';
import img6984 from '../assets/images/gallery/IMG_6984.jpg';
import image11022022 from '../assets/images/gallery/Image_ 11022022_19_19_16(1).jpg';
import image20221102 from '../assets/images/gallery/Image_20221102_232544_334.jpeg';
import image20221118 from '../assets/images/gallery/Image_20221118_145359_870 (4).jpeg';
import microfluidicChip from '../assets/images/gallery/Microfluidic chip flowrate experiment.jpg';
import missionDashboard from '../assets/images/gallery/Mission_Dashboard_Screenshot.jpg';
import p1 from '../assets/images/gallery/P1.jpg';
import p23 from '../assets/images/gallery/P23.jpg';
import p6 from '../assets/images/gallery/P6.jpg';
import suborbitalExpress from '../assets/images/gallery/SubOrbital-Express-3-launch.webp';
import v2 from '../assets/images/gallery/V 2.jpg';
import whatsapp1 from '../assets/images/gallery/WhatsApp Image 2023-12-08 at 16.52.08_e4be6a91.jpg';
import whatsapp2 from '../assets/images/gallery/WhatsApp Image 2023-12-08 at 16.53.24_6b5e22fa.jpg';
import whatsapp3 from '../assets/images/gallery/WhatsApp Image 2023-12-08 at 23.55.09_6e58b7b8.jpg';
import whatsapp4 from '../assets/images/gallery/WhatsApp Image 2024-02-25 at 15.08.07_a9e86f05.jpg';
import whatsapp5 from '../assets/images/gallery/WhatsApp Image 2024-02-25 at 15.08.34_531215bc.jpg';
import whatsapp6 from '../assets/images/gallery/WhatsApp Image 2024-02-25 at 15.08.44_27d716e0.jpg';
import whatsapp7 from '../assets/images/gallery/WhatsApp Image 2024-02-27 at 11.24.18_2185ba9e.jpg';
import whatsapp8 from '../assets/images/gallery/WhatsApp Image 2024-08-02 at 17.05.54_60d4f3d5.jpg';
import dsc7330 from '../assets/images/gallery/_DSC7330.JPG';
import dsc7331 from '../assets/images/gallery/_DSC7331.JPG';
import dsc7380 from '../assets/images/gallery/_DSC7380.JPG';
import image009 from '../assets/images/gallery/image009.png';
import image010 from '../assets/images/gallery/image010.jpg';

const GalleryPage = () => {
  useEffect(() => {
    document.title = 'Gallery | ResearchSat';
    window.scrollTo(0, 0);
  }, []);

  const galleryImages = [
    {
      id: 1,
      src: galimg001,
      alt: 'Research Laboratory Setup',
      category: 'activity',
      size: 'large'
    },
    {
      id: 2,
      src: figure001,
      alt: 'Scientific Figure and Data Analysis',
      category: 'mission',
      size: 'medium'
    },
    {
      id: 3,
      src: titaniumBox,
      alt: 'Titanium Payload Container',
      category: 'payload',
      size: 'small'
    },
    {
      id: 4,
      src: galimg006,
      alt: 'Space Research Equipment',
      category: 'activity',
      size: 'medium'
    },
    {
      id: 5,
      src: figure020,
      alt: 'Mission Data and Results',
      category: 'mission',
      size: 'small'
    },
    {
      id: 6,
      src: galimg007,
      alt: 'Laboratory Research Process',
      category: 'activity',
      size: 'large'
    },
    {
      id: 7,
      src: galimg008,
      alt: 'Team Collaboration Session',
      category: 'team',
      size: 'medium'
    },
    {
      id: 8,
      src: abhrMob,
      alt: 'Mobile Research Platform',
      category: 'payload',
      size: 'small'
    },
    {
      id: 9,
      src: galimg009,
      alt: 'Advanced Research Facility',
      category: 'activity',
      size: 'medium'
    },
    {
      id: 10,
      src: serv1,
      alt: 'Space Services and Operations',
      category: 'mission',
      size: 'large'
    },
    {
      id: 11,
      src: img11,
      alt: 'Research Equipment Setup',
      category: 'activity',
      size: 'small'
    },
    {
      id: 12,
      src: img20220919_190558,
      alt: 'Mission Preparation - September 2022',
      category: 'mission',
      size: 'medium'
    },
    {
      id: 13,
      src: img20220919_194504,
      alt: 'Team Working Session',
      category: 'team',
      size: 'small'
    },
    {
      id: 14,
      src: img20220920_001428,
      alt: 'Late Night Mission Operations',
      category: 'mission',
      size: 'large'
    },
    {
      id: 15,
      src: img20220920_001539,
      alt: 'Mission Control Center',
      category: 'activity',
      size: 'medium'
    },
    {
      id: 16,
      src: img20220920_001809,
      alt: 'Technical Equipment Check',
      category: 'payload',
      size: 'small'
    },
    {
      id: 17,
      src: img20220920_001901,
      alt: 'Mission Monitoring',
      category: 'mission',
      size: 'medium'
    },
    {
      id: 18,
      src: aicRstPayloads,
      alt: 'AIC & RST Payloads',
      category: 'payload',
      size: 'large'
    },
    {
      id: 19,
      src: cubesatGalaxy,
      alt: 'CubeSat Galaxy Mission',
      category: 'mission',
      size: 'medium'
    },
    {
      id: 20,
      src: dsc00382,
      alt: 'Professional Mission Photography',
      category: 'activity',
      size: 'small'
    },
    {
      id: 21,
      src: engineers,
      alt: 'Engineering Team',
      category: 'team',
      size: 'large'
    },
    {
      id: 22,
      src: img4837,
      alt: 'Technical Documentation',
      category: 'activity',
      size: 'medium'
    },
    {
      id: 23,
      src: img6984,
      alt: 'Research Facility',
      category: 'activity',
      size: 'small'
    },
    {
      id: 24,
      src: image11022022,
      alt: 'Mission Event - November 2022',
      category: 'mission',
      size: 'medium'
    },
    {
      id: 25,
      src: image20221102,
      alt: 'Team Collaboration',
      category: 'team',
      size: 'small'
    },
    {
      id: 26,
      src: image20221118,
      alt: 'Research Progress - November 2022',
      category: 'activity',
      size: 'large'
    },
    {
      id: 27,
      src: microfluidicChip,
      alt: 'Microfluidic Chip Flowrate Experiment',
      category: 'payload',
      size: 'medium'
    },
    {
      id: 28,
      src: missionDashboard,
      alt: 'Mission Dashboard Screenshot',
      category: 'mission',
      size: 'small'
    },
    {
      id: 29,
      src: p1,
      alt: 'Project Documentation P1',
      category: 'activity',
      size: 'medium'
    },
    {
      id: 30,
      src: p23,
      alt: 'Project Documentation P23',
      category: 'activity',
      size: 'small'
    },
    {
      id: 31,
      src: p6,
      alt: 'Project Documentation P6',
      category: 'activity',
      size: 'large'
    },
    {
      id: 32,
      src: suborbitalExpress,
      alt: 'SubOrbital Express 3 Launch',
      category: 'mission',
      size: 'medium'
    },
    {
      id: 33,
      src: v2,
      alt: 'Version 2 Development',
      category: 'payload',
      size: 'small'
    },
    {
      id: 34,
      src: whatsapp1,
      alt: 'Team Communication - December 2023',
      category: 'team',
      size: 'medium'
    },
    {
      id: 35,
      src: whatsapp2,
      alt: 'Project Updates - December 2023',
      category: 'activity',
      size: 'small'
    },
    {
      id: 36,
      src: whatsapp3,
      alt: 'Late Night Work Session',
      category: 'team',
      size: 'large'
    },
    {
      id: 37,
      src: whatsapp4,
      alt: 'Research Progress - February 2024',
      category: 'activity',
      size: 'medium'
    },
    {
      id: 38,
      src: whatsapp5,
      alt: 'Equipment Testing - February 2024',
      category: 'payload',
      size: 'small'
    },
    {
      id: 39,
      src: whatsapp6,
      alt: 'Technical Setup - February 2024',
      category: 'activity',
      size: 'medium'
    },
    {
      id: 40,
      src: whatsapp7,
      alt: 'Mission Planning - February 2024',
      category: 'mission',
      size: 'small'
    },
    {
      id: 41,
      src: whatsapp8,
      alt: 'Recent Developments - August 2024',
      category: 'activity',
      size: 'large'
    },
    {
      id: 42,
      src: dsc7330,
      alt: 'Professional Photography Session',
      category: 'team',
      size: 'medium'
    },
    {
      id: 43,
      src: dsc7331,
      alt: 'Team Portrait Session',
      category: 'team',
      size: 'small'
    },
    {
      id: 44,
      src: dsc7380,
      alt: 'Professional Team Photography',
      category: 'team',
      size: 'medium'
    },
    {
      id: 45,
      src: image009,
      alt: 'Technical Documentation Image',
      category: 'activity',
      size: 'small'
    },
    {
      id: 46,
      src: image010,
      alt: 'Research Documentation',
      category: 'activity',
      size: 'large'
    }
  ];

  return (
    <>
      <Helmet>
        <title>Gallery | ResearchSat</title>
        <meta name="description" content="Explore our gallery showcasing past missions, team members, and research activities at ResearchSat." />
        <meta name="keywords" content="gallery, space missions, team, research activities, ResearchSat photos" />
        <meta property="og:title" content="Gallery | ResearchSat" />
        <meta property="og:description" content="Explore our gallery showcasing past missions, team members, and research activities at ResearchSat." />
        <meta property="og:image" content="/images/gallery/galleryhr.jpeg" />
        <meta property="og:url" content="/gallery" />
        <meta name="twitter:card" content="summary_large_image" />
      </Helmet>

      <div className={styles.galleryPage}>
        {/* Hero Section */}
        <section className={styles.heroSection}>
          {/* Hero Background */}
          <img
            src={heroBackground}
            alt="Gallery hero background"
            className={styles.heroBackground}
          />

          {/* Gradient Overlay */}
          <div className={styles.gradientOverlay}></div>

          {/* Hero Content */}
          <div className={styles.contentContainer}>
            <div className={styles.heroContent}>
              <div className={styles.galleryLabel}>_Gallery</div>
              <h1 className={styles.heroTitle}>
                Discover our world through images
              </h1>
              <p className={styles.heroSubtitle}>
                Explore our journey through space research, showcasing past missions, our dedicated team, and groundbreaking activities.
              </p>
            </div>
          </div>
        </section>

        {/* Gallery Grid Section */}
        <section className={styles.gallerySection}>
          <div className={styles.container}>
            <div className={styles.galleryGrid}>
              {galleryImages.map((image) => (
                <div
                  key={image.id}
                  className={`${styles.galleryItem} ${styles[image.size]} ${styles[image.category]}`}
                >
                  <img
                    src={image.src}
                    alt={image.alt}
                    className={styles.galleryImage}
                    loading="lazy"
                  />
                  <div className={styles.imageOverlay}>
                    <span className={styles.imageCategory}>
                      {image.category.charAt(0).toUpperCase() + image.category.slice(1)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className={styles.ctaSection}>
          <div className={styles.container}>
            <div className={styles.ctaContent}>
              <h2 className={styles.ctaTitle}>Ready to be part of our story?</h2>
              <p className={styles.ctaDescription}>
                Join us in advancing space research and be featured in our next gallery showcase.
              </p>
              <div className={styles.ctaButtons}>
                <a href="/book-mission" className={styles.primaryButton}>
                  Book Mission
                </a>
                <a href="/contact" className={styles.secondaryButton}>
                  Contact Us
                </a>
              </div>
            </div>
          </div>
        </section>

        {/* Add margin above footer */}
        <div className={styles.footerMargin}></div>
      </div>
    </>
  );
};

export default GalleryPage;
