import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import styles from '../styles/pages/GalleryPage.module.css';
import heroBackground from '../assets/images/galleryhr.jpeg';

// Import gallery images
import mission1 from '../assets/images/mission_1.jpg';
import mission2 from '../assets/images/mission_2.jpg';
import mission3 from '../assets/images/mission_3.jpg';
import mission4 from '../assets/images/mission_4.jpg';
import payload1 from '../assets/images/payload_1.jpg';
import payload2 from '../assets/images/Payload_2.jpg';
import payload3 from '../assets/images/Payload_3.jpg';
import payload4 from '../assets/images/payload_4.jpg';
import teamRavi from '../assets/images/about/team/Ravi.jpg';
import teamShrusthi from '../assets/images/about/team/shrusthi.png';
import teamJibin from '../assets/images/about/team/jibin.png';
import teamTannu from '../assets/images/about/team/tannu.jpg';
import teamSaki from '../assets/images/about/team/saki.jpg';
import teamAdeel from '../assets/images/about/team/adeel.png';
import abj1 from '../assets/images/abj1.jpg';
import abj3 from '../assets/images/abj3.jpg';
import abthr1 from '../assets/images/abthr1.jpg';
import svhr1 from '../assets/images/svhr1.jpg';
import svr2 from '../assets/images/svr2.jpg';
import serv1 from '../assets/images/serv1.jpg';

const GalleryPage = () => {
  useEffect(() => {
    document.title = 'Gallery | ResearchSat';
    window.scrollTo(0, 0);
  }, []);

  const galleryImages = [
    {
      id: 1,
      src: mission1,
      alt: 'Space Mission 1',
      category: 'mission',
      size: 'large'
    },
    {
      id: 2,
      src: teamRavi,
      alt: 'Team Member - Ravi',
      category: 'team',
      size: 'medium'
    },
    {
      id: 3,
      src: payload1,
      alt: 'Payload Research',
      category: 'payload',
      size: 'small'
    },
    {
      id: 4,
      src: mission2,
      alt: 'Space Mission 2',
      category: 'mission',
      size: 'medium'
    },
    {
      id: 5,
      src: teamShrusthi,
      alt: 'Team Member - Shrusthi',
      category: 'team',
      size: 'small'
    },
    {
      id: 6,
      src: abj1,
      alt: 'Research Activity',
      category: 'activity',
      size: 'large'
    },
    {
      id: 7,
      src: payload2,
      alt: 'Payload Development',
      category: 'payload',
      size: 'medium'
    },
    {
      id: 8,
      src: teamJibin,
      alt: 'Team Member - Jibin',
      category: 'team',
      size: 'small'
    },
    {
      id: 9,
      src: mission3,
      alt: 'Space Mission 3',
      category: 'mission',
      size: 'medium'
    },
    {
      id: 10,
      src: svhr1,
      alt: 'Space Research',
      category: 'activity',
      size: 'large'
    },
    {
      id: 11,
      src: teamTannu,
      alt: 'Team Member - Tannu',
      category: 'team',
      size: 'small'
    },
    {
      id: 12,
      src: payload3,
      alt: 'Payload Testing',
      category: 'payload',
      size: 'medium'
    },
    {
      id: 13,
      src: mission4,
      alt: 'Space Mission 4',
      category: 'mission',
      size: 'small'
    },
    {
      id: 14,
      src: abj3,
      alt: 'Laboratory Work',
      category: 'activity',
      size: 'medium'
    },
    {
      id: 15,
      src: teamSaki,
      alt: 'Team Member - Saki',
      category: 'team',
      size: 'small'
    },
    {
      id: 16,
      src: svr2,
      alt: 'Space Technology',
      category: 'activity',
      size: 'large'
    },
    {
      id: 17,
      src: payload4,
      alt: 'Payload Integration',
      category: 'payload',
      size: 'medium'
    },
    {
      id: 18,
      src: teamAdeel,
      alt: 'Team Member - Adeel',
      category: 'team',
      size: 'small'
    },
    {
      id: 19,
      src: abthr1,
      alt: 'Research Facility',
      category: 'activity',
      size: 'medium'
    },
    {
      id: 20,
      src: serv1,
      alt: 'Space Services',
      category: 'activity',
      size: 'small'
    }
  ];

  return (
    <>
      <Helmet>
        <title>Gallery | ResearchSat</title>
        <meta name="description" content="Explore our gallery showcasing past missions, team members, and research activities at ResearchSat." />
        <meta name="keywords" content="gallery, space missions, team, research activities, ResearchSat photos" />
        <meta property="og:title" content="Gallery | ResearchSat" />
        <meta property="og:description" content="Explore our gallery showcasing past missions, team members, and research activities at ResearchSat." />
        <meta property="og:image" content="/images/galleryhr.jpeg" />
        <meta property="og:url" content="/gallery" />
        <meta name="twitter:card" content="summary_large_image" />
      </Helmet>

      <div className={styles.galleryPage}>
        {/* Hero Section */}
        <section className={styles.heroSection}>
          {/* Hero Background */}
          <img
            src={heroBackground}
            alt="Gallery hero background"
            className={styles.heroBackground}
          />

          {/* Gradient Overlay */}
          <div className={styles.gradientOverlay}></div>

          {/* Hero Content */}
          <div className={styles.contentContainer}>
            <div className={styles.heroContent}>
              <div className={styles.galleryLabel}>_Gallery</div>
              <h1 className={styles.heroTitle}>
                Discover our world through images
              </h1>
              <p className={styles.heroSubtitle}>
                Explore our journey through space research, showcasing past missions, our dedicated team, and groundbreaking activities.
              </p>
            </div>
          </div>
        </section>

        {/* Gallery Grid Section */}
        <section className={styles.gallerySection}>
          <div className={styles.container}>
            <div className={styles.galleryGrid}>
              {galleryImages.map((image) => (
                <div
                  key={image.id}
                  className={`${styles.galleryItem} ${styles[image.size]} ${styles[image.category]}`}
                >
                  <img
                    src={image.src}
                    alt={image.alt}
                    className={styles.galleryImage}
                    loading="lazy"
                  />
                  <div className={styles.imageOverlay}>
                    <span className={styles.imageCategory}>
                      {image.category.charAt(0).toUpperCase() + image.category.slice(1)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className={styles.ctaSection}>
          <div className={styles.container}>
            <div className={styles.ctaContent}>
              <h2 className={styles.ctaTitle}>Ready to be part of our story?</h2>
              <p className={styles.ctaDescription}>
                Join us in advancing space research and be featured in our next gallery showcase.
              </p>
              <div className={styles.ctaButtons}>
                <a href="/book-mission" className={styles.primaryButton}>
                  Book Mission
                </a>
                <a href="/contact" className={styles.secondaryButton}>
                  Contact Us
                </a>
              </div>
            </div>
          </div>
        </section>

        {/* Add margin above footer */}
        <div className={styles.footerMargin}></div>
      </div>
    </>
  );
};

export default GalleryPage;
