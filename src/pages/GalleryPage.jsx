import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import styles from '../styles/pages/GalleryPage.module.css';
import heroBackground from '../assets/images/gallery/galleryhr.jpeg';

// Import gallery images from gallery folder
import galimg001 from '../assets/images/gallery/galimg001.jpeg';
import galimg002 from '../assets/images/gallery/galimg002.jpg';
import galimg003 from '../assets/images/gallery/galimg003.jpg';
import galimg004 from '../assets/images/gallery/galimg004.jpg';
import galimg005 from '../assets/images/gallery/galimg005.jpg';
import galimg006 from '../assets/images/gallery/galimg006.png';
import galimg007 from '../assets/images/gallery/galimg007.png';
import galimg008 from '../assets/images/gallery/galimg008.JPG';
import galimg009 from '../assets/images/gallery/galimg009.png';
import figure001 from '../assets/images/gallery/Figure001.png';
import figure020 from '../assets/images/gallery/Figure020.png';
import titaniumBox from '../assets/images/gallery/Titanium_Box_croped.png';
import abhrMob from '../assets/images/gallery/abhrMob.png';
import serv1 from '../assets/images/gallery/serv1.jpg';
import img11 from '../assets/images/gallery/11.jpg';
import img20220919_190558 from '../assets/images/gallery/20220919_190558.jpg';
import img20220919_194504 from '../assets/images/gallery/20220919_194504.jpg';
import img20220920_001428 from '../assets/images/gallery/20220920_001428.jpg';
import img20220920_001539 from '../assets/images/gallery/20220920_001539.jpg';
import img20220920_001809 from '../assets/images/gallery/20220920_001809.jpg';
import img20220920_001901 from '../assets/images/gallery/20220920_001901.jpg';
import aicRstPayloads from '../assets/images/gallery/AIC & RST Payloads.jpg';
import cubesatGalaxy from '../assets/images/gallery/CubeSat_galaxyfixed.png';
import dsc00382 from '../assets/images/gallery/DSC00382.JPG';
import engineers from '../assets/images/gallery/Engineers.jpg';
import img0225 from '../assets/images/gallery/IMG_0225.jpg';
import img0762 from '../assets/images/gallery/IMG_0762.jpg';
import img0787 from '../assets/images/gallery/IMG_0787.jpg';
import img1517 from '../assets/images/gallery/IMG_1517.jpg';
import img1536 from '../assets/images/gallery/IMG_1536.jpg';
import img1556 from '../assets/images/gallery/IMG_1556.jpg';
import img2571 from '../assets/images/gallery/IMG_2571.jpg';
import img3282 from '../assets/images/gallery/IMG_3282.jpg';
import img3356 from '../assets/images/gallery/IMG_3356 (1).jpg';
import img4752 from '../assets/images/gallery/IMG_4752.jpg';
import img4837 from '../assets/images/gallery/IMG_4837.png';
import img4950 from '../assets/images/gallery/IMG_4950.jpg';
import img6948 from '../assets/images/gallery/IMG_6948 (1).jpg';
import img6949 from '../assets/images/gallery/IMG_6949.jpg';
import img6984 from '../assets/images/gallery/IMG_6984.jpg';
import image11022022 from '../assets/images/gallery/Image_ 11022022_19_19_16(1).jpg';
import image20221102 from '../assets/images/gallery/Image_20221102_232544_334.jpeg';
import image20221118 from '../assets/images/gallery/Image_20221118_145359_870 (4).jpeg';
import microfluidicChip from '../assets/images/gallery/Microfluidic chip flowrate experiment.jpg';
import missionDashboard from '../assets/images/gallery/Mission_Dashboard_Screenshot.jpg';
import p1 from '../assets/images/gallery/P1.jpg';
import p23 from '../assets/images/gallery/P23.jpg';
import p6 from '../assets/images/gallery/P6.jpg';
import suborbitalExpress from '../assets/images/gallery/SubOrbital-Express-3-launch.webp';
import v2 from '../assets/images/gallery/V 2.jpg';
import whatsapp1 from '../assets/images/gallery/WhatsApp Image 2023-12-08 at 16.52.08_e4be6a91.jpg';
import whatsapp2 from '../assets/images/gallery/WhatsApp Image 2023-12-08 at 16.53.24_6b5e22fa.jpg';
import whatsapp3 from '../assets/images/gallery/WhatsApp Image 2023-12-08 at 23.55.09_6e58b7b8.jpg';
import whatsapp4 from '../assets/images/gallery/WhatsApp Image 2024-02-25 at 15.08.07_a9e86f05.jpg';
import whatsapp5 from '../assets/images/gallery/WhatsApp Image 2024-02-25 at 15.08.34_531215bc.jpg';
import whatsapp6 from '../assets/images/gallery/WhatsApp Image 2024-02-25 at 15.08.44_27d716e0.jpg';
import whatsapp7 from '../assets/images/gallery/WhatsApp Image 2024-02-27 at 11.24.18_2185ba9e.jpg';
import whatsapp8 from '../assets/images/gallery/WhatsApp Image 2024-08-02 at 17.05.54_60d4f3d5.jpg';
import dsc7330 from '../assets/images/gallery/_DSC7330.JPG';
import dsc7331 from '../assets/images/gallery/_DSC7331.JPG';
import dsc7380 from '../assets/images/gallery/_DSC7380.JPG';
import image009 from '../assets/images/gallery/image009.png';
import image010 from '../assets/images/gallery/image010.jpg';

const GalleryPage = () => {
  useEffect(() => {
    document.title = 'Gallery | ResearchSat';
    window.scrollTo(0, 0);
  }, []);

  // Organize images into sections for bento-grid layout
  const missionSection = [
    { src: suborbitalExpress, alt: 'SubOrbital Express 3 Launch', position: 'hero' },
    { src: figure001, alt: 'Scientific Figure and Data Analysis', position: 'large' },
    { src: cubesatGalaxy, alt: 'CubeSat Galaxy Mission', position: 'medium' },
    { src: missionDashboard, alt: 'Mission Dashboard Screenshot', position: 'small' },
    { src: img20220920_001428, alt: 'Late Night Mission Operations', position: 'medium' },
    { src: figure020, alt: 'Mission Data and Results', position: 'small' },
    { src: img20220919_190558, alt: 'Mission Preparation - September 2022', position: 'medium' },
    { src: img20220920_001901, alt: 'Mission Monitoring', position: 'small' },
    { src: image11022022, alt: 'Mission Event - November 2022', position: 'large' },
    { src: whatsapp7, alt: 'Mission Planning - February 2024', position: 'small' }
  ];

  const teamSection = [
    { src: engineers, alt: 'Engineering Team', position: 'hero' },
    { src: dsc7330, alt: 'Professional Photography Session', position: 'large' },
    { src: galimg008, alt: 'Team Collaboration Session', position: 'medium' },
    { src: dsc7380, alt: 'Professional Team Photography', position: 'medium' },
    { src: img20220919_194504, alt: 'Team Working Session', position: 'small' },
    { src: dsc7331, alt: 'Team Portrait Session', position: 'small' },
    { src: whatsapp3, alt: 'Late Night Work Session', position: 'large' },
    { src: image20221102, alt: 'Team Collaboration', position: 'small' },
    { src: whatsapp1, alt: 'Team Communication - December 2023', position: 'medium' },
    { src: img0225, alt: 'Team Member Portrait', position: 'small' },
    { src: img0762, alt: 'Team Working', position: 'small' },
    { src: img1517, alt: 'Team Discussion', position: 'medium' }
  ];

  const payloadSection = [
    { src: aicRstPayloads, alt: 'AIC & RST Payloads', position: 'hero' },
    { src: titaniumBox, alt: 'Titanium Payload Container', position: 'large' },
    { src: microfluidicChip, alt: 'Microfluidic Chip Flowrate Experiment', position: 'large' },
    { src: abhrMob, alt: 'Mobile Research Platform', position: 'medium' },
    { src: v2, alt: 'Version 2 Development', position: 'medium' },
    { src: img20220920_001809, alt: 'Technical Equipment Check', position: 'small' },
    { src: whatsapp5, alt: 'Equipment Testing - February 2024', position: 'small' },
    { src: img0787, alt: 'Payload Development', position: 'medium' },
    { src: img1556, alt: 'Payload Testing', position: 'small' },
    { src: img2571, alt: 'Payload Assembly', position: 'small' }
  ];

  const activitySection = [
    { src: galimg001, alt: 'Research Laboratory Setup', position: 'hero' },
    { src: galimg007, alt: 'Laboratory Research Process', position: 'large' },
    { src: image20221118, alt: 'Research Progress - November 2022', position: 'large' },
    { src: galimg006, alt: 'Space Research Equipment', position: 'medium' },
    { src: galimg009, alt: 'Advanced Research Facility', position: 'medium' },
    { src: img20220920_001539, alt: 'Mission Control Center', position: 'medium' },
    { src: p6, alt: 'Project Documentation P6', position: 'large' },
    { src: img4837, alt: 'Technical Documentation', position: 'medium' },
    { src: serv1, alt: 'Space Services and Operations', position: 'medium' },
    { src: img11, alt: 'Research Equipment Setup', position: 'small' },
    { src: dsc00382, alt: 'Professional Mission Photography', position: 'small' },
    { src: img6984, alt: 'Research Facility', position: 'small' },
    { src: p1, alt: 'Project Documentation P1', position: 'small' },
    { src: p23, alt: 'Project Documentation P23', position: 'small' },
    { src: whatsapp2, alt: 'Project Updates - December 2023', position: 'small' },
    { src: whatsapp4, alt: 'Research Progress - February 2024', position: 'medium' },
    { src: whatsapp6, alt: 'Technical Setup - February 2024', position: 'small' },
    { src: whatsapp8, alt: 'Recent Developments - August 2024', position: 'large' },
    { src: image009, alt: 'Technical Documentation Image', position: 'small' },
    { src: image010, alt: 'Research Documentation', position: 'medium' },
    { src: galimg002, alt: 'Laboratory Equipment', position: 'medium' },
    { src: galimg003, alt: 'Research Setup', position: 'small' },
    { src: galimg004, alt: 'Technical Work', position: 'small' },
    { src: galimg005, alt: 'Research Process', position: 'medium' },
    { src: img1536, alt: 'Research Activity', position: 'small' },
    { src: img3282, alt: 'Laboratory Work', position: 'small' },
    { src: img3356, alt: 'Technical Setup', position: 'medium' },
    { src: img4752, alt: 'Research Documentation', position: 'small' },
    { src: img4950, alt: 'Laboratory Process', position: 'medium' },
    { src: img6948, alt: 'Research Equipment', position: 'small' },
    { src: img6949, alt: 'Technical Work', position: 'small' }
  ];

  return (
    <>
      <Helmet>
        <title>Gallery | ResearchSat</title>
        <meta name="description" content="Explore our gallery showcasing past missions, team members, and research activities at ResearchSat." />
        <meta name="keywords" content="gallery, space missions, team, research activities, ResearchSat photos" />
        <meta property="og:title" content="Gallery | ResearchSat" />
        <meta property="og:description" content="Explore our gallery showcasing past missions, team members, and research activities at ResearchSat." />
        <meta property="og:image" content="/images/gallery/galleryhr.jpeg" />
        <meta property="og:url" content="/gallery" />
        <meta name="twitter:card" content="summary_large_image" />
      </Helmet>

      <div className={styles.galleryPage}>
        {/* Hero Section */}
        <section className={styles.heroSection}>
          {/* Hero Background */}
          <img
            src={heroBackground}
            alt="Space background"
            className={styles.heroBackground}
          />

          {/* Gradient Overlay */}
          <div className={styles.gradientOverlay}></div>

          {/* Hero Content */}
          <div className={styles.contentContainer}>
            <div className={styles.heroContent}>
              <div className={styles.galleryLabel}>_Gallery</div>
              <h1 className={styles.heroTitle}>
                Discover our world through images
              </h1>
            </div>
          </div>

          {/* Bottom Content */}
          <div className={styles.bottomContainer}>
            <div className={styles.descriptionContainer}>
              <p className={styles.descriptionText}>
                Explore our journey through space research, showcasing past missions, our dedicated team, and groundbreaking activities.
              </p>
              <div style={{ textAlign: 'right' }}>
                <a href="/book-mission" style={{ textDecoration: 'none' }}>
                  <span style={{
                    background: 'linear-gradient(180deg, #BBDED0 0%, #87C2AA 100%)',
                    WebkitBackgroundClip: 'text',
                    backgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    fontSize: '16px',
                    fontWeight: 500,
                    lineHeight: '120%',
                    letterSpacing: '0.25px',
                    fontFamily: 'Poppins, sans-serif'
                  }}>
                    ...explore our missions
                  </span>
                </a>
              </div>
            </div>
          </div>
        </section>

        {/* Mission Section */}
        <section className={styles.gallerySection}>
          <div className={styles.container}>
            <h2 className={styles.sectionTitle}>Space Missions</h2>
            <div className={styles.bentoGrid}>
              {missionSection.map((image, index) => (
                <div
                  key={`mission-${index}`}
                  className={`${styles.bentoItem} ${styles[image.position]}`}
                >
                  <img
                    src={image.src}
                    alt={image.alt}
                    className={styles.bentoImage}
                    loading="lazy"
                  />
                  <div className={styles.imageOverlay}>
                    <span className={styles.imageCategory}>Mission</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className={styles.gallerySection}>
          <div className={styles.container}>
            <h2 className={styles.sectionTitle}>Our Team</h2>
            <div className={styles.bentoGrid}>
              {teamSection.map((image, index) => (
                <div
                  key={`team-${index}`}
                  className={`${styles.bentoItem} ${styles[image.position]}`}
                >
                  <img
                    src={image.src}
                    alt={image.alt}
                    className={styles.bentoImage}
                    loading="lazy"
                  />
                  <div className={styles.imageOverlay}>
                    <span className={styles.imageCategory}>Team</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Payload Section */}
        <section className={styles.gallerySection}>
          <div className={styles.container}>
            <h2 className={styles.sectionTitle}>Payloads & Equipment</h2>
            <div className={styles.bentoGrid}>
              {payloadSection.map((image, index) => (
                <div
                  key={`payload-${index}`}
                  className={`${styles.bentoItem} ${styles[image.position]}`}
                >
                  <img
                    src={image.src}
                    alt={image.alt}
                    className={styles.bentoImage}
                    loading="lazy"
                  />
                  <div className={styles.imageOverlay}>
                    <span className={styles.imageCategory}>Payload</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Activity Section */}
        <section className={styles.gallerySection}>
          <div className={styles.container}>
            <h2 className={styles.sectionTitle}>Research Activities</h2>
            <div className={styles.bentoGrid}>
              {activitySection.map((image, index) => (
                <div
                  key={`activity-${index}`}
                  className={`${styles.bentoItem} ${styles[image.position]}`}
                >
                  <img
                    src={image.src}
                    alt={image.alt}
                    className={styles.bentoImage}
                    loading="lazy"
                  />
                  <div className={styles.imageOverlay}>
                    <span className={styles.imageCategory}>Activity</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className={styles.ctaSection}>
          <div className={styles.container}>
            <div className={styles.ctaContent}>
              <h2 className={styles.ctaTitle}>Ready to be part of our story?</h2>
              <p className={styles.ctaDescription}>
                Join us in advancing space research and be featured in our next gallery showcase.
              </p>
              <div className={styles.ctaButtons}>
                <a href="/book-mission" className={styles.primaryButton}>
                  Book Mission
                </a>
                <a href="/contact" className={styles.secondaryButton}>
                  Contact Us
                </a>
              </div>
            </div>
          </div>
        </section>

        {/* Add margin above footer */}
        <div className={styles.footerMargin}></div>
      </div>
    </>
  );
};

export default GalleryPage;
