import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import styles from '../styles/pages/GalleryPage.module.css';
import heroBackground from '../assets/images/gallery/galleryhr.jpeg';

// Import gallery images from gallery folder
import galimg001 from '../assets/images/gallery/galimg001.jpeg';
import galimg006 from '../assets/images/gallery/galimg006.png';
import galimg007 from '../assets/images/gallery/galimg007.png';
import galimg008 from '../assets/images/gallery/galimg008.JPG';
import galimg009 from '../assets/images/gallery/galimg009.png';
import figure001 from '../assets/images/gallery/Figure001.png';
import figure020 from '../assets/images/gallery/Figure020.png';
import titaniumBox from '../assets/images/gallery/Titanium_Box_croped.png';
import abhrMob from '../assets/images/gallery/abhrMob.png';
import serv1 from '../assets/images/gallery/serv1.jpg';

const GalleryPage = () => {
  useEffect(() => {
    document.title = 'Gallery | ResearchSat';
    window.scrollTo(0, 0);
  }, []);

  const galleryImages = [
    {
      id: 1,
      src: galimg001,
      alt: 'Research Laboratory Setup',
      category: 'activity',
      size: 'large'
    },
    {
      id: 2,
      src: figure001,
      alt: 'Scientific Figure and Data Analysis',
      category: 'mission',
      size: 'medium'
    },
    {
      id: 3,
      src: titaniumBox,
      alt: 'Titanium Payload Container',
      category: 'payload',
      size: 'small'
    },
    {
      id: 4,
      src: galimg006,
      alt: 'Space Research Equipment',
      category: 'activity',
      size: 'medium'
    },
    {
      id: 5,
      src: figure020,
      alt: 'Mission Data and Results',
      category: 'mission',
      size: 'small'
    },
    {
      id: 6,
      src: galimg007,
      alt: 'Laboratory Research Process',
      category: 'activity',
      size: 'large'
    },
    {
      id: 7,
      src: galimg008,
      alt: 'Team Collaboration Session',
      category: 'team',
      size: 'medium'
    },
    {
      id: 8,
      src: abhrMob,
      alt: 'Mobile Research Platform',
      category: 'payload',
      size: 'small'
    },
    {
      id: 9,
      src: galimg009,
      alt: 'Advanced Research Facility',
      category: 'activity',
      size: 'medium'
    },
    {
      id: 10,
      src: serv1,
      alt: 'Space Services and Operations',
      category: 'mission',
      size: 'large'
    }
  ];

  return (
    <>
      <Helmet>
        <title>Gallery | ResearchSat</title>
        <meta name="description" content="Explore our gallery showcasing past missions, team members, and research activities at ResearchSat." />
        <meta name="keywords" content="gallery, space missions, team, research activities, ResearchSat photos" />
        <meta property="og:title" content="Gallery | ResearchSat" />
        <meta property="og:description" content="Explore our gallery showcasing past missions, team members, and research activities at ResearchSat." />
        <meta property="og:image" content="/images/gallery/galleryhr.jpeg" />
        <meta property="og:url" content="/gallery" />
        <meta name="twitter:card" content="summary_large_image" />
      </Helmet>

      <div className={styles.galleryPage}>
        {/* Hero Section */}
        <section className={styles.heroSection}>
          {/* Hero Background */}
          <img
            src={heroBackground}
            alt="Gallery hero background"
            className={styles.heroBackground}
          />

          {/* Gradient Overlay */}
          <div className={styles.gradientOverlay}></div>

          {/* Hero Content */}
          <div className={styles.contentContainer}>
            <div className={styles.heroContent}>
              <div className={styles.galleryLabel}>_Gallery</div>
              <h1 className={styles.heroTitle}>
                Discover our world through images
              </h1>
              <p className={styles.heroSubtitle}>
                Explore our journey through space research, showcasing past missions, our dedicated team, and groundbreaking activities.
              </p>
            </div>
          </div>
        </section>

        {/* Gallery Grid Section */}
        <section className={styles.gallerySection}>
          <div className={styles.container}>
            <div className={styles.galleryGrid}>
              {galleryImages.map((image) => (
                <div
                  key={image.id}
                  className={`${styles.galleryItem} ${styles[image.size]} ${styles[image.category]}`}
                >
                  <img
                    src={image.src}
                    alt={image.alt}
                    className={styles.galleryImage}
                    loading="lazy"
                  />
                  <div className={styles.imageOverlay}>
                    <span className={styles.imageCategory}>
                      {image.category.charAt(0).toUpperCase() + image.category.slice(1)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className={styles.ctaSection}>
          <div className={styles.container}>
            <div className={styles.ctaContent}>
              <h2 className={styles.ctaTitle}>Ready to be part of our story?</h2>
              <p className={styles.ctaDescription}>
                Join us in advancing space research and be featured in our next gallery showcase.
              </p>
              <div className={styles.ctaButtons}>
                <a href="/book-mission" className={styles.primaryButton}>
                  Book Mission
                </a>
                <a href="/contact" className={styles.secondaryButton}>
                  Contact Us
                </a>
              </div>
            </div>
          </div>
        </section>

        {/* Add margin above footer */}
        <div className={styles.footerMargin}></div>
      </div>
    </>
  );
};

export default GalleryPage;
