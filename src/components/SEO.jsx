import React from 'react';
import { Helmet } from 'react-helmet-async';

const SEO = ({
  title,
  description,
  keywords = [],
  ogImage = '/src/assets/images/og-image.jpg',
  ogType = 'website',
  canonical,
  structuredData,
  author = 'ResearchSat Team',
  publishedAt,
  modifiedAt,
  noindex = false,
  locale = 'en_US',
  alternateLanguages = [],
  breadcrumbs = [],
  faqData = [],
  organizationData = null
}) => {
  // Default site title suffix
  const siteName = 'ResearchSat';
  const siteUrl = 'https://researchsat.space';

  // Format the full title (optimized for SEO - keep under 60 characters)
  const fullTitle = title ? `${title} | ${siteName}` : `${siteName} - Space Biology & Microgravity Research Solutions`;

  // Default description if none provided (optimized for SEO - keep under 160 characters)
  const defaultDescription = 'ResearchSat provides cutting-edge microgravity research solutions for space biology, offering custom satellite payloads and mission services worldwide.';

  // Use provided description or default
  const metaDescription = description || defaultDescription;

  // Enhanced default keywords with long-tail variations
  const defaultKeywords = [
    'space biology research',
    'microgravity research solutions',
    'satellite payloads',
    'space missions',
    'space biotechnology',
    'orbital research',
    'ISS experiments',
    'protein crystallization space',
    'cell culture microgravity',
    'space medicine research'
  ];

  // Combine default and provided keywords
  const metaKeywords = [...defaultKeywords, ...keywords].join(', ');

  // Current URL for canonical link if not provided
  const currentUrl = canonical || (typeof window !== 'undefined' ? window.location.href : siteUrl);

  // Default organization structured data
  const defaultOrganizationData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "ResearchSat",
    "url": siteUrl,
    "logo": `${siteUrl}/src/assets/images/new-logo.svg`,
    "description": metaDescription,
    "foundingDate": "2020",
    "industry": "Space Technology",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "US"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>"
    },
    "sameAs": [
      "https://www.linkedin.com/company/researchsat/",
      "https://twitter.com/researchsat",
      "https://www.facebook.com/researchsat/",
      "https://www.instagram.com/researchsat/"
    ]
  };

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={metaDescription} />
      <meta name="keywords" content={metaKeywords} />

      {/* Canonical Link */}
      <link rel="canonical" href={currentUrl} />

      {/* Alternate Language Links */}
      {alternateLanguages.map((lang) => (
        <link key={lang.hreflang} rel="alternate" hreflang={lang.hreflang} href={lang.href} />
      ))}

      {/* Open Graph / Facebook */}
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={metaDescription} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={locale} />

      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@researchsat" />
      <meta name="twitter:creator" content="@researchsat" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={metaDescription} />
      <meta name="twitter:image" content={ogImage} />

      {/* Additional Meta Tags for SEO */}
      <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
      <meta name="author" content={author} />
      <meta name="robots" content={noindex ? 'noindex, nofollow' : 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1'} />
      <meta name="googlebot" content="index, follow" />
      <meta name="bingbot" content="index, follow" />

      {/* Mobile and App Meta Tags */}
      <meta name="theme-color" content="#17242D" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
      <meta name="apple-mobile-web-app-title" content={siteName} />
      <meta name="application-name" content={siteName} />
      <meta name="msapplication-TileColor" content="#17242D" />
      <meta name="msapplication-config" content="/browserconfig.xml" />

      {/* Performance and Security */}
      <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="referrer" content="origin-when-cross-origin" />

      {/* If article, add published and modified dates */}
      {publishedAt && <meta property="article:published_time" content={publishedAt} />}
      {modifiedAt && <meta property="article:modified_time" content={modifiedAt} />}

      {/* Organization Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(organizationData || defaultOrganizationData)}
      </script>

      {/* Custom Structured Data */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}

      {/* Breadcrumbs Structured Data */}
      {breadcrumbs.length > 0 && (
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": breadcrumbs.map((crumb, index) => ({
              "@type": "ListItem",
              "position": index + 1,
              "name": crumb.name,
              "item": crumb.url
            }))
          })}
        </script>
      )}

      {/* FAQ Structured Data */}
      {faqData.length > 0 && (
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": faqData.map((faq) => ({
              "@type": "Question",
              "name": faq.question,
              "acceptedAnswer": {
                "@type": "Answer",
                "text": faq.answer
              }
            }))
          })}
        </script>
      )}

      <html lang="en" />
    </Helmet>
  );
};

export default SEO;
