import React from 'react';
import styles from '../styles/components/PlayButton.module.css';

const PlayButton = ({ onClick }) => {
  return (
    <button 
      className={styles.playButton} 
      onClick={onClick}
      aria-label="Play video"
    >
      <div className={styles.buttonInner}>
        <svg 
          width="24" 
          height="24" 
          viewBox="0 0 24 24" 
          fill="none" 
          xmlns="http://www.w3.org/2000/svg"
          className={styles.playIcon}
        >
          <path 
            d="M8 5V19L19 12L8 5Z" 
            fill="white" 
            stroke="white" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
          />
        </svg>
      </div>
      <div className={styles.ringAnimation}></div>
    </button>
  );
};

export default PlayButton;
