import React, { useState, useEffect, useRef, useMemo } from 'react';
import OptimizedImage from './OptimizedImage';

const VirtualizedGallery = ({
  images,
  onImageClick,
  className,
  itemHeight = 300,
  containerHeight = 600,
  overscan = 3
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerSize, setContainerSize] = useState({ width: 0, height: containerHeight });
  const containerRef = useRef(null);

  // Calculate visible items based on scroll position
  const visibleRange = useMemo(() => {
    const itemsPerRow = Math.floor(containerSize.width / 300); // Assuming 300px item width
    const totalRows = Math.ceil(images.length / itemsPerRow);
    const rowHeight = itemHeight;
    
    const startRow = Math.max(0, Math.floor(scrollTop / rowHeight) - overscan);
    const endRow = Math.min(
      totalRows - 1,
      Math.ceil((scrollTop + containerSize.height) / rowHeight) + overscan
    );
    
    const startIndex = startRow * itemsPerRow;
    const endIndex = Math.min(images.length - 1, (endRow + 1) * itemsPerRow - 1);
    
    return {
      startIndex,
      endIndex,
      startRow,
      endRow,
      itemsPerRow,
      totalRows,
      rowHeight
    };
  }, [scrollTop, containerSize, images.length, itemHeight, overscan]);

  // Update container size on resize
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setContainerSize({ width, height });
      }
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  // Handle scroll
  const handleScroll = (e) => {
    setScrollTop(e.target.scrollTop);
  };

  // Generate visible items
  const visibleItems = useMemo(() => {
    const items = [];
    const { startIndex, endIndex, itemsPerRow, rowHeight } = visibleRange;
    
    for (let i = startIndex; i <= endIndex; i++) {
      if (i >= images.length) break;
      
      const row = Math.floor(i / itemsPerRow);
      const col = i % itemsPerRow;
      const itemWidth = containerSize.width / itemsPerRow;
      
      items.push({
        index: i,
        image: images[i],
        style: {
          position: 'absolute',
          top: row * rowHeight,
          left: col * itemWidth,
          width: itemWidth - 16, // Account for gap
          height: itemHeight - 16,
          padding: '8px'
        }
      });
    }
    
    return items;
  }, [visibleRange, images, containerSize, itemHeight]);

  const totalHeight = visibleRange.totalRows * visibleRange.rowHeight;

  return (
    <div
      ref={containerRef}
      className={className}
      style={{
        height: containerHeight,
        overflow: 'auto',
        position: 'relative'
      }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        {visibleItems.map(({ index, image, style }) => (
          <div
            key={index}
            style={style}
            onClick={() => onImageClick && onImageClick(index)}
          >
            <OptimizedImage
              src={image.src}
              alt={image.alt}
              className="gallery-item-image"
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                borderRadius: '12px',
                cursor: 'pointer'
              }}
              sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
              loading="lazy"
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default VirtualizedGallery;
