import React, { useState, useRef, useEffect } from 'react';

const OptimizedImage = ({
  src,
  alt,
  className,
  loading = 'lazy',
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  priority = false,
  onClick,
  style,
  placeholder = true
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef(null);

  // Generate responsive image sources
  const generateSrcSet = (baseSrc) => {
    if (!baseSrc) return '';
    
    const extension = baseSrc.split('.').pop().toLowerCase();
    const basePath = baseSrc.replace(`.${extension}`, '');
    
    // Generate WebP and original format sources
    const webpSrcSet = [
      `${basePath}-320.webp 320w`,
      `${basePath}-640.webp 640w`,
      `${basePath}-960.webp 960w`,
      `${basePath}-1280.webp 1280w`,
      `${basePath}-1920.webp 1920w`
    ].join(', ');
    
    const fallbackSrcSet = [
      `${basePath}-320.${extension} 320w`,
      `${basePath}-640.${extension} 640w`,
      `${basePath}-960.${extension} 960w`,
      `${basePath}-1280.${extension} 1280w`,
      `${basePath}-1920.${extension} 1920w`
    ].join(', ');
    
    return { webpSrcSet, fallbackSrcSet };
  };

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority) {
      setIsInView(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px 0px', // Start loading 50px before the image enters viewport
        threshold: 0.1
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority]);

  const handleLoad = () => {
    setIsLoaded(true);
  };

  const handleError = () => {
    setHasError(true);
    setIsLoaded(true);
  };

  const { webpSrcSet, fallbackSrcSet } = generateSrcSet(src);

  // Placeholder component
  const PlaceholderDiv = () => (
    <div
      className={className}
      style={{
        ...style,
        backgroundColor: '#1a1a1a',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '200px',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      <div
        style={{
          width: '40px',
          height: '40px',
          border: '3px solid rgba(255, 50, 65, 0.3)',
          borderTop: '3px solid #ff3241',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}
      />
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );

  // Error fallback
  if (hasError) {
    return (
      <div
        className={className}
        style={{
          ...style,
          backgroundColor: '#1a1a1a',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#666',
          fontSize: '14px'
        }}
      >
        Image unavailable
      </div>
    );
  }

  // Show placeholder until image is in view
  if (!isInView && placeholder) {
    return <div ref={imgRef}><PlaceholderDiv /></div>;
  }

  return (
    <div ref={imgRef} style={{ position: 'relative' }}>
      {/* Show placeholder while loading */}
      {!isLoaded && placeholder && <PlaceholderDiv />}
      
      <picture
        style={{
          display: isLoaded ? 'block' : 'none'
        }}
      >
        {/* WebP source with responsive sizes */}
        <source
          srcSet={webpSrcSet}
          sizes={sizes}
          type="image/webp"
        />
        
        {/* Fallback source with responsive sizes */}
        <source
          srcSet={fallbackSrcSet}
          sizes={sizes}
        />
        
        {/* Fallback img element */}
        <img
          src={src}
          alt={alt}
          className={className}
          style={style}
          loading={loading}
          onLoad={handleLoad}
          onError={handleError}
          onClick={onClick}
          decoding="async"
          fetchpriority={priority ? 'high' : 'auto'}
        />
      </picture>
    </div>
  );
};

export default OptimizedImage;
