// Sitemap Generator for ResearchSat
// Generates XML sitemap for better SEO

import { pageConfigs, siteConfig } from '../config/seoConfig.js';

// Define all static pages with their priorities and change frequencies
const staticPages = [
  {
    path: '/',
    priority: 1.0,
    changefreq: 'weekly',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    path: '/about',
    priority: 0.9,
    changefreq: 'monthly',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    path: '/missions',
    priority: 0.9,
    changefreq: 'weekly',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    path: '/payloads',
    priority: 0.9,
    changefreq: 'weekly',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    path: '/gallery',
    priority: 0.8,
    changefreq: 'weekly',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    path: '/news',
    priority: 0.8,
    changefreq: 'daily',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    path: '/careers',
    priority: 0.7,
    changefreq: 'weekly',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    path: '/contact',
    priority: 0.8,
    changefreq: 'monthly',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    path: '/book-mission',
    priority: 0.9,
    changefreq: 'monthly',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    path: '/features',
    priority: 0.7,
    changefreq: 'monthly',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    path: '/offerings',
    priority: 0.7,
    changefreq: 'monthly',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    path: '/partnerships',
    priority: 0.6,
    changefreq: 'monthly',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    path: '/past-missions',
    priority: 0.6,
    changefreq: 'monthly',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    path: '/privacy-policy',
    priority: 0.3,
    changefreq: 'yearly',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    path: '/terms-conditions',
    priority: 0.3,
    changefreq: 'yearly',
    lastmod: new Date().toISOString().split('T')[0]
  }
];

// Generate XML sitemap
export const generateSitemap = (additionalPages = []) => {
  const allPages = [...staticPages, ...additionalPages];
  
  const sitemapXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9">
${allPages.map(page => `  <url>
    <loc>${siteConfig.siteUrl}${page.path}</loc>
    <lastmod>${page.lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('\n')}
</urlset>`;

  return sitemapXml;
};

// Generate robots.txt content
export const generateRobotsTxt = () => {
  return `User-agent: *
Allow: /

# Sitemap
Sitemap: ${siteConfig.siteUrl}/sitemap.xml

# Crawl-delay for respectful crawling
Crawl-delay: 1

# Disallow admin and private areas
Disallow: /admin/
Disallow: /api/
Disallow: /_next/
Disallow: /static/

# Allow important pages
Allow: /
Allow: /about
Allow: /missions
Allow: /payloads
Allow: /gallery
Allow: /news
Allow: /careers
Allow: /contact
Allow: /book-mission`;
};

// Generate structured data for website
export const generateWebsiteStructuredData = () => {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": siteConfig.siteName,
    "alternateName": "ResearchSat Space Biology Research",
    "url": siteConfig.siteUrl,
    "description": siteConfig.defaultDescription,
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${siteConfig.siteUrl}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": siteConfig.siteName,
      "url": siteConfig.siteUrl,
      "logo": {
        "@type": "ImageObject",
        "url": `${siteConfig.siteUrl}/src/assets/images/new-logo.svg`,
        "width": 200,
        "height": 60
      },
      "sameAs": Object.values(siteConfig.socialMedia)
    }
  };
};

// Generate breadcrumb list structured data
export const generateBreadcrumbStructuredData = (breadcrumbs) => {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url
    }))
  };
};

// Generate FAQ structured data
export const generateFAQStructuredData = (faqs) => {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
};

// Generate article structured data for news/blog posts
export const generateArticleStructuredData = (article) => {
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": article.title,
    "description": article.description,
    "image": article.image,
    "author": {
      "@type": "Organization",
      "name": siteConfig.siteName
    },
    "publisher": {
      "@type": "Organization",
      "name": siteConfig.siteName,
      "logo": {
        "@type": "ImageObject",
        "url": `${siteConfig.siteUrl}/src/assets/images/new-logo.svg`
      }
    },
    "datePublished": article.publishedDate,
    "dateModified": article.modifiedDate || article.publishedDate,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `${siteConfig.siteUrl}${article.url}`
    }
  };
};

// Generate service structured data
export const generateServiceStructuredData = (service) => {
  return {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": service.name,
    "description": service.description,
    "provider": {
      "@type": "Organization",
      "name": siteConfig.siteName,
      "url": siteConfig.siteUrl
    },
    "serviceType": service.type,
    "areaServed": "Worldwide",
    "offers": {
      "@type": "Offer",
      "description": service.offerDescription,
      "availability": "https://schema.org/InStock"
    }
  };
};

// Meta tags generator for social media
export const generateSocialMetaTags = (pageData) => {
  return {
    // Open Graph
    'og:title': pageData.title,
    'og:description': pageData.description,
    'og:image': pageData.image || `${siteConfig.siteUrl}/src/assets/images/og-image.jpg`,
    'og:url': `${siteConfig.siteUrl}${pageData.path}`,
    'og:type': pageData.type || 'website',
    'og:site_name': siteConfig.siteName,
    
    // Twitter
    'twitter:card': 'summary_large_image',
    'twitter:site': siteConfig.twitterHandle,
    'twitter:creator': siteConfig.twitterHandle,
    'twitter:title': pageData.title,
    'twitter:description': pageData.description,
    'twitter:image': pageData.image || `${siteConfig.siteUrl}/src/assets/images/og-image.jpg`,
    
    // Additional meta tags
    'description': pageData.description,
    'keywords': pageData.keywords ? pageData.keywords.join(', ') : siteConfig.defaultKeywords.join(', '),
    'author': siteConfig.author,
    'robots': 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1'
  };
};

export default {
  generateSitemap,
  generateRobotsTxt,
  generateWebsiteStructuredData,
  generateBreadcrumbStructuredData,
  generateFAQStructuredData,
  generateArticleStructuredData,
  generateServiceStructuredData,
  generateSocialMetaTags,
  staticPages
};
