// Image preloader utility for optimizing gallery performance

class ImagePreloader {
  constructor() {
    this.cache = new Map();
    this.loadingPromises = new Map();
    this.preloadQueue = [];
    this.isProcessing = false;
    this.maxConcurrent = 3; // Maximum concurrent image loads
    this.currentLoading = 0;
  }

  // Preload a single image with WebP support
  async preloadImage(src, priority = false) {
    if (this.cache.has(src)) {
      return this.cache.get(src);
    }

    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src);
    }

    const promise = this.loadImageWithFallback(src);
    this.loadingPromises.set(src, promise);

    try {
      const result = await promise;
      this.cache.set(src, result);
      this.loadingPromises.delete(src);
      return result;
    } catch (error) {
      this.loadingPromises.delete(src);
      throw error;
    }
  }

  // Load image with WebP fallback
  async loadImageWithFallback(src) {
    const extension = src.split('.').pop().toLowerCase();
    const basePath = src.replace(`.${extension}`, '');
    
    // Try WebP first for better compression
    const webpSrc = `${basePath}.webp`;
    
    try {
      // Check if WebP is supported
      if (this.supportsWebP()) {
        const webpImage = await this.loadSingleImage(webpSrc);
        return { src: webpSrc, image: webpImage, format: 'webp' };
      }
    } catch (error) {
      // WebP failed, fall back to original
    }

    // Load original format
    const originalImage = await this.loadSingleImage(src);
    return { src, image: originalImage, format: extension };
  }

  // Load a single image and return promise
  loadSingleImage(src) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => resolve(img);
      img.onerror = () => reject(new Error(`Failed to load image: ${src}`));
      
      // Set crossOrigin for external images
      if (src.startsWith('http')) {
        img.crossOrigin = 'anonymous';
      }
      
      img.src = src;
    });
  }

  // Check WebP support
  supportsWebP() {
    if (typeof this._webpSupport !== 'undefined') {
      return this._webpSupport;
    }

    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    
    this._webpSupport = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    return this._webpSupport;
  }

  // Preload multiple images with queue management
  async preloadImages(sources, options = {}) {
    const { 
      priority = false, 
      maxConcurrent = this.maxConcurrent,
      onProgress,
      onError 
    } = options;

    const results = [];
    const errors = [];
    let completed = 0;

    // Process images in batches
    for (let i = 0; i < sources.length; i += maxConcurrent) {
      const batch = sources.slice(i, i + maxConcurrent);
      
      const batchPromises = batch.map(async (src) => {
        try {
          const result = await this.preloadImage(src, priority);
          completed++;
          
          if (onProgress) {
            onProgress(completed, sources.length);
          }
          
          return result;
        } catch (error) {
          completed++;
          errors.push({ src, error });
          
          if (onError) {
            onError(src, error);
          }
          
          if (onProgress) {
            onProgress(completed, sources.length);
          }
          
          return null;
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return {
      results: results.filter(Boolean),
      errors,
      totalLoaded: results.filter(Boolean).length,
      totalErrors: errors.length
    };
  }

  // Preload critical images immediately
  async preloadCritical(sources) {
    return this.preloadImages(sources, {
      priority: true,
      maxConcurrent: 6, // Higher concurrency for critical images
      onProgress: (loaded, total) => {
        console.log(`Critical images: ${loaded}/${total} loaded`);
      }
    });
  }

  // Preload images in viewport
  async preloadInViewport(sources) {
    return this.preloadImages(sources, {
      priority: false,
      maxConcurrent: 2, // Lower concurrency for viewport images
      onProgress: (loaded, total) => {
        console.log(`Viewport images: ${loaded}/${total} loaded`);
      }
    });
  }

  // Get responsive image sources
  getResponsiveSources(baseSrc) {
    const extension = baseSrc.split('.').pop().toLowerCase();
    const basePath = baseSrc.replace(`.${extension}`, '');
    
    return {
      webp: [
        `${basePath}-320.webp`,
        `${basePath}-640.webp`,
        `${basePath}-960.webp`,
        `${basePath}-1280.webp`,
        `${basePath}-1920.webp`
      ],
      original: [
        `${basePath}-320.${extension}`,
        `${basePath}-640.${extension}`,
        `${basePath}-960.${extension}`,
        `${basePath}-1280.${extension}`,
        `${basePath}-1920.${extension}`
      ]
    };
  }

  // Preload responsive images based on viewport
  async preloadResponsive(baseSrc, viewportWidth = window.innerWidth) {
    const sources = this.getResponsiveSources(baseSrc);
    
    // Determine appropriate size based on viewport
    let targetSize;
    if (viewportWidth <= 320) targetSize = '320';
    else if (viewportWidth <= 640) targetSize = '640';
    else if (viewportWidth <= 960) targetSize = '960';
    else if (viewportWidth <= 1280) targetSize = '1280';
    else targetSize = '1920';

    // Preload current size and next size up
    const sizesToLoad = [targetSize];
    const sizeIndex = ['320', '640', '960', '1280', '1920'].indexOf(targetSize);
    if (sizeIndex < 4) {
      sizesToLoad.push(['320', '640', '960', '1280', '1920'][sizeIndex + 1]);
    }

    const imagesToPreload = [];
    sizesToLoad.forEach(size => {
      const extension = baseSrc.split('.').pop().toLowerCase();
      const basePath = baseSrc.replace(`.${extension}`, '');
      imagesToPreload.push(`${basePath}-${size}.webp`);
      imagesToPreload.push(`${basePath}-${size}.${extension}`);
    });

    return this.preloadImages(imagesToPreload, { priority: true });
  }

  // Clear cache
  clearCache() {
    this.cache.clear();
    this.loadingPromises.clear();
  }

  // Get cache stats
  getCacheStats() {
    return {
      cached: this.cache.size,
      loading: this.loadingPromises.size,
      memoryUsage: this.estimateMemoryUsage()
    };
  }

  // Estimate memory usage (rough calculation)
  estimateMemoryUsage() {
    let totalBytes = 0;
    this.cache.forEach((value) => {
      if (value.image) {
        // Rough estimate: width * height * 4 bytes (RGBA)
        totalBytes += (value.image.width || 0) * (value.image.height || 0) * 4;
      }
    });
    return totalBytes;
  }
}

// Create singleton instance
const imagePreloader = new ImagePreloader();

export default imagePreloader;

// Export utility functions
export const preloadImage = (src, priority) => imagePreloader.preloadImage(src, priority);
export const preloadImages = (sources, options) => imagePreloader.preloadImages(sources, options);
export const preloadCritical = (sources) => imagePreloader.preloadCritical(sources);
export const preloadResponsive = (baseSrc, viewportWidth) => imagePreloader.preloadResponsive(baseSrc, viewportWidth);
