// Performance monitoring utility for gallery optimization

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      imageLoadTimes: [],
      totalImages: 0,
      loadedImages: 0,
      failedImages: 0,
      averageLoadTime: 0,
      largestContentfulPaint: 0,
      firstContentfulPaint: 0,
      cumulativeLayoutShift: 0
    };
    
    this.observers = [];
    this.startTime = performance.now();
    this.initializeObservers();
  }

  // Initialize performance observers
  initializeObservers() {
    // Largest Contentful Paint
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          this.metrics.largestContentfulPaint = lastEntry.startTime;
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);
      } catch (e) {
        console.warn('LCP observer not supported');
      }

      // First Contentful Paint
      try {
        const fcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.name === 'first-contentful-paint') {
              this.metrics.firstContentfulPaint = entry.startTime;
            }
          });
        });
        fcpObserver.observe({ entryTypes: ['paint'] });
        this.observers.push(fcpObserver);
      } catch (e) {
        console.warn('FCP observer not supported');
      }

      // Cumulative Layout Shift
      try {
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0;
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          this.metrics.cumulativeLayoutShift = clsValue;
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(clsObserver);
      } catch (e) {
        console.warn('CLS observer not supported');
      }
    }
  }

  // Track image loading start
  trackImageLoadStart(src) {
    const startTime = performance.now();
    this.metrics.totalImages++;
    
    return {
      src,
      startTime,
      endTime: null,
      loadTime: null,
      success: null
    };
  }

  // Track image loading completion
  trackImageLoadEnd(imageTracker, success = true) {
    const endTime = performance.now();
    const loadTime = endTime - imageTracker.startTime;
    
    imageTracker.endTime = endTime;
    imageTracker.loadTime = loadTime;
    imageTracker.success = success;
    
    if (success) {
      this.metrics.loadedImages++;
      this.metrics.imageLoadTimes.push(loadTime);
    } else {
      this.metrics.failedImages++;
    }
    
    this.updateAverageLoadTime();
    return imageTracker;
  }

  // Update average load time
  updateAverageLoadTime() {
    if (this.metrics.imageLoadTimes.length > 0) {
      const total = this.metrics.imageLoadTimes.reduce((sum, time) => sum + time, 0);
      this.metrics.averageLoadTime = total / this.metrics.imageLoadTimes.length;
    }
  }

  // Get performance metrics
  getMetrics() {
    return {
      ...this.metrics,
      totalLoadTime: performance.now() - this.startTime,
      loadSuccessRate: this.metrics.totalImages > 0 
        ? (this.metrics.loadedImages / this.metrics.totalImages) * 100 
        : 0,
      medianLoadTime: this.getMedianLoadTime(),
      p95LoadTime: this.getPercentileLoadTime(95),
      slowestImage: Math.max(...this.metrics.imageLoadTimes, 0),
      fastestImage: Math.min(...this.metrics.imageLoadTimes, 0)
    };
  }

  // Get median load time
  getMedianLoadTime() {
    if (this.metrics.imageLoadTimes.length === 0) return 0;
    
    const sorted = [...this.metrics.imageLoadTimes].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    
    return sorted.length % 2 === 0
      ? (sorted[mid - 1] + sorted[mid]) / 2
      : sorted[mid];
  }

  // Get percentile load time
  getPercentileLoadTime(percentile) {
    if (this.metrics.imageLoadTimes.length === 0) return 0;
    
    const sorted = [...this.metrics.imageLoadTimes].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    
    return sorted[Math.max(0, index)];
  }

  // Log performance summary
  logPerformanceSummary() {
    const metrics = this.getMetrics();
    
    console.group('🚀 Gallery Performance Metrics');
    console.log(`📊 Total Images: ${metrics.totalImages}`);
    console.log(`✅ Loaded Successfully: ${metrics.loadedImages}`);
    console.log(`❌ Failed to Load: ${metrics.failedImages}`);
    console.log(`📈 Success Rate: ${metrics.loadSuccessRate.toFixed(1)}%`);
    console.log(`⏱️ Average Load Time: ${metrics.averageLoadTime.toFixed(0)}ms`);
    console.log(`📊 Median Load Time: ${metrics.medianLoadTime.toFixed(0)}ms`);
    console.log(`🐌 95th Percentile: ${metrics.p95LoadTime.toFixed(0)}ms`);
    console.log(`🏃 Fastest Image: ${metrics.fastestImage.toFixed(0)}ms`);
    console.log(`🐌 Slowest Image: ${metrics.slowestImage.toFixed(0)}ms`);
    console.log(`🎨 First Contentful Paint: ${metrics.firstContentfulPaint.toFixed(0)}ms`);
    console.log(`🖼️ Largest Contentful Paint: ${metrics.largestContentfulPaint.toFixed(0)}ms`);
    console.log(`📐 Cumulative Layout Shift: ${metrics.cumulativeLayoutShift.toFixed(3)}`);
    console.log(`⏰ Total Load Time: ${metrics.totalLoadTime.toFixed(0)}ms`);
    console.groupEnd();
  }

  // Get performance grade
  getPerformanceGrade() {
    const metrics = this.getMetrics();
    let score = 100;
    
    // Deduct points for slow loading
    if (metrics.averageLoadTime > 1000) score -= 20;
    else if (metrics.averageLoadTime > 500) score -= 10;
    
    // Deduct points for failed images
    if (metrics.loadSuccessRate < 95) score -= 15;
    else if (metrics.loadSuccessRate < 98) score -= 5;
    
    // Deduct points for poor Core Web Vitals
    if (metrics.largestContentfulPaint > 2500) score -= 20;
    else if (metrics.largestContentfulPaint > 1500) score -= 10;
    
    if (metrics.cumulativeLayoutShift > 0.25) score -= 15;
    else if (metrics.cumulativeLayoutShift > 0.1) score -= 5;
    
    // Return grade
    if (score >= 90) return { grade: 'A', score, message: 'Excellent performance!' };
    if (score >= 80) return { grade: 'B', score, message: 'Good performance' };
    if (score >= 70) return { grade: 'C', score, message: 'Average performance' };
    if (score >= 60) return { grade: 'D', score, message: 'Below average performance' };
    return { grade: 'F', score, message: 'Poor performance - needs optimization' };
  }

  // Track Core Web Vitals
  trackCoreWebVitals() {
    return new Promise((resolve) => {
      // Wait for page load to complete
      if (document.readyState === 'complete') {
        setTimeout(() => {
          resolve(this.getCoreWebVitals());
        }, 1000);
      } else {
        window.addEventListener('load', () => {
          setTimeout(() => {
            resolve(this.getCoreWebVitals());
          }, 1000);
        });
      }
    });
  }

  // Get Core Web Vitals summary
  getCoreWebVitals() {
    const metrics = this.getMetrics();
    
    return {
      lcp: {
        value: metrics.largestContentfulPaint,
        rating: metrics.largestContentfulPaint <= 1500 ? 'good' : 
                metrics.largestContentfulPaint <= 2500 ? 'needs-improvement' : 'poor'
      },
      fcp: {
        value: metrics.firstContentfulPaint,
        rating: metrics.firstContentfulPaint <= 1000 ? 'good' : 
                metrics.firstContentfulPaint <= 1800 ? 'needs-improvement' : 'poor'
      },
      cls: {
        value: metrics.cumulativeLayoutShift,
        rating: metrics.cumulativeLayoutShift <= 0.1 ? 'good' : 
                metrics.cumulativeLayoutShift <= 0.25 ? 'needs-improvement' : 'poor'
      }
    };
  }

  // Cleanup observers
  cleanup() {
    this.observers.forEach(observer => {
      try {
        observer.disconnect();
      } catch (e) {
        console.warn('Error disconnecting observer:', e);
      }
    });
    this.observers = [];
  }

  // Export metrics as JSON
  exportMetrics() {
    const metrics = this.getMetrics();
    const grade = this.getPerformanceGrade();
    const coreWebVitals = this.getCoreWebVitals();
    
    return {
      timestamp: new Date().toISOString(),
      metrics,
      grade,
      coreWebVitals,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    };
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

// Export utility functions
export const trackImageLoad = (src) => performanceMonitor.trackImageLoadStart(src);
export const trackImageComplete = (tracker, success) => performanceMonitor.trackImageLoadEnd(tracker, success);
export const getPerformanceMetrics = () => performanceMonitor.getMetrics();
export const logPerformance = () => performanceMonitor.logPerformanceSummary();
export const getPerformanceGrade = () => performanceMonitor.getPerformanceGrade();
export const trackCoreWebVitals = () => performanceMonitor.trackCoreWebVitals();
export const exportPerformanceData = () => performanceMonitor.exportMetrics();

export default performanceMonitor;
