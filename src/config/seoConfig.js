// SEO Configuration for ResearchSat
// Centralized SEO data for all pages

export const siteConfig = {
  siteName: 'ResearchSat',
  siteUrl: 'https://researchsat.space',
  defaultDescription: 'ResearchSat provides cutting-edge microgravity research solutions for space biology, offering custom satellite payloads and mission services worldwide.',
  defaultKeywords: [
    'space biology research',
    'microgravity research solutions',
    'satellite payloads',
    'space missions',
    'space biotechnology',
    'orbital research',
    'ISS experiments',
    'protein crystallization space',
    'cell culture microgravity',
    'space medicine research'
  ],
  author: 'ResearchSat Team',
  twitterHandle: '@researchsat',
  socialMedia: {
    facebook: 'https://www.facebook.com/researchsat/',
    twitter: 'https://twitter.com/researchsat',
    instagram: 'https://www.instagram.com/researchsat/',
    linkedin: 'https://www.linkedin.com/company/researchsat/'
  }
};

export const pageConfigs = {
  home: {
    title: 'Space Biology & Microgravity Research Solutions',
    description: 'Unlock the potential of microgravity environments to advance life-science technologies and therapeutics with ResearchSat\'s custom satellite solutions and space research services.',
    keywords: [
      'microgravity research',
      'space biology',
      'satellite payloads',
      'life sciences',
      'space medicine',
      'protein crystallization',
      'cell culture space',
      'ISS research',
      'orbital experiments',
      'space biotechnology'
    ],
    path: '/'
  },
  about: {
    title: 'About Us - Space Biology Research Pioneers',
    description: 'Learn about ResearchSat\'s mission to advance space biology research through innovative microgravity solutions, custom satellite payloads, and cutting-edge space technology.',
    keywords: [
      'about researchsat',
      'space biology company',
      'microgravity research team',
      'satellite technology',
      'space research pioneers',
      'biotechnology space',
      'research satellite company',
      'space medicine innovation'
    ],
    path: '/about'
  },
  gallery: {
    title: 'Gallery - Space Biology Research Images',
    description: 'Explore ResearchSat\'s comprehensive gallery showcasing space missions, research team, cutting-edge payloads, and groundbreaking microgravity research activities.',
    keywords: [
      'space research gallery',
      'microgravity experiments photos',
      'satellite missions images',
      'space biology photos',
      'research team gallery',
      'space technology images',
      'orbital research pictures',
      'space laboratory photos'
    ],
    path: '/gallery'
  },
  contact: {
    title: 'Contact Us - Space Biology Research Partnerships',
    description: 'Get in touch with ResearchSat for space biology research collaborations, custom mission planning, partnership opportunities, and microgravity experiment consultations.',
    keywords: [
      'contact researchsat',
      'space research partnerships',
      'microgravity collaboration',
      'satellite mission planning',
      'space biology consulting',
      'research partnerships',
      'space technology contact',
      'orbital research collaboration'
    ],
    path: '/contact'
  },
  careers: {
    title: 'Careers - Join Our Space Biology Research Team',
    description: 'Join ResearchSat\'s innovative team and help advance space biology research, microgravity technology, and satellite mission development. Explore career opportunities.',
    keywords: [
      'researchsat careers',
      'space research jobs',
      'microgravity technology careers',
      'satellite engineering jobs',
      'space biology careers',
      'aerospace engineering positions',
      'space technology jobs',
      'research scientist positions'
    ],
    path: '/careers'
  },
  missions: {
    title: 'Space Biology Research Missions',
    description: 'Explore ResearchSat\'s range of space mission types from atmospheric to ISS missions, providing the perfect environment for your space biology research needs.',
    keywords: [
      'space missions',
      'microgravity research',
      'ISS missions',
      'suborbital missions',
      'atmospheric missions',
      'orbital missions',
      'space research platforms',
      'satellite missions'
    ],
    path: '/missions'
  },
  payloads: {
    title: 'Space Biology Research Payloads',
    description: 'Explore ResearchSat\'s custom payload solutions for space biology research, designed to meet the specific requirements of your experiments in microgravity.',
    keywords: [
      'space payloads',
      'microgravity research',
      'biology payloads',
      'custom payloads',
      'space biology',
      'research payloads',
      'satellite equipment',
      'space laboratory equipment'
    ],
    path: '/payloads'
  },
  news: {
    title: 'News & Updates - Space Biology Research',
    description: 'Stay updated with the latest news, research findings, mission updates, and technological advancements from ResearchSat\'s space biology research team.',
    keywords: [
      'researchsat news',
      'space biology news',
      'satellite research updates',
      'space mission news',
      'microgravity research news',
      'space technology updates',
      'research findings',
      'space industry news'
    ],
    path: '/news'
  },
  bookMission: {
    title: 'Book Your Space Research Mission',
    description: 'Design and book your custom space research mission. From protein crystallization to material science - launch your research to new heights with ResearchSat.',
    keywords: [
      'book space mission',
      'custom space research',
      'microgravity experiments',
      'satellite mission booking',
      'space research planning',
      'orbital experiments',
      'space biology mission',
      'research mission design'
    ],
    path: '/book-mission'
  },
  features: {
    title: 'Features - Advanced Space Research Capabilities',
    description: 'Discover ResearchSat\'s advanced features and capabilities for space biology research, including cutting-edge technology and innovative solutions.',
    keywords: [
      'space research features',
      'microgravity capabilities',
      'satellite technology features',
      'space biology tools',
      'research capabilities',
      'space laboratory features',
      'orbital research tools',
      'space technology solutions'
    ],
    path: '/features'
  },
  offerings: {
    title: 'Our Offerings - Complete Space Research Solutions',
    description: 'Explore ResearchSat\'s comprehensive offerings for space biology research, from mission planning to data analysis and everything in between.',
    keywords: [
      'space research offerings',
      'microgravity services',
      'satellite research services',
      'space biology solutions',
      'research services',
      'space mission services',
      'orbital research offerings',
      'space technology services'
    ],
    path: '/offerings'
  }
};

// Generate structured data for different page types
export const generateStructuredData = (pageType, customData = {}) => {
  const baseOrganization = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "ResearchSat",
    "url": siteConfig.siteUrl,
    "logo": `${siteConfig.siteUrl}/src/assets/images/new-logo.svg`,
    "description": siteConfig.defaultDescription,
    "foundingDate": "2020",
    "industry": "Space Technology",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "US"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>"
    },
    "sameAs": Object.values(siteConfig.socialMedia)
  };

  switch (pageType) {
    case 'website':
      return {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": siteConfig.siteName,
        "url": siteConfig.siteUrl,
        "description": siteConfig.defaultDescription,
        "potentialAction": {
          "@type": "SearchAction",
          "target": `${siteConfig.siteUrl}/search?q={search_term_string}`,
          "query-input": "required name=search_term_string"
        },
        "publisher": baseOrganization,
        ...customData
      };
    
    case 'organization':
      return {
        ...baseOrganization,
        ...customData
      };
    
    case 'service':
      return {
        "@context": "https://schema.org",
        "@type": "Service",
        "provider": baseOrganization,
        "serviceType": "Space Research Services",
        "areaServed": "Worldwide",
        ...customData
      };
    
    case 'contactPage':
      return {
        "@context": "https://schema.org",
        "@type": "ContactPage",
        "mainEntity": baseOrganization,
        ...customData
      };
    
    case 'aboutPage':
      return {
        "@context": "https://schema.org",
        "@type": "AboutPage",
        "mainEntity": baseOrganization,
        ...customData
      };
    
    default:
      return {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "publisher": baseOrganization,
        ...customData
      };
  }
};

// Generate breadcrumbs for any page
export const generateBreadcrumbs = (path) => {
  const breadcrumbs = [{ name: "Home", url: siteConfig.siteUrl }];
  
  if (path !== '/') {
    const pathSegments = path.split('/').filter(segment => segment);
    let currentPath = '';
    
    pathSegments.forEach(segment => {
      currentPath += `/${segment}`;
      const pageName = segment.charAt(0).toUpperCase() + segment.slice(1).replace('-', ' ');
      breadcrumbs.push({
        name: pageName,
        url: `${siteConfig.siteUrl}${currentPath}`
      });
    });
  }
  
  return breadcrumbs;
};

export default {
  siteConfig,
  pageConfigs,
  generateStructuredData,
  generateBreadcrumbs
};
