# Apache configuration for React SPA deployment on cPanel/hPanel
# This file handles client-side routing and optimizations

<IfModule mod_rewrite.c>
  RewriteEngine On

  # Handle client-side routing for React Router
  # If an existing asset or directory is requested go to it as it is
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteCond %{REQUEST_URI} !^/assets/
  RewriteCond %{REQUEST_URI} !^/src/
  RewriteRule ^.*$ /index.html [L]

  # Redirect old hash-based URLs to clean URLs
  RewriteCond %{QUERY_STRING} ^(.*)$
  RewriteRule ^#/(.*)$ /$1? [R=301,L]

  # Remove trailing slashes (optional)
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteCond %{THE_REQUEST} /+[^?\s]*?/[\s?]
  RewriteRule ^(.+)/$ /$1 [R=301,L]
</IfModule>

# Security headers for enhanced protection
<IfModule mod_headers.c>
  # Prevent MIME type sniffing
  Header always set X-Content-Type-Options nosniff

  # Enable XSS protection
  Header always set X-XSS-Protection "1; mode=block"

  # Prevent clickjacking
  Header always set X-Frame-Options DENY

  # Referrer policy
  Header always set Referrer-Policy "strict-origin-when-cross-origin"

  # Content Security Policy (basic)
  Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:;"
</IfModule>

# Enhanced compression for better performance
<IfModule mod_deflate.c>
  # Compress HTML, CSS, JavaScript, Text, XML and fonts
  AddOutputFilterByType DEFLATE application/javascript
  AddOutputFilterByType DEFLATE application/rss+xml
  AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
  AddOutputFilterByType DEFLATE application/x-font
  AddOutputFilterByType DEFLATE application/x-font-opentype
  AddOutputFilterByType DEFLATE application/x-font-otf
  AddOutputFilterByType DEFLATE application/x-font-truetype
  AddOutputFilterByType DEFLATE application/x-font-ttf
  AddOutputFilterByType DEFLATE application/x-javascript
  AddOutputFilterByType DEFLATE application/xhtml+xml
  AddOutputFilterByType DEFLATE application/xml
  AddOutputFilterByType DEFLATE font/opentype
  AddOutputFilterByType DEFLATE font/otf
  AddOutputFilterByType DEFLATE font/ttf
  AddOutputFilterByType DEFLATE image/svg+xml
  AddOutputFilterByType DEFLATE image/x-icon
  AddOutputFilterByType DEFLATE text/css
  AddOutputFilterByType DEFLATE text/html
  AddOutputFilterByType DEFLATE text/javascript
  AddOutputFilterByType DEFLATE text/plain
  AddOutputFilterByType DEFLATE text/xml
</IfModule>

# Enhanced browser caching for better performance
<IfModule mod_expires.c>
  ExpiresActive on

  # Images - cache for 1 month
  ExpiresByType image/jpg "access plus 1 month"
  ExpiresByType image/jpeg "access plus 1 month"
  ExpiresByType image/gif "access plus 1 month"
  ExpiresByType image/png "access plus 1 month"
  ExpiresByType image/webp "access plus 1 month"
  ExpiresByType image/svg+xml "access plus 1 month"
  ExpiresByType image/x-icon "access plus 1 year"

  # CSS and JavaScript - cache for 1 month
  ExpiresByType text/css "access plus 1 month"
  ExpiresByType application/javascript "access plus 1 month"
  ExpiresByType text/javascript "access plus 1 month"

  # Fonts - cache for 1 year
  ExpiresByType font/woff "access plus 1 year"
  ExpiresByType font/woff2 "access plus 1 year"
  ExpiresByType application/font-woff "access plus 1 year"
  ExpiresByType application/font-woff2 "access plus 1 year"

  # HTML - no cache
  ExpiresByType text/html "access plus 0 seconds"
</IfModule>

# Cache control headers for fine-grained control
<IfModule mod_headers.c>
  # Cache static assets for 1 month
  <FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|svg|woff|woff2|ttf|eot)$">
    Header set Cache-Control "max-age=2592000, public"
  </FilesMatch>

  # Don't cache HTML files
  <FilesMatch "\.(html|htm)$">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires 0
  </FilesMatch>
</IfModule>

# MIME types for modern file formats
<IfModule mod_mime.c>
  AddType image/webp .webp
  AddType font/woff .woff
  AddType font/woff2 .woff2
  AddType application/font-woff .woff
  AddType application/font-woff2 .woff2
</IfModule>

# Prevent access to sensitive files
<FilesMatch "(^#.*#|\.(bak|config|dist|fla|inc|ini|log|psd|sh|sql|sw[op])|~)$">
  Order allow,deny
  Deny from all
  Satisfy All
</FilesMatch>

# Force HTTPS (uncomment if you have SSL certificate)
# <IfModule mod_rewrite.c>
#   RewriteCond %{HTTPS} off
#   RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
# </IfModule>
