import{j as e}from"./main-yODui1Lp.js";import{b as s,L as a,e as t}from"./vendor-react-DkG9wxl6.js";import{h as i}from"./hero-background-iGDmX_zv.js";import{P as r}from"./PartnershipsSectionContainer-CCUHpTwI.js";import{a as n}from"./avatar-square-CmKYi4da.js";import{a as o}from"./arrow-icon-BgG77x6T.js";import{B as c}from"./BookMission-DYuXxTbF.js";import{S as l}from"./SEO-CnfzhZqe.js";import"./vendor-utils-DOb1KAbh.js";import"./CalButton-Tnfz923M.js";const d="_header_1rcho_2",u="_heroContainer_1rcho_12",h="_rightContent_1rcho_21",_="_brandText_1rcho_31",m="_brandName_1rcho_39",g="_tagline_1rcho_49",p="_buttonWrapper_1rcho_58",v="_exploreButton_1rcho_62",x="_bottomContent_1rcho_89",j="_mainText_1rcho_120",f={container:"_container_1q8lp_1",loaded:"_loaded_1q8lp_9",fixed:"_fixed_1q8lp_13",overlay:"_overlay_1q8lp_18"},b=({src:a,children:t,className:i="",placeholderColor:r,position:n="center",size:o="cover",fixed:c=!1,overlay:l=!1,overlayOpacity:d=.5,minHeight:u="300px",...h})=>{const[_,m]=s.useState(!1),[g,p]=s.useState(!1),v=s.useRef(null),x=((e,s={})=>{const{sizes:a=[320,640,960,1280,1920],formats:t=["webp","jpg","png"],quality:i=80,baseUrl:r=""}=s;if("object"==typeof e&&e.default)return{original:e.default||e,webp:e.default||e,fallback:e.default||e,srcSet:"",sizes:""};if("string"==typeof e&&(e.startsWith("http://")||e.startsWith("https://")||e.startsWith("data:")||e.startsWith("blob:")))return{original:e,webp:e,fallback:e,srcSet:"",sizes:""};if("string"!=typeof e)return{original:e,webp:e,fallback:e,srcSet:"",sizes:""};const n=e.lastIndexOf("."),o=e.lastIndexOf("/"),c=n>o?e.slice(n+1):"jpg";let l=n>o?e.slice(0,n):e;const d=/\-\d+$/;d.test(l)&&(l=l.replace(d,""));const u=["jpg","jpeg","png"].includes(c.toLowerCase())?c:"jpg";return{original:e,webp:`${l}.webp`,fallback:`${l}.${u}`,webpSrcSet:a.map((e=>`${l}-${e}.webp ${e}w`)).join(", "),fallbackSrcSet:a.map((e=>`${l}-${e}.${u} ${e}w`)).join(", "),sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}})(a),j=r||(e=>{let s=0;for(let a=0;a<e.length;a++)s=(s<<5)-s+e.charCodeAt(a),s&=s;return`hsl(${Math.abs(s)%360}, 70%, 80%)`})(a);s.useEffect((()=>{const e=new IntersectionObserver((s=>{s[0].isIntersecting&&(p(!0),e.disconnect())}),{rootMargin:"200px",threshold:.01});return v.current&&e.observe(v.current),()=>{v.current&&e.unobserve(v.current),e.disconnect()}}),[]),s.useEffect((()=>{if(!g)return;const e=new Image;e.src=x.webp,e.onload=()=>{m(!0)},e.onerror=()=>{const e=new Image;e.src=x.fallback,e.onload=()=>{m(!0)}}}),[g,x.webp,x.fallback]);const b=[f.container,_?f.loaded:"",c?f.fixed:"",l?f.overlay:"",i].filter(Boolean).join(" "),w={backgroundColor:j,minHeight:u,...g&&{backgroundImage:`url(${x.webp}), url(${x.fallback})`,backgroundPosition:n,backgroundSize:o,backgroundRepeat:"no-repeat"},...l&&{"--overlay-opacity":d},...h.style};return e.jsx("div",{ref:v,className:b,style:w,...h,children:t})},w="_modalOverlay_17qew_1",y="_modalContent_17qew_15",N="_closeButton_17qew_26",C="_videoContainer_17qew_49",S=({isOpen:a,onClose:t,videoId:i})=>{const r=s.useRef(null);return s.useEffect((()=>{const e=e=>{r.current&&!r.current.contains(e.target)&&t()};return a&&(document.addEventListener("mousedown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("mousedown",e),document.body.style.overflow="auto"}}),[a,t]),s.useEffect((()=>{const e=e=>{"Escape"===e.key&&t()};return a&&document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}),[a,t]),a?e.jsx("div",{className:w,children:e.jsxs("div",{className:y,ref:r,children:[e.jsx("button",{className:N,onClick:t,"aria-label":"Close video",children:"×"}),e.jsx("div",{className:C,children:e.jsx("iframe",{src:`https://www.youtube.com/embed/${i}?autoplay=1&mute=1&loop=1&playlist=${i}&controls=0&showinfo=0&rel=0`,title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})})]})}):null},k="_playButton_nizm2_1",L="_buttonInner_nizm2_27",I="_playIcon_nizm2_39",T="_ringAnimation_nizm2_50",A=({onClick:s})=>e.jsxs("button",{className:k,onClick:s,"aria-label":"Play video",children:[e.jsx("div",{className:L,children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:I,children:e.jsx("path",{d:"M8 5V19L19 12L8 5Z",fill:"white",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("div",{className:T})]}),z=()=>{const[a,t]=s.useState(!1),[r,n]=s.useState(!1);s.useEffect((()=>{const e=()=>{n(window.innerWidth<=768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]);const o=e=>{e.preventDefault(),t(!0)},c=r?"/assets/jpg/mobliehr3-BTstzeRM.jpg":i;return e.jsxs("header",{id:"header",className:d,children:[e.jsxs(b,{src:c,className:u,minHeight:"100vh",position:"center",size:"cover",overlay:!0,overlayOpacity:.3,children:[e.jsxs("div",{className:h,children:[e.jsxs("div",{className:_,children:[e.jsx("span",{className:m,children:"ResearchSat"}),e.jsx("span",{className:g,children:"empowers your space biology research with seamless, end-to-end solutions"})]}),e.jsx("div",{className:p,children:e.jsx("a",{href:"#",className:v,onClick:o,children:"Explore Payloads"})})]}),e.jsx("div",{className:x,children:e.jsxs("div",{className:j,children:[e.jsx("span",{children:"Satellites"}),e.jsx("span",{children:"for"}),e.jsx("br",{}),e.jsx("span",{children:"life sciences"})]})}),e.jsx(A,{onClick:o})]}),e.jsx(S,{isOpen:a,onClose:()=>{t(!1)},videoId:"Ud6-gEj7wow"})]})},$="_spaceSection_ljiwx_2",E="_container_ljiwx_12",M="_leftContent_ljiwx_22",B="_microbiologyText_ljiwx_29",R="_spaceText_ljiwx_38",D="_spaceHighlight_ljiwx_46",O="_rightContent_ljiwx_51",q="_partnerTitle_ljiwx_59",P="_description_ljiwx_67",F="_partnerLogosContainer_ljiwx_76",W="_partnerLogos_ljiwx_76",H="_logoItem_ljiwx_102",U="_partnerLogo_ljiwx_76",G=()=>{const s=["/assets/png/SMA-BbXiAbMb.png","/assets/png/sasic-CpR6ZEto.png","/assets/png/agnikul-C9Q7sFQ6.png","/assets/svg/Hex-LTsHZtQH.svg","/assets/png/Asro-By4hOpiV.png","/assets/png/ssc-C4G9DMWh.png","/assets/png/unisa-DYeTDLBg.png","/assets/png/state-DKZrOqy3.png","/assets/png/onnes-CmT-Ve1Q.png","/assets/png/iCC-DTRpc3jY.png","/assets/svg/adluni-DuipHg1C.svg","/assets/png/OrbitalParadigm-CyoQpD-i.png","/assets/png/aicraft-CjYDFZom.png","/assets/png/cmex-coqzEvxE.png","/assets/png/badge-DFCnB-JG.png","/assets/png/sandc-XLO8T00w.png"],a=[...s,...s];return e.jsx("section",{className:$,children:e.jsxs("div",{className:E,children:[e.jsxs("div",{className:M,children:[e.jsx("div",{className:B,children:"_Microbiology"}),e.jsxs("h2",{className:R,children:[e.jsx("span",{className:D,children:"Space"})," provides a unique environment that challenges the fluid dynamics affecting the living cell dynamics."]})]}),e.jsxs("div",{className:O,children:[e.jsx("h3",{className:q,children:"And we are your partner for end-to-end Space Biology Research"}),e.jsx("p",{className:P,children:"From tailor-made satellite payloads to a range of space mission types to provide a seamless, end-to-end Space Research Solutions"})]}),e.jsx("div",{className:F,children:e.jsx("div",{className:W,children:a.map(((s,a)=>e.jsx("div",{className:H,children:e.jsx("img",{src:s,alt:"Partner Logo "+(a%10+1),className:U,width:120,height:60})},a)))})})]})})},V="_benefitsSection_1uhvj_2",X="_container_1uhvj_14",Q="_header_1uhvj_23",Y="_benefitsLabel_1uhvj_32",Z="_benefitsTitle_1uhvj_40",K="_benefitsDescription_1uhvj_49",J="_successButton_1uhvj_59",ee="_bentoGrid_1uhvj_86",se="_card1_1uhvj_97",ae="_column2_1uhvj_118",te="_card2_1uhvj_127",ie="_card2Bg_1uhvj_146",re="_card3_1uhvj_157",ne="_card4_1uhvj_176",oe="_card5_1uhvj_197",ce="_cardContent_1uhvj_218",le="_cardTitle_1uhvj_228",de="_starsBackground_1uhvj_241",ue="_starsBackgroundSvg_1uhvj_254",he="_solarSystemSvg_1uhvj_274",_e="_waveBg_1uhvj_281",me="_maskGroup_1uhvj_290",ge=()=>{const[a,t]=s.useState(!1);return e.jsxs("section",{className:`${V} parallax-section`,children:[e.jsxs("div",{className:`${X} parallax-content`,children:[e.jsxs("div",{className:Q,children:[e.jsx("div",{className:Y,children:"_Benefits"}),e.jsx("h2",{className:Z,children:"Unlocking the Cosmos"}),e.jsx("p",{className:K,children:"ResearchSat offers a variety of cutting-edge capabilities and features designed to advance the field of space biology research."})]}),e.jsx("a",{href:"#",className:J,onClick:e=>{e.preventDefault(),t(!0)},children:"Success Stories"}),e.jsxs("div",{className:ee,children:[e.jsxs("div",{className:se,children:[e.jsx("div",{className:de,children:e.jsx("img",{src:"/src/assets/images/benefits/starysky.svg",alt:"Starry Sky",className:ue})}),e.jsx("div",{className:ce,children:e.jsx("div",{className:le,children:"Impactful & Intellectually Interesting Projects"})})]}),e.jsxs("div",{className:ae,children:[e.jsxs("div",{className:te,children:[e.jsx("img",{src:"/src/assets/images/background/checkedbg.svg",alt:"Checkered Background",className:ie}),e.jsx("div",{className:le,children:"Interdisciplinary Research"})]}),e.jsx("div",{className:re,children:e.jsx("div",{className:ce,children:e.jsx("div",{className:le,children:"New Knowledge Creation"})})})]}),e.jsx("div",{className:ne,children:e.jsxs("div",{className:ce,children:[e.jsx("div",{className:_e,children:e.jsx("img",{src:"/src/assets/images/benefits/solarsytem.svg",alt:"Solar System",className:he})}),e.jsx("div",{className:le,children:"Represent your industry sector in Space Sector"})]})}),e.jsx("div",{className:oe,children:e.jsxs("div",{className:ce,children:[e.jsx("div",{className:_e,children:e.jsx("img",{src:"/src/assets/images/benefits/mask-group0.svg",alt:"",className:me})}),e.jsx("div",{className:le,children:"Conduct cutting-edge research"})]})})]})]}),e.jsx(S,{isOpen:a,onClose:()=>{t(!1)},videoId:"EG5MEujVL7Y"})]})},pe={featuresSection:"_featuresSection_1u95h_2",container:"_container_1u95h_14",capabilitiesLabel:"_capabilitiesLabel_1u95h_23",featuresTitle:"_featuresTitle_1u95h_35",featureSlider:"_featureSlider_1u95h_48",featureCard:"_featureCard_1u95h_57",slideOut:"_slideOut_1u95h_64",featureContent:"_featureContent_1u95h_69",objectsIcon:"_objectsIcon_1u95h_78",featureTextContainer:"_featureTextContainer_1u95h_83",featureTitle:"_featureTitle_1u95h_91",featureDescription:"_featureDescription_1u95h_100",sliderIndicator:"_sliderIndicator_1u95h_109",sliderTrack:"_sliderTrack_1u95h_117",sliderProgress:"_sliderProgress_1u95h_124",seeAllButton:"_seeAllButton_1u95h_134",imageContainer:"_imageContainer_1u95h_161",featureImage:"_featureImage_1u95h_165",hardwareLabel:"_hardwareLabel_1u95h_177",boltIcon:"_boltIcon_1u95h_199",labelTextContainer:"_labelTextContainer_1u95h_204",labelText:"_labelText_1u95h_204",activeLabel:"_activeLabel_1u95h_224",inactiveLabel:"_inactiveLabel_1u95h_230"},ve=()=>{const t=[{id:1,icon:"/src/assets/images/features/objects.svg",title:"Microgravity platform",description:"A weightless environment is necessary for conducting cutting edge research on biological systems.",buttonText:"See All"},{id:2,icon:"/src/assets/images/features/objects0.svg",title:"Automated experiments",description:"Advanced automation technology is necessary to ensure that experiments are conducted remotely and with precision.",buttonText:"Learn More"},{id:3,icon:"/src/assets/images/features/objects1.svg",title:"Customizable platform",description:"The ability to customize the satellite platform to meet the specific needs of each client requirements for a successful research.",buttonText:"Explore Options"},{id:4,icon:"/src/assets/images/features/objects2.svg",title:"Comprehensive logistics support",description:"Conducting experiments in space requires comprehensive logistics support including launch services, transportation and integrations.",buttonText:"Contact Us"}],[i,r]=s.useState(0),[n,o]=s.useState(!1),c=["Space hardware for experiments","Affordable satellite platforms","Modular platforms"],[l,d]=s.useState(0);s.useEffect((()=>{const e=setInterval((()=>{u()}),7e3);return()=>clearInterval(e)}),[i]),s.useEffect((()=>{const e=setInterval((()=>{d((e=>e===c.length-1?0:e+1))}),3600);return()=>clearInterval(e)}),[]);const u=()=>{n||(o(!0),setTimeout((()=>{r((e=>e===t.length-1?0:e+1)),o(!1)}),500))},h=i/(t.length-1)*100+"%";return e.jsx("section",{className:`${pe.featuresSection} parallax-section`,children:e.jsxs("div",{className:`${pe.container} parallax-content`,children:[e.jsx("div",{className:pe.capabilitiesLabel,children:"_Capabilities"}),e.jsx("h2",{className:pe.featuresTitle,children:"Our Features"}),e.jsxs("div",{className:pe.featureSlider,children:[e.jsx("div",{className:`${pe.featureCard} ${n?pe.slideOut:""}`,children:e.jsxs("div",{className:pe.featureContent,children:[e.jsx("img",{src:t[i].icon,alt:"",className:pe.objectsIcon}),e.jsxs("div",{className:pe.featureTextContainer,children:[e.jsx("h3",{className:pe.featureTitle,children:t[i].title}),e.jsx("p",{className:pe.featureDescription,children:t[i].description})]})]})}),e.jsx("div",{className:pe.sliderIndicator,children:e.jsx("div",{className:pe.sliderTrack,children:e.jsx("div",{className:pe.sliderProgress,style:{width:h}})})})]}),e.jsx(a,{to:"/features",className:`${pe.seeAllButton} ${n?pe.buttonFade:""}`,children:t[i].buttonText||"See All"}),e.jsx("div",{className:pe.imageContainer,children:e.jsx("img",{src:"/src/assets/images/features/hubbletelescope.png",alt:"Hubble telescope in space",className:pe.featureImage})}),e.jsxs("div",{className:pe.hardwareLabel,children:[e.jsx("img",{src:"/src/assets/images/features/bolt.svg",alt:"",className:pe.boltIcon}),e.jsx("div",{className:pe.labelTextContainer,children:c.map(((s,a)=>e.jsx("div",{className:`${pe.labelText} ${a===l?pe.activeLabel:pe.inactiveLabel}`,children:s},`label-${a}`)))})]})]})})},xe="_partnershipsSection_fwoji_2",je="_container_fwoji_14",fe="_trustWallLabel_fwoji_23",be="_partnershipsTitle_fwoji_35",we="_viewAllButton_fwoji_48",ye="_cardsGrid_fwoji_76",Ne="_leftColumn_fwoji_91",Ce="_topRow_fwoji_104",Se="_card_fwoji_76",ke="_card1_fwoji_155",Le="_card2_fwoji_161",Ie="_cardIcon_fwoji_167",Te="_cardDetails_fwoji_180",Ae="_cardHeading_fwoji_192",ze="_cardCategory_fwoji_202",$e="_cardTitle_fwoji_213",Ee="_bottomCard_fwoji_229",Me="_rightColumn_fwoji_253",Be="_largeCard_fwoji_266",Re="_largeCardImage_fwoji_284",De="_cardOverlay_fwoji_312",Oe=()=>e.jsx("section",{className:`${xe} parallax-section`,children:e.jsxs("div",{className:`${je} parallax-content`,children:[e.jsx("div",{className:fe,children:"_Opportunities"}),e.jsx("h2",{className:be,children:"Our Missions"}),e.jsx(a,{to:"/missions",className:we,children:"View Details"}),e.jsxs("div",{className:ye,children:[e.jsxs("div",{className:Ne,children:[e.jsxs("div",{className:Ce,children:[e.jsxs("div",{className:`${Se} ${ke}`,children:[e.jsx("img",{src:"/src/assets/images/partnerships/diagonal-arrow1.svg",alt:"",className:Ie}),e.jsx("div",{className:Te,children:e.jsxs("div",{className:Ae,children:[e.jsx("div",{className:ze,children:"Missions"}),e.jsx("div",{className:$e,children:"Atmospheric Missions - Duration: 9 seconds"})]})})]}),e.jsxs("div",{className:`${Se} ${Le}`,children:[e.jsx("img",{src:"/src/assets/images/partnerships/diagonal-arrow2.svg",alt:"",className:Ie}),e.jsx("div",{className:Te,children:e.jsxs("div",{className:Ae,children:[e.jsx("div",{className:ze,children:"Missions"}),e.jsx("div",{className:$e,children:"Sub-Orbital Missions - Duration: 9 minutes"})]})})]})]}),e.jsx("div",{className:Ee,children:e.jsxs("div",{className:De,children:[e.jsx("div",{className:ze,children:"Missions"}),e.jsx("div",{className:$e,children:"Orbital Mission - Duration: 9 weeks"})]})})]}),e.jsx("div",{className:Me,children:e.jsxs("div",{className:Be,children:[e.jsx("img",{src:"/src/assets/images/partnerships/Missionastronaut.png",alt:"Astronaut on Mission",className:Re}),e.jsxs("div",{className:De,children:[e.jsx("div",{className:ze,children:"Missions"}),e.jsx("div",{className:$e,children:"ISS Missions - Duration: 9 Months"})]})]})})]})]})}),qe="_newsSection_1u1tv_2",Pe="_container_1u1tv_14",Fe="_newsLabel_1u1tv_23",We="_newsTitle_1u1tv_35",He="_viewAllButton_1u1tv_48",Ue="_newsContent_1u1tv_76",Ge="_featuredArticle_1u1tv_88",Ve="_authorImage_1u1tv_97",Xe="_authorName_1u1tv_122",Qe="_articleExcerpt_1u1tv_132",Ye="_readMoreLink_1u1tv_141",Ze="_newsCardsContainer_1u1tv_159",Ke="_navArrow_1u1tv_169",Je="_visible_1u1tv_189",es="_leftArrow_1u1tv_194",ss="_rightArrow_1u1tv_198",as="_newsCards_1u1tv_159",ts="_scrollIndicators_1u1tv_245",is="_scrollDot_1u1tv_252",rs="_active_1u1tv_263",ns="_newsCard_1u1tv_159",os="_cardIcon_1u1tv_298",cs="_cardDetails_1u1tv_313",ls="_cardCategory_1u1tv_321",ds="_cardTitle_1u1tv_330",us="_cardDate_1u1tv_344",hs="data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M15%2018L9%2012L15%206'%20stroke='white'%20stroke-width='2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/svg%3e",_s="data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M9%2018L15%2012L9%206'%20stroke='white'%20stroke-width='2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/svg%3e",ms="_lightboxOverlay_1y4z1_1",gs="_lightboxContainer_1y4z1_17",ps="_closeButton_1y4z1_29",vs="_lightboxContent_1y4z1_52",xs="_animating_1y4z1_60",js="_imageSection_1y4z1_64",fs="_cardImage_1y4z1_69",bs="_cardCategory_1y4z1_88",ws="_contentSection_1y4z1_101",ys="_headerContainer_1y4z1_111",Ns="_cardTitle_1y4z1_115",Cs="_cardDate_1y4z1_123",Ss="_authorContainer_1y4z1_129",ks="_authorImage_1y4z1_136",Ls="_authorName_1y4z1_150",Is="_cardContent_1y4z1_156",Ts="_buttonsContainer_1y4z1_194",As="_backButton_1y4z1_200",zs="_navArrow_1y4z1_222",$s="_prevArrow_1y4z1_239",Es="_nextArrow_1y4z1_243",Ms="_paginationIndicators_1y4z1_263",Bs="_indicator_1y4z1_274",Rs="_active_1y4z1_285",Ds=({isOpen:a,onClose:t,newsCards:i,initialIndex:r=0})=>{const[o,c]=s.useState(r),l=s.useRef(null),d=s.useRef(null),u=i[o]||{};s.useEffect((()=>{const e=e=>{l.current&&!l.current.contains(e.target)&&t()};return a&&(document.addEventListener("mousedown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("mousedown",e),document.body.style.overflow="auto"}}),[a,t]),s.useEffect((()=>{const e=e=>{if(a)switch(e.key){case"ArrowLeft":h();break;case"ArrowRight":_();break;case"Escape":t()}};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)}),[a,o,i.length,t]),s.useEffect((()=>{d.current&&(d.current.classList.add(xs),setTimeout((()=>{d.current&&d.current.classList.remove(xs)}),300))}),[o]);const h=()=>{c(o>0?o-1:i.length-1)},_=()=>{o<i.length-1?c(o+1):c(0)};return a?e.jsx("div",{className:ms,children:e.jsxs("div",{className:gs,ref:l,children:[e.jsx("button",{className:ps,onClick:t,"aria-label":"Close",children:"×"}),e.jsxs("div",{className:vs,ref:d,children:[e.jsx("div",{className:js,children:e.jsx("div",{className:fs,style:{backgroundImage:u.backgroundImage},children:e.jsx("div",{className:bs,children:u.category})})}),e.jsxs("div",{className:ws,children:[e.jsxs("div",{className:ys,children:[e.jsx("h2",{className:Ns,children:u.title}),e.jsx("div",{className:Cs,children:u.date})]}),e.jsxs("div",{className:Ss,children:[e.jsx("div",{className:ks,children:e.jsx("img",{src:n,alt:"Author"})}),e.jsx("div",{className:Ls,children:"ResearchSat Team"})]}),e.jsxs("div",{className:Is,children:[e.jsx("p",{children:u.content||"ResearchSat is pioneering the future of space biology research, enabling groundbreaking discoveries in microgravity environments. Our innovative satellite platforms and bioreactors are unlocking new possibilities for pharmaceutical development, regenerative medicine, and sustainable technologies."}),e.jsx("p",{children:u.additionalContent||"By leveraging the unique conditions of microgravity, we're accelerating scientific breakthroughs that have the potential to transform healthcare, agriculture, and materials science on Earth."})]}),e.jsx("div",{className:Ts,children:e.jsx("button",{className:As,onClick:t,children:"Close"})})]})]}),e.jsx("button",{className:`${zs} ${$s}`,onClick:h,"aria-label":"Previous article",children:e.jsx("img",{src:hs,alt:"Previous"})}),e.jsx("button",{className:`${zs} ${Es}`,onClick:_,"aria-label":"Next article",children:e.jsx("img",{src:_s,alt:"Next"})}),e.jsx("div",{className:Ms,children:i.map(((s,a)=>e.jsx("button",{className:`${Bs} ${a===o?Rs:""}`,onClick:()=>c(a),"aria-label":`Go to article ${a+1}`},a)))})]})}):null},Os=()=>{const i=t(),r=s.useRef(null),[c,l]=s.useState(!1),[d,u]=s.useState(0),[h,_]=s.useState(0),[m,g]=s.useState(0),[p,v]=s.useState(!1),[x,j]=s.useState(!0),[f,b]=s.useState(!1),[w,y]=s.useState(!1),[N,C]=s.useState(0),S=()=>{if(r.current){const e=r.current,s=e.scrollWidth-e.clientWidth;v(e.scrollLeft>20),j(e.scrollLeft<s-20);const a=305,t=16,i=Math.round(e.scrollLeft/(a+t));g(i)}};s.useEffect((()=>(S(),window.addEventListener("resize",S),()=>{window.removeEventListener("resize",S)})),[]),s.useEffect((()=>{const e=r.current;if(e){let s=!1;const a=()=>{s||(s=!0,window.requestAnimationFrame((()=>{S(),s=!1})))};return e.addEventListener("scroll",a,{passive:!0}),()=>{e.removeEventListener("scroll",a)}}}),[]),s.useEffect((()=>{const e=()=>{b(window.innerWidth<=768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]);const k=(e,s)=>{e.preventDefault(),f?i("/news"):(e=>{C(e),y(!0),document.body.style.overflow="hidden"})(s)},L=[{id:1,category:"Partnership",title:"Cryogenics Alliance: ResearchSat × Onnes Cryogenics",date:"26 Mar 2025",backgroundImage:"linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=1000)",content:"ResearchSat announced a strategic partnership with Dutch‑Australian cryogenic pioneer Onnes Cryogenics to integrate advanced zero‑boil‑off cooling into forthcoming bioreactor satellites, enabling temperature‑sensitive pharma workflows in orbit.",additionalContent:"The accord was celebrated during a South‑Australian trade mission to Hyderabad, marking a significant step forward in our mission to advance space biology research capabilities."},{id:2,category:"Media",title:"Lot Fourteen Feature on Microgravity Breakthroughs",date:"21 Apr 2025",backgroundImage:"linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(https://images.unsplash.com/photo-1446776811953-b23d57bd21aa?q=80&w=1000)",content:"Adelaide's Lot Fourteen innovation precinct profiled ResearchSat's expansion in South Australia, emphasising plans to scale bacteria‑based experiments and orbital manufacturing after earlier sub‑orbital successes.",additionalContent:"The feature highlights our growing presence in the South Australian space ecosystem and our contributions to advancing microgravity research capabilities."},{id:3,category:"Partnership",title:"Edge-AI MoU with AICRAFT",date:"17 Apr 2025",backgroundImage:"linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(https://images.unsplash.com/photo-1614728894747-a83421e2b9c9?q=80&w=1000)",content:"A Memorandum of Understanding with edge‑computing firm AICRAFT will embed real‑time on‑orbit analytics into biological payloads, reducing downlink bandwidth and accelerating experiment iteration cycles.",additionalContent:"This collaboration represents a significant advancement in our data processing capabilities, allowing for more efficient and responsive space-based experiments."},{id:4,category:"Collaboration",title:"Strategic Collaboration: Cambrian Executive Partnership",date:"15 Apr 2025",backgroundImage:"linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(https://images.unsplash.com/photo-1454789548928-9efd52dc4031?q=80&w=1000)",content:"ResearchSat has entered into a strategic executive partnership with Cambrian Bio to explore microgravity-enabled drug discovery pathways for longevity therapeutics.",additionalContent:"This collaboration combines our expertise in space-based research platforms with Cambrian's pioneering work in longevity science, opening new frontiers in pharmaceutical development."},{id:5,category:"Article",title:"Thought Leadership Series — Space Bioreactors",date:"08 Mar 2025",backgroundImage:"linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(https://images.unsplash.com/photo-1516339901601-2e1b62dc0c45?q=80&w=1000)",content:"In a long‑form article, ResearchSat detailed its roadmap for in‑space tissue engineering, drug discovery, and food‑tech applications of its proprietary ADI‑Lab platform.",additionalContent:"The article explores how our innovative approach to space bioreactors is creating new possibilities across multiple industries, from pharmaceuticals to sustainable food production."},{id:6,category:"Research",title:"Expert Insights: Regenerative Medicine Post",date:"19 Mar 2025",backgroundImage:"linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(https://images.unsplash.com/photo-1581822261290-991b38693d1b?q=80&w=1000)",content:"A widely‑shared LinkedIn post explored how microgravity modulates stem‑cell pathways, positioning space bioprocessing as a cornerstone for next‑gen therapies.",additionalContent:"Our research team's insights into the unique cellular behaviors observed in microgravity environments are helping to advance the field of regenerative medicine and opening new therapeutic possibilities."},{id:7,category:"Interview",title:"In-Depth Founder Interview in Biostache",date:"24 Feb 2025",backgroundImage:"linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(https://images.unsplash.com/photo-1581291518633-83b4ebd1d83e?q=80&w=1000)",content:"Biostache magazine featured an extensive interview with ResearchSat's founders, discussing the journey from concept to orbital deployment and future visions for space-based biotechnology.",additionalContent:"The interview provides insights into our founding story, the challenges we've overcome, and our ambitious plans to revolutionize biological research through microgravity experimentation."},{id:8,category:"Recognition",title:'Industry Recognition: "Startups to Watch 2025" Listing',date:"Apr 2025",backgroundImage:"linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(https://images.unsplash.com/photo-1517976487492-5750f3195933?q=80&w=1000)",content:"SpaceConnect Online listed ResearchSat among Australia's five most innovative space ventures, citing AI‑driven feedback loops and scalable bioreactor design.",additionalContent:"This recognition highlights our position at the forefront of space innovation and acknowledges our contributions to advancing Australia's capabilities in the global space economy."}];return e.jsxs("section",{className:`${qe} parallax-section`,children:[e.jsxs("div",{className:`${Pe} parallax-content`,children:[e.jsx("div",{className:Fe,children:"_News"}),e.jsx("h2",{className:We,children:"Here's what's new with us"}),e.jsx(a,{to:"/news",className:He,children:"View All"}),e.jsxs("div",{className:Ue,children:[e.jsxs("div",{className:Ge,children:[e.jsx("div",{className:Ve,children:e.jsx("img",{src:n,alt:"John Kelly"})}),e.jsx("h3",{className:Xe,children:"ResearchSat Team"}),e.jsx("p",{className:Qe,children:"Unlocking the Future of Drug Discovery in Microgravity! By sending experiments into microgravity, we're achieving exceptional crystal quality that's hard to replicate on Earth..."}),e.jsx(a,{to:"/news/article/featured-1",className:Ye,children:"Read More"})]}),e.jsxs("div",{className:Ze,children:[e.jsx("button",{className:`${Ke} ${es} ${p?Je:""}`,onClick:()=>{if(!r.current)return;const e=r.current,s=Math.max(m-1,0);e.scrollTo({left:321*s,behavior:"smooth"}),g(s)},"aria-label":"Previous cards",children:e.jsx("img",{src:hs,alt:"Previous"})}),e.jsx("div",{className:as,ref:r,onMouseDown:e=>{r.current&&(l(!0),u(e.pageX-r.current.offsetLeft),_(r.current.scrollLeft),r.current.style.cursor="grabbing",r.current.style.userSelect="none")},onMouseMove:e=>{if(!c||!r.current)return;const s=2*(e.pageX-r.current.offsetLeft-d);r.current.scrollLeft=h-s},onMouseUp:()=>{l(!1),r.current&&(r.current.style.cursor="grab",r.current.style.removeProperty("user-select"))},onMouseLeave:()=>{c&&(l(!1),r.current&&(r.current.style.cursor="grab",r.current.style.removeProperty("user-select")))},onTouchStart:e=>{r.current&&(l(!0),u(e.touches[0].pageX-r.current.offsetLeft),_(r.current.scrollLeft))},onTouchMove:e=>{if(!c||!r.current)return;const s=2*(e.touches[0].pageX-r.current.offsetLeft-d);r.current.scrollLeft=h-s},onTouchEnd:()=>{l(!1)},children:L.map(((s,a)=>e.jsxs("div",{className:ns,style:{backgroundImage:s.backgroundImage},onClick:e=>k(e,a),children:[e.jsx("img",{src:o,alt:"",className:os}),e.jsxs("div",{className:cs,children:[e.jsx("div",{className:ls,children:s.category}),e.jsx("h4",{className:ds,children:s.title}),e.jsx("div",{className:us,children:s.date})]})]},s.id)))}),e.jsx("button",{className:`${Ke} ${ss} ${x?Je:""}`,onClick:()=>{if(!r.current)return;const e=r.current,s=L.length,a=Math.min(m+1,s-1);e.scrollTo({left:321*a,behavior:"smooth"}),g(a)},"aria-label":"Next cards",children:e.jsx("img",{src:_s,alt:"Next"})}),e.jsx("div",{className:ts,children:L.map(((s,a)=>e.jsx("button",{className:`${is} ${m===a?rs:""}`,onClick:()=>(e=>{if(!r.current)return;const s=321*e;r.current.scrollTo({left:s,behavior:"smooth"}),g(e)})(a),"aria-label":`Go to card ${a+1}`},a)))})]})]})]}),!f&&e.jsx(Ds,{isOpen:w,onClose:()=>{y(!1),document.body.style.overflow="auto"},newsCards:L,initialIndex:N})]})},qs=()=>{s.useEffect((()=>{window.scrollTo(0,0)}),[]);return e.jsxs(e.Fragment,{children:[e.jsx(l,{title:"Space Biology & Microgravity Research Solutions",description:"Unlock the potential of microgravity environments to advance life-science technologies and therapeutics with ResearchSat's custom satellite solutions and space research services.",keywords:["microgravity research","space biology","satellite payloads","life sciences","space medicine","protein crystallization","cell culture space","ISS research","orbital experiments","space biotechnology"],canonical:"https://researchsat.space",structuredData:{"@context":"https://schema.org","@type":"WebSite",name:"ResearchSat",url:"https://researchsat.space",description:"Leading provider of microgravity research solutions and space biology services",potentialAction:{"@type":"SearchAction",target:"https://researchsat.space/search?q={search_term_string}","query-input":"required name=search_term_string"},publisher:{"@type":"Organization",name:"ResearchSat",logo:"https://researchsat.space/src/assets/images/new-logo.svg"}},breadcrumbs:[{name:"Home",url:"https://researchsat.space"}],ogType:"website"}),e.jsx(z,{}),e.jsx(G,{}),e.jsx(ge,{}),e.jsx(ve,{}),e.jsx(Oe,{}),e.jsx(r,{}),e.jsx(Os,{}),e.jsx(c,{})]})};export{qs as default};
