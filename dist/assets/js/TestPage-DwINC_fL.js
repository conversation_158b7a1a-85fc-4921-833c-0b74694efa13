import{j as e}from"./main-CgIzQBkX.js";import{b as s}from"./vendor-react-DkG9wxl6.js";import{S as i}from"./SEO-DKPeNMo0.js";import"./vendor-utils-DOb1KAbh.js";const t="_testPage_yxa7h_3",a="_heroSection_yxa7h_10",n="_videoContainer_yxa7h_19",h="_videoWrapper_yxa7h_28",c="_videoBackground_yxa7h_37",r="_videoActive_yxa7h_46",l="_videoOverlay_yxa7h_51",o="_overlayHidden_yxa7h_66",d="_playButton_yxa7h_72",x="_heroContent_yxa7h_80",m="_contentContainer_yxa7h_90",v="_heroTitle_yxa7h_101",_="_heroSubtitle_yxa7h_111",y="_statsContainer_yxa7h_121",u="_statItem_yxa7h_128",j="_statNumber_yxa7h_132",p="_statLabel_yxa7h_142",g="_canvasSection_yxa7h_152",w="_canvasContainer_yxa7h_161",f="_canvas_yxa7h_152",N="_canvasContent_yxa7h_182",b="_canvasTitle_yxa7h_190",S="_canvasDescription_yxa7h_203",M="_canvasFeatures_yxa7h_213",R="_feature_yxa7h_220",C="_featureIcon_yxa7h_235",E=()=>{const[E,L]=s.useState(!1),[P,T]=s.useState(!1),A=s.useRef(null),$=s.useRef(null);s.useEffect((()=>{document.title="Test Page | ResearchSat",window.scrollTo(0,0)}),[]),s.useEffect((()=>{const e=A.current;if(!e)return;const s=e.getContext("2d");let i,t=0;const a=1e3/60,n=()=>{const i=e.getBoundingClientRect(),t=Math.min(window.devicePixelRatio,2);e.width=i.width*t,e.height=i.height*t,e.style.width=i.width+"px",e.style.height=i.height+"px",s.scale(t,t)};n(),window.addEventListener("resize",n);const h=[],c={x:0,y:0,isActive:!1};let r,l=0;const o=s=>{r||(r=setTimeout((()=>{const i=e.getBoundingClientRect();c.x=(s.clientX-i.left)*(e.width/i.width),c.y=(s.clientY-i.top)*(e.height/i.height),c.isActive=!0,r=null}),16))},d=()=>{c.isActive=!1};e.addEventListener("mousemove",o),e.addEventListener("mouseleave",d);class x{constructor(){const s=Math.min(window.devicePixelRatio,2);this.x=Math.random()*e.width/s,this.y=Math.random()*e.height/s,this.vx=.5*(Math.random()-.5),this.vy=.5*(Math.random()-.5),this.radius=2*Math.random()+1,this.opacity=.6*Math.random()+.3,this.hue=40*Math.random()+180,this.originalRadius=this.radius,this.pulseSpeed=.01*Math.random()+.005}update(){const s=Math.min(window.devicePixelRatio,2);if(this.x+=this.vx,this.y+=this.vy,this.x<0&&(this.x=e.width/s),this.x>e.width/s&&(this.x=0),this.y<0&&(this.y=e.height/s),this.y>e.height/s&&(this.y=0),this.radius=this.originalRadius+.3*Math.sin(l*this.pulseSpeed),c.isActive){const e=c.x-this.x,s=c.y-this.y,i=Math.sqrt(e*e+s*s);if(i<80){const t=(80-i)/80*.005;this.x-=e*t,this.y-=s*t}}}draw(){s.save(),s.globalAlpha=this.opacity,s.beginPath(),s.arc(this.x,this.y,this.radius,0,2*Math.PI),s.fillStyle=`hsl(${this.hue}, 60%, 60%)`,s.fill(),this.radius>1.5&&(s.shadowBlur=5,s.shadowColor=`hsl(${this.hue}, 60%, 60%)`,s.beginPath(),s.arc(this.x,this.y,.7*this.radius,0,2*Math.PI),s.fillStyle=`hsla(${this.hue}, 70%, 70%, 0.5)`,s.fill()),s.restore()}}for(let v=0;v<60;v++)h.push(new x);const m=n=>{if(n-t>=a){l+=.016;const i=Math.min(window.devicePixelRatio,2);if(s.clearRect(0,0,e.width/i,e.height/i),s.fillStyle="rgba(0, 23, 24, 0.02)",s.fillRect(0,0,e.width/i,e.height/i),Math.floor(60*l)%2==0){s.strokeStyle="rgba(16, 126, 125, 0.15)",s.lineWidth=.5;for(let e=0;e<h.length;e+=2)for(let i=e+2;i<h.length;i+=2){const t=h[e].x-h[i].x,a=h[e].y-h[i].y,n=Math.sqrt(t*t+a*a);if(n<60){const t=(60-n)/60*.2;s.save(),s.globalAlpha=t,s.beginPath(),s.moveTo(h[e].x,h[e].y),s.lineTo(h[i].x,h[i].y),s.stroke(),s.restore()}}}h.forEach((e=>{e.update(),e.draw()})),t=n}i=requestAnimationFrame(m)};return m(0),()=>{window.removeEventListener("resize",n),e.removeEventListener("mousemove",o),e.removeEventListener("mouseleave",d),r&&clearTimeout(r),i&&cancelAnimationFrame(i)}}),[]);const B=(e=>{const s=e.match(/^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/);return s&&11===s[2].length?s[2]:null})("https://youtu.be/Ud6-gEj7wow?list=TLGGLtNQLdmT8SoyNjA1MjAyNQ");return e.jsxs(e.Fragment,{children:[e.jsx(i,{title:"Test Page | ResearchSat",description:"Test page with video hero section and interactive canvas elements",keywords:["test","video","canvas","space biotech"]}),e.jsxs("div",{className:t,children:[e.jsx("section",{className:a,children:e.jsxs("div",{className:n,children:[e.jsx("div",{className:h,children:e.jsx("iframe",{ref:$,className:`${c} ${E?r:""}`,src:`https://www.youtube.com/embed/${B}?autoplay=1&mute=1&loop=1&playlist=${B}&controls=${E?1:0}&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1`,title:"ResearchSat Video",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0,onLoad:()=>{T(!0)}})}),e.jsx("div",{className:`${l} ${E?o:""}`,onClick:()=>{L(!0)},children:e.jsx("div",{className:d,children:e.jsxs("svg",{width:"80",height:"80",viewBox:"0 0 80 80",fill:"none",children:[e.jsx("circle",{cx:"40",cy:"40",r:"40",fill:"rgba(188, 216, 213, 0.9)"}),e.jsx("path",{d:"M32 25L55 40L32 55V25Z",fill:"#001718"})]})})}),e.jsx("div",{className:x,children:e.jsxs("div",{className:m,children:[e.jsxs("h1",{className:v,children:["Space biotech",e.jsx("br",{}),"for a better life"]}),e.jsx("p",{className:_,children:"We leverage microgravity to create"}),e.jsxs("div",{className:y,children:[e.jsxs("div",{className:u,children:[e.jsx("div",{className:j,children:"151"}),e.jsxs("div",{className:p,children:["Space labs launched",e.jsx("br",{}),"by our team"]})]}),e.jsxs("div",{className:u,children:[e.jsx("div",{className:j,children:"61"}),e.jsxs("div",{className:p,children:["Customers on 4",e.jsx("br",{}),"continents"]})]}),e.jsxs("div",{className:u,children:[e.jsx("div",{className:j,children:"138"}),e.jsxs("div",{className:p,children:["Space biology",e.jsx("br",{}),"publications"]})]})]})]})})]})}),e.jsx("section",{className:g,children:e.jsxs("div",{className:w,children:[e.jsx("canvas",{ref:A,className:f,"data-engine":"three.js r147"}),e.jsxs("div",{className:N,children:[e.jsx("h2",{className:b,children:"Interactive Space Technology"}),e.jsx("p",{className:S,children:"Explore our interconnected network of space-based research capabilities. Each particle represents a breakthrough in microgravity science, connected through our innovative technology platform."}),e.jsxs("div",{className:M,children:[e.jsxs("div",{className:R,children:[e.jsx("div",{className:C,children:"🔬"}),e.jsx("h3",{children:"Advanced Research"}),e.jsx("p",{children:"Cutting-edge experiments in microgravity environments"})]}),e.jsxs("div",{className:R,children:[e.jsx("div",{className:C,children:"🚀"}),e.jsx("h3",{children:"Mission Planning"}),e.jsx("p",{children:"Comprehensive mission design and execution"})]}),e.jsxs("div",{className:R,children:[e.jsx("div",{className:C,children:"🧬"}),e.jsx("h3",{children:"Biotechnology"}),e.jsx("p",{children:"Revolutionary biological discoveries in space"})]})]})]})]})})]})]})};export{E as default};
