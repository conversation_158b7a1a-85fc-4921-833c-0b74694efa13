const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/HomePage-DGkHi5g0.js","assets/js/vendor-react-DkG9wxl6.js","assets/js/hero-background-iGDmX_zv.js","assets/js/PartnershipsSectionContainer-BM5-oK84.js","assets/css/PartnershipsSectionContainer-CingN96U.css","assets/js/avatar-square-CmKYi4da.js","assets/js/arrow-icon-BgG77x6T.js","assets/js/BookMission-CLg2k6W0.js","assets/js/CalButton-BDenXGt5.js","assets/css/BookMission-iJha7DC2.css","assets/js/SEO-B3bIlZbv.js","assets/js/vendor-utils-DOb1KAbh.js","assets/css/HomePage-CIcoxzfO.css","assets/js/AboutPage-DiqxacV1.js","assets/js/SectionDivider-B14-s4wf.js","assets/css/SectionDivider-Ccx4UF4q.css","assets/js/hero-background-D1hGMsA4.js","assets/css/AboutPage-kyOkVN3s.css","assets/js/FeaturesPage-DK2Hvggj.js","assets/css/FeaturesPage-CezYsF5n.css","assets/js/PayloadsPage-3yeaJz2w.js","assets/js/serv1-Bb5bIGGW.js","assets/css/PayloadsPage-DlS7ZIBj.css","assets/js/MissionsPage-CsndvgYN.js","assets/js/hero-background-DVoA4ozo.js","assets/js/mission_3-DRuGaD7I.js","assets/css/mission_3-DOO511LJ.css","assets/css/MissionsPage-DFjEQebr.css","assets/js/PastMissionsPage-CHjHD8aV.js","assets/css/PastMissionsPage-C7UBcOj1.css","assets/js/CareersPage-BBvVkwf0.js","assets/css/CareersPage-7qrgowqo.css","assets/js/ContactPage-CaEFMZJD.js","assets/css/ContactPage-Aqdm03PN.css","assets/js/PartnershipsPage-C7HcO-4m.js","assets/css/PartnershipsPage-CEYSZc8-.css","assets/js/NewsPage-BzFB5KYq.js","assets/css/NewsPage-B5wIpVel.css","assets/js/NewsArticlePage-CN9FHy0C.js","assets/css/NewsArticlePage-DXYI5Qdf.css","assets/js/PrivacyPolicyPage-nNqoRAAZ.js","assets/js/TermsConditionsPage-L3ng8iYH.js","assets/js/EmailSignPage-DIeKiie_.js","assets/js/TestPage-C7FTlaxU.js","assets/css/TestPage-DduXOArH.css","assets/js/BookMissionPage-Ba9VIrgu.js","assets/js/spacexperiment-email-DxDf5037.js","assets/css/BookMissionPage-DhG1_2b6.css","assets/js/SpaceXperimentPage-CxkWPAk9.js","assets/css/SpaceXperimentPage-CTpI4VuR.css","assets/js/OfferingsPage-BfPJHwp-.js","assets/css/OfferingsPage-BujR1ZHB.css","assets/js/NotFoundPage-CjZzi6sk.js","assets/js/sliders-FN4r_CE7.js"])))=>i.map(i=>d[i]);
var e,t,n=Object.defineProperty,r=(e,t,r)=>((e,t,r)=>t in e?n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r)(e,"symbol"!=typeof t?t+"":t,r);import{r as i,a,g as o,b as l,R as s,L as u,B as c,u as d,c as f,d as p}from"./vendor-react-DkG9wxl6.js";import{r as h,a as m}from"./vendor-utils-DOb1KAbh.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var g,v,b={exports:{}},y={};var _,w,k,x,S=(v||(v=1,b.exports=function(){if(g)return y;g=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.fragment");function n(t,n,r){var i=null;if(void 0!==r&&(i=""+r),void 0!==n.key&&(i=""+n.key),"key"in n)for(var a in r={},n)"key"!==a&&(r[a]=n[a]);else r=n;return n=r.ref,{$$typeof:e,type:t,key:i,ref:void 0!==n?n:null,props:r}}return y.Fragment=t,y.jsx=n,y.jsxs=n,y}()),b.exports),E={exports:{}},C={},T={exports:{}},L={};function A(){return w||(w=1,T.exports=(_||(_=1,function(e){function t(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<i(a,t)))break e;e[r]=t,e[n]=a,n=r}}function n(e){return 0===e.length?null:e[0]}function r(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var l=2*(r+1)-1,s=e[l],u=l+1,c=e[u];if(0>i(s,n))u<a&&0>i(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[l]=n,r=l);else{if(!(u<a&&0>i(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(e.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var a=performance;e.unstable_now=function(){return a.now()}}else{var o=Date,l=o.now();e.unstable_now=function(){return o.now()-l}}var s=[],u=[],c=1,d=null,f=3,p=!1,h=!1,m=!1,g=!1,v="function"==typeof setTimeout?setTimeout:null,b="function"==typeof clearTimeout?clearTimeout:null,y="undefined"!=typeof setImmediate?setImmediate:null;function _(e){for(var i=n(u);null!==i;){if(null===i.callback)r(u);else{if(!(i.startTime<=e))break;r(u),i.sortIndex=i.expirationTime,t(s,i)}i=n(u)}}function w(e){if(m=!1,_(e),!h)if(null!==n(s))h=!0,x||(x=!0,k());else{var t=n(u);null!==t&&P(w,t.startTime-e)}}var k,x=!1,S=-1,E=5,C=-1;function T(){return!(!g&&e.unstable_now()-C<E)}function L(){if(g=!1,x){var t=e.unstable_now();C=t;var i=!0;try{e:{h=!1,m&&(m=!1,b(S),S=-1),p=!0;var a=f;try{t:{for(_(t),d=n(s);null!==d&&!(d.expirationTime>t&&T());){var o=d.callback;if("function"==typeof o){d.callback=null,f=d.priorityLevel;var l=o(d.expirationTime<=t);if(t=e.unstable_now(),"function"==typeof l){d.callback=l,_(t),i=!0;break t}d===n(s)&&r(s),_(t)}else r(s);d=n(s)}if(null!==d)i=!0;else{var c=n(u);null!==c&&P(w,c.startTime-t),i=!1}}break e}finally{d=null,f=a,p=!1}i=void 0}}finally{i?k():x=!1}}}if("function"==typeof y)k=function(){y(L)};else if("undefined"!=typeof MessageChannel){var A=new MessageChannel,N=A.port2;A.port1.onmessage=L,k=function(){N.postMessage(null)}}else k=function(){v(L,0)};function P(t,n){S=v((function(){t(e.unstable_now())}),n)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_forceFrameRate=function(e){0>e||125<e||(E=0<e?Math.floor(1e3/e):5)},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},e.unstable_requestPaint=function(){g=!0},e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},e.unstable_scheduleCallback=function(r,i,a){var o=e.unstable_now();switch(a="object"==typeof a&&null!==a&&"number"==typeof(a=a.delay)&&0<a?o+a:o,r){case 1:var l=-1;break;case 2:l=250;break;case 5:l=**********;break;case 4:l=1e4;break;default:l=5e3}return r={id:c++,callback:i,priorityLevel:r,startTime:a,expirationTime:l=a+l,sortIndex:-1},a>o?(r.sortIndex=a,t(u,r),null===n(s)&&r===n(u)&&(m?(b(S),S=-1):m=!0,P(w,a-o))):(r.sortIndex=l,t(s,r),h||p||(h=!0,x||(x=!0,k()))),r},e.unstable_shouldYield=T,e.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}}(L)),L)),T.exports}
/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function N(){if(k)return C;k=1;var e=A(),t=i(),n=a();function r(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function l(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function s(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function u(e){if(l(e)!==e)throw Error(r(188))}function c(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=c(e)))return t;e=e.sibling}return null}var d=Object.assign,f=Symbol.for("react.element"),p=Symbol.for("react.transitional.element"),h=Symbol.for("react.portal"),m=Symbol.for("react.fragment"),g=Symbol.for("react.strict_mode"),v=Symbol.for("react.profiler"),b=Symbol.for("react.provider"),y=Symbol.for("react.consumer"),_=Symbol.for("react.context"),w=Symbol.for("react.forward_ref"),x=Symbol.for("react.suspense"),S=Symbol.for("react.suspense_list"),E=Symbol.for("react.memo"),T=Symbol.for("react.lazy"),L=Symbol.for("react.activity"),N=Symbol.for("react.memo_cache_sentinel"),P=Symbol.iterator;function O(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=P&&e[P]||e["@@iterator"])?e:null}var j=Symbol.for("react.client.reference");function z(e){if(null==e)return null;if("function"==typeof e)return e.$$typeof===j?null:e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case m:return"Fragment";case v:return"Profiler";case g:return"StrictMode";case x:return"Suspense";case S:return"SuspenseList";case L:return"Activity"}if("object"==typeof e)switch(e.$$typeof){case h:return"Portal";case _:return(e.displayName||"Context")+".Provider";case y:return(e._context.displayName||"Context")+".Consumer";case w:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case E:return null!==(t=e.displayName||null)?t:z(e.type)||"Memo";case T:t=e._payload,e=e._init;try{return z(e(t))}catch(n){}}return null}var M=Array.isArray,D=t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,I=n.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,F={pending:!1,data:null,method:null,action:null},$=[],H=-1;function R(e){return{current:e}}function V(e){0>H||(e.current=$[H],$[H]=null,H--)}function B(e,t){H++,$[H]=e.current,e.current=t}var U=R(null),W=R(null),q=R(null),Q=R(null);function K(e,t){switch(B(q,t),B(W,e),B(U,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?od(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=ld(t=od(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}V(U),B(U,e)}function Y(){V(U),V(W),V(q)}function Z(e){null!==e.memoizedState&&B(Q,e);var t=U.current,n=ld(t,e.type);t!==n&&(B(W,e),B(U,n))}function X(e){W.current===e&&(V(U),V(W)),Q.current===e&&(V(Q),Zd._currentValue=F)}var G=Object.prototype.hasOwnProperty,J=e.unstable_scheduleCallback,ee=e.unstable_cancelCallback,te=e.unstable_shouldYield,ne=e.unstable_requestPaint,re=e.unstable_now,ie=e.unstable_getCurrentPriorityLevel,ae=e.unstable_ImmediatePriority,oe=e.unstable_UserBlockingPriority,le=e.unstable_NormalPriority,se=e.unstable_LowPriority,ue=e.unstable_IdlePriority,ce=e.log,de=e.unstable_setDisableYieldValue,fe=null,pe=null;function he(e){if("function"==typeof ce&&de(e),pe&&"function"==typeof pe.setStrictMode)try{pe.setStrictMode(fe,e)}catch(t){}}var me=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(ge(e)/ve|0)|0},ge=Math.log,ve=Math.LN2;var be=256,ye=4194304;function _e(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function we(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var i=0,a=e.suspendedLanes,o=e.pingedLanes;e=e.warmLanes;var l=134217727&r;return 0!==l?0!==(r=l&~a)?i=_e(r):0!==(o&=l)?i=_e(o):n||0!==(n=l&~e)&&(i=_e(n)):0!==(l=r&~a)?i=_e(l):0!==o?i=_e(o):n||0!==(n=r&~e)&&(i=_e(n)),0===i?0:0!==t&&t!==i&&0===(t&a)&&((a=i&-i)>=(n=t&-t)||32===a&&4194048&n)?t:i}function ke(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function xe(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function Se(){var e=be;return!(4194048&(be<<=1))&&(be=256),e}function Ee(){var e=ye;return!(62914560&(ye<<=1))&&(ye=4194304),e}function Ce(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Te(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Le(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-me(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Ae(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-me(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}function Ne(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Pe(e){return 2<(e&=-e)?8<e?134217727&e?32:268435456:8:2}function Oe(){var e=I.p;return 0!==e?e:void 0===(e=window.event)?32:ff(e.type)}var je=Math.random().toString(36).slice(2),ze="__reactFiber$"+je,Me="__reactProps$"+je,De="__reactContainer$"+je,Ie="__reactEvents$"+je,Fe="__reactListeners$"+je,$e="__reactHandles$"+je,He="__reactResources$"+je,Re="__reactMarker$"+je;function Ve(e){delete e[ze],delete e[Me],delete e[Ie],delete e[Fe],delete e[$e]}function Be(e){var t=e[ze];if(t)return t;for(var n=e.parentNode;n;){if(t=n[De]||n[ze]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=wd(e);null!==e;){if(n=e[ze])return n;e=wd(e)}return t}n=(e=n).parentNode}return null}function Ue(e){if(e=e[ze]||e[De]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function We(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(r(33))}function qe(e){var t=e[He];return t||(t=e[He]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Qe(e){e[Re]=!0}var Ke=new Set,Ye={};function Ze(e,t){Xe(e,t),Xe(e+"Capture",t)}function Xe(e,t){for(Ye[e]=t,e=0;e<t.length;e++)Ke.add(t[e])}var Ge,Je,et=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),tt={},nt={};function rt(e,t,n){if(i=t,G.call(nt,i)||!G.call(tt,i)&&(et.test(i)?nt[i]=!0:(tt[i]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var i}function it(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function at(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function ot(e){if(void 0===Ge)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ge=t&&t[1]||"",Je=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Ge+e+Je}var lt=!1;function st(e,t){if(!e||lt)return"";lt=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(i){var r=i}Reflect.construct(e,[],n)}else{try{n.call()}catch(a){r=a}e.call(n.prototype)}}else{try{throw Error()}catch(o){r=o}(n=e())&&"function"==typeof n.catch&&n.catch((function(){}))}}catch(l){if(l&&r&&"string"==typeof l.stack)return[l.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var a=r.DetermineComponentFrameRoot(),o=a[0],l=a[1];if(o&&l){var s=o.split("\n"),u=l.split("\n");for(i=r=0;r<s.length&&!s[r].includes("DetermineComponentFrameRoot");)r++;for(;i<u.length&&!u[i].includes("DetermineComponentFrameRoot");)i++;if(r===s.length||i===u.length)for(r=s.length-1,i=u.length-1;1<=r&&0<=i&&s[r]!==u[i];)i--;for(;1<=r&&0<=i;r--,i--)if(s[r]!==u[i]){if(1!==r||1!==i)do{if(r--,0>--i||s[r]!==u[i]){var c="\n"+s[r].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=r&&0<=i);break}}}finally{lt=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?ot(n):""}function ut(e){switch(e.tag){case 26:case 27:case 5:return ot(e.type);case 16:return ot("Lazy");case 13:return ot("Suspense");case 19:return ot("SuspenseList");case 0:case 15:return st(e.type,!1);case 11:return st(e.type.render,!1);case 1:return st(e.type,!0);case 31:return ot("Activity");default:return""}}function ct(e){try{var t="";do{t+=ut(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function dt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ft(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function pt(e){e._valueTracker||(e._valueTracker=function(e){var t=ft(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var i=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ht(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ft(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function mt(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var gt=/[\n"\\]/g;function vt(e){return e.replace(gt,(function(e){return"\\"+e.charCodeAt(0).toString(16)+" "}))}function bt(e,t,n,r,i,a,o,l){e.name="",null!=o&&"function"!=typeof o&&"symbol"!=typeof o&&"boolean"!=typeof o?e.type=o:e.removeAttribute("type"),null!=t?"number"===o?(0===t&&""===e.value||e.value!=t)&&(e.value=""+dt(t)):e.value!==""+dt(t)&&(e.value=""+dt(t)):"submit"!==o&&"reset"!==o||e.removeAttribute("value"),null!=t?_t(e,o,dt(t)):null!=n?_t(e,o,dt(n)):null!=r&&e.removeAttribute("value"),null==i&&null!=a&&(e.defaultChecked=!!a),null!=i&&(e.checked=i&&"function"!=typeof i&&"symbol"!=typeof i),null!=l&&"function"!=typeof l&&"symbol"!=typeof l&&"boolean"!=typeof l?e.name=""+dt(l):e.removeAttribute("name")}function yt(e,t,n,r,i,a,o,l){if(null!=a&&"function"!=typeof a&&"symbol"!=typeof a&&"boolean"!=typeof a&&(e.type=a),null!=t||null!=n){if(("submit"===a||"reset"===a)&&null==t)return;n=null!=n?""+dt(n):"",t=null!=t?""+dt(t):n,l||t===e.value||(e.value=t),e.defaultValue=t}r="function"!=typeof(r=null!=r?r:i)&&"symbol"!=typeof r&&!!r,e.checked=l?e.checked:!!r,e.defaultChecked=!!r,null!=o&&"function"!=typeof o&&"symbol"!=typeof o&&"boolean"!=typeof o&&(e.name=o)}function _t(e,t,n){"number"===t&&mt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function wt(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+dt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n)return e[i].selected=!0,void(r&&(e[i].defaultSelected=!0));null!==t||e[i].disabled||(t=e[i])}null!==t&&(t.selected=!0)}}function kt(e,t,n){null==t||((t=""+dt(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+dt(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function xt(e,t,n,i){if(null==t){if(null!=i){if(null!=n)throw Error(r(92));if(M(i)){if(1<i.length)throw Error(r(93));i=i[0]}n=i}null==n&&(n=""),t=n}n=dt(t),e.defaultValue=n,(i=e.textContent)===n&&""!==i&&null!==i&&(e.value=i)}function St(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var Et=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Ct(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"==typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!=typeof n||0===n||Et.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Tt(e,t,n){if(null!=t&&"object"!=typeof t)throw Error(r(62));if(e=e.style,null!=n){for(var i in n)!n.hasOwnProperty(i)||null!=t&&t.hasOwnProperty(i)||(0===i.indexOf("--")?e.setProperty(i,""):"float"===i?e.cssFloat="":e[i]="");for(var a in t)i=t[a],t.hasOwnProperty(a)&&n[a]!==i&&Ct(e,a,i)}else for(var o in t)t.hasOwnProperty(o)&&Ct(e,o,t[o])}function Lt(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var At=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Nt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Pt(e){return Nt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Ot=null;function jt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var zt=null,Mt=null;function Dt(e){var t=Ue(e);if(t&&(e=t.stateNode)){var n=e[Me]||null;e:switch(e=t.stateNode,t.type){case"input":if(bt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+vt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var i=n[t];if(i!==e&&i.form===e.form){var a=i[Me]||null;if(!a)throw Error(r(90));bt(i,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(t=0;t<n.length;t++)(i=n[t]).form===e.form&&ht(i)}break e;case"textarea":kt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&wt(e,!!n.multiple,t,!1)}}}var It=!1;function Ft(e,t,n){if(It)return e(t,n);It=!0;try{return e(t)}finally{if(It=!1,(null!==zt||null!==Mt)&&(Bu(),zt&&(t=zt,e=Mt,Mt=zt=null,Dt(t),e)))for(t=0;t<e.length;t++)Dt(e[t])}}function $t(e,t){var n=e.stateNode;if(null===n)return null;var i=n[Me]||null;if(null===i)return null;n=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(i=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!i;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(r(231,t,typeof n));return n}var Ht=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),Rt=!1;if(Ht)try{var Vt={};Object.defineProperty(Vt,"passive",{get:function(){Rt=!0}}),window.addEventListener("test",Vt,Vt),window.removeEventListener("test",Vt,Vt)}catch(Df){Rt=!1}var Bt=null,Ut=null,Wt=null;function qt(){if(Wt)return Wt;var e,t,n=Ut,r=n.length,i="value"in Bt?Bt.value:Bt.textContent,a=i.length;for(e=0;e<r&&n[e]===i[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===i[a-t];t++);return Wt=i.slice(e,1<t?1-t:void 0)}function Qt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Kt(){return!0}function Yt(){return!1}function Zt(e){function t(t,n,r,i,a){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=i,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(i):i[o]);return this.isDefaultPrevented=(null!=i.defaultPrevented?i.defaultPrevented:!1===i.returnValue)?Kt:Yt,this.isPropagationStopped=Yt,this}return d(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Kt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Kt)},persist:function(){},isPersistent:Kt}),t}var Xt,Gt,Jt,en={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},tn=Zt(en),nn=d({},en,{view:0,detail:0}),rn=Zt(nn),an=d({},nn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:gn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Jt&&(Jt&&"mousemove"===e.type?(Xt=e.screenX-Jt.screenX,Gt=e.screenY-Jt.screenY):Gt=Xt=0,Jt=e),Xt)},movementY:function(e){return"movementY"in e?e.movementY:Gt}}),on=Zt(an),ln=Zt(d({},an,{dataTransfer:0})),sn=Zt(d({},nn,{relatedTarget:0})),un=Zt(d({},en,{animationName:0,elapsedTime:0,pseudoElement:0})),cn=Zt(d({},en,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),dn=Zt(d({},en,{data:0})),fn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},pn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},hn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function mn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=hn[e])&&!!t[e]}function gn(){return mn}var vn=Zt(d({},nn,{key:function(e){if(e.key){var t=fn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Qt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?pn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:gn,charCode:function(e){return"keypress"===e.type?Qt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Qt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),bn=Zt(d({},an,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),yn=Zt(d({},nn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:gn})),_n=Zt(d({},en,{propertyName:0,elapsedTime:0,pseudoElement:0})),wn=Zt(d({},an,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),kn=Zt(d({},en,{newState:0,oldState:0})),xn=[9,13,27,32],Sn=Ht&&"CompositionEvent"in window,En=null;Ht&&"documentMode"in document&&(En=document.documentMode);var Cn=Ht&&"TextEvent"in window&&!En,Tn=Ht&&(!Sn||En&&8<En&&11>=En),Ln=String.fromCharCode(32),An=!1;function Nn(e,t){switch(e){case"keyup":return-1!==xn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Pn(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var On=!1;var jn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function zn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!jn[e.type]:"textarea"===t}function Mn(e,t,n,r){zt?Mt?Mt.push(r):Mt=[r]:zt=r,0<(t=qc(t,"onChange")).length&&(n=new tn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Dn=null,In=null;function Fn(e){Fc(e,0)}function $n(e){if(ht(We(e)))return e}function Hn(e,t){if("change"===e)return t}var Rn=!1;if(Ht){var Vn;if(Ht){var Bn="oninput"in document;if(!Bn){var Un=document.createElement("div");Un.setAttribute("oninput","return;"),Bn="function"==typeof Un.oninput}Vn=Bn}else Vn=!1;Rn=Vn&&(!document.documentMode||9<document.documentMode)}function Wn(){Dn&&(Dn.detachEvent("onpropertychange",qn),In=Dn=null)}function qn(e){if("value"===e.propertyName&&$n(In)){var t=[];Mn(t,In,e,jt(e)),Ft(Fn,t)}}function Qn(e,t,n){"focusin"===e?(Wn(),In=n,(Dn=t).attachEvent("onpropertychange",qn)):"focusout"===e&&Wn()}function Kn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return $n(In)}function Yn(e,t){if("click"===e)return $n(t)}function Zn(e,t){if("input"===e||"change"===e)return $n(t)}var Xn="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function Gn(e,t){if(Xn(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!G.call(t,i)||!Xn(e[i],t[i]))return!1}return!0}function Jn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function er(e,t){var n,r=Jn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Jn(r)}}function tr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?tr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function nr(e){for(var t=mt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=mt((e=t.contentWindow).document)}return t}function rr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var ir=Ht&&"documentMode"in document&&11>=document.documentMode,ar=null,or=null,lr=null,sr=!1;function ur(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;sr||null==ar||ar!==mt(r)||("selectionStart"in(r=ar)&&rr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},lr&&Gn(lr,r)||(lr=r,0<(r=qc(or,"onSelect")).length&&(t=new tn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=ar)))}function cr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var dr={animationend:cr("Animation","AnimationEnd"),animationiteration:cr("Animation","AnimationIteration"),animationstart:cr("Animation","AnimationStart"),transitionrun:cr("Transition","TransitionRun"),transitionstart:cr("Transition","TransitionStart"),transitioncancel:cr("Transition","TransitionCancel"),transitionend:cr("Transition","TransitionEnd")},fr={},pr={};function hr(e){if(fr[e])return fr[e];if(!dr[e])return e;var t,n=dr[e];for(t in n)if(n.hasOwnProperty(t)&&t in pr)return fr[e]=n[t];return e}Ht&&(pr=document.createElement("div").style,"AnimationEvent"in window||(delete dr.animationend.animation,delete dr.animationiteration.animation,delete dr.animationstart.animation),"TransitionEvent"in window||delete dr.transitionend.transition);var mr=hr("animationend"),gr=hr("animationiteration"),vr=hr("animationstart"),br=hr("transitionrun"),yr=hr("transitionstart"),_r=hr("transitioncancel"),wr=hr("transitionend"),kr=new Map,xr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Sr(e,t){kr.set(e,t),Ze(t,[e])}xr.push("scrollEnd");var Er=new WeakMap;function Cr(e,t){if("object"==typeof e&&null!==e){var n=Er.get(e);return void 0!==n?n:(t={value:e,source:t,stack:ct(t)},Er.set(e,t),t)}return{value:e,source:t,stack:ct(t)}}var Tr=[],Lr=0,Ar=0;function Nr(){for(var e=Lr,t=Ar=Lr=0;t<e;){var n=Tr[t];Tr[t++]=null;var r=Tr[t];Tr[t++]=null;var i=Tr[t];Tr[t++]=null;var a=Tr[t];if(Tr[t++]=null,null!==r&&null!==i){var o=r.pending;null===o?i.next=i:(i.next=o.next,o.next=i),r.pending=i}0!==a&&zr(n,i,a)}}function Pr(e,t,n,r){Tr[Lr++]=e,Tr[Lr++]=t,Tr[Lr++]=n,Tr[Lr++]=r,Ar|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Or(e,t,n,r){return Pr(e,t,n,r),Mr(e)}function jr(e,t){return Pr(e,null,null,t),Mr(e)}function zr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var i=!1,a=e.return;null!==a;)a.childLanes|=n,null!==(r=a.alternate)&&(r.childLanes|=n),22===a.tag&&(null===(e=a.stateNode)||1&e._visibility||(i=!0)),e=a,a=a.return;return 3===e.tag?(a=e.stateNode,i&&null!==t&&(i=31-me(n),null===(r=(e=a.hiddenUpdates)[i])?e[i]=[t]:r.push(t),t.lane=536870912|n),a):null}function Mr(e){if(50<zu)throw zu=0,Mu=null,Error(r(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Dr={};function Ir(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Fr(e,t,n,r){return new Ir(e,t,n,r)}function $r(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Hr(e,t){var n=e.alternate;return null===n?((n=Fr(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Rr(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Vr(e,t,n,i,a,o){var l=0;if(i=e,"function"==typeof e)$r(e)&&(l=1);else if("string"==typeof e)l=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!=typeof t.precedence||"string"!=typeof t.href||""===t.href)break;return!0;case"link":if("string"!=typeof t.rel||"string"!=typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"==typeof t.precedence&&null==e);case"script":if(t.async&&"function"!=typeof t.async&&"symbol"!=typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"==typeof t.src)return!0}return!1}(e,n,U.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case L:return(e=Fr(31,n,t,a)).elementType=L,e.lanes=o,e;case m:return Br(n.children,a,o,t);case g:l=8,a|=24;break;case v:return(e=Fr(12,n,t,2|a)).elementType=v,e.lanes=o,e;case x:return(e=Fr(13,n,t,a)).elementType=x,e.lanes=o,e;case S:return(e=Fr(19,n,t,a)).elementType=S,e.lanes=o,e;default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case b:case _:l=10;break e;case y:l=9;break e;case w:l=11;break e;case E:l=14;break e;case T:l=16,i=null;break e}l=29,n=Error(r(130,null===e?"null":typeof e,"")),i=null}return(t=Fr(l,n,t,a)).elementType=e,t.type=i,t.lanes=o,t}function Br(e,t,n,r){return(e=Fr(7,e,r,t)).lanes=n,e}function Ur(e,t,n){return(e=Fr(6,e,null,t)).lanes=n,e}function Wr(e,t,n){return(t=Fr(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var qr=[],Qr=0,Kr=null,Yr=0,Zr=[],Xr=0,Gr=null,Jr=1,ei="";function ti(e,t){qr[Qr++]=Yr,qr[Qr++]=Kr,Kr=e,Yr=t}function ni(e,t,n){Zr[Xr++]=Jr,Zr[Xr++]=ei,Zr[Xr++]=Gr,Gr=e;var r=Jr;e=ei;var i=32-me(r)-1;r&=~(1<<i),n+=1;var a=32-me(t)+i;if(30<a){var o=i-i%5;a=(r&(1<<o)-1).toString(32),r>>=o,i-=o,Jr=1<<32-me(t)+i|n<<i|r,ei=a+e}else Jr=1<<a|n<<i|r,ei=e}function ri(e){null!==e.return&&(ti(e,1),ni(e,1,0))}function ii(e){for(;e===Kr;)Kr=qr[--Qr],qr[Qr]=null,Yr=qr[--Qr],qr[Qr]=null;for(;e===Gr;)Gr=Zr[--Xr],Zr[Xr]=null,ei=Zr[--Xr],Zr[Xr]=null,Jr=Zr[--Xr],Zr[Xr]=null}var ai=null,oi=null,li=!1,si=null,ui=!1,ci=Error(r(519));function di(e){throw vi(Cr(Error(r(418,"")),e)),ci}function fi(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[ze]=e,t[Me]=r,n){case"dialog":$c("cancel",t),$c("close",t);break;case"iframe":case"object":case"embed":$c("load",t);break;case"video":case"audio":for(n=0;n<Dc.length;n++)$c(Dc[n],t);break;case"source":$c("error",t);break;case"img":case"image":case"link":$c("error",t),$c("load",t);break;case"details":$c("toggle",t);break;case"input":$c("invalid",t),yt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),pt(t);break;case"select":$c("invalid",t);break;case"textarea":$c("invalid",t),xt(t,r.value,r.defaultValue,r.children),pt(t)}"string"!=typeof(n=r.children)&&"number"!=typeof n&&"bigint"!=typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Gc(t.textContent,n)?(null!=r.popover&&($c("beforetoggle",t),$c("toggle",t)),null!=r.onScroll&&$c("scroll",t),null!=r.onScrollEnd&&$c("scrollend",t),null!=r.onClick&&(t.onclick=Jc),t=!0):t=!1,t||di(e)}function pi(e){for(ai=e.return;ai;)switch(ai.tag){case 5:case 13:return void(ui=!1);case 27:case 3:return void(ui=!0);default:ai=ai.return}}function hi(e){if(e!==ai)return!1;if(!li)return pi(e),li=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||sd(e.type,e.memoizedProps)),t=!t),t&&oi&&di(e),pi(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(r(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){oi=yd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}oi=null}}else 27===n?(n=oi,md(e.type)?(e=_d,_d=null,oi=e):oi=n):oi=ai?yd(e.stateNode.nextSibling):null;return!0}function mi(){oi=ai=null,li=!1}function gi(){var e=si;return null!==e&&(null===wu?wu=e:wu.push.apply(wu,e),si=null),e}function vi(e){null===si?si=[e]:si.push(e)}var bi=R(null),yi=null,_i=null;function wi(e,t,n){B(bi,t._currentValue),t._currentValue=n}function ki(e){e._currentValue=bi.current,V(bi)}function xi(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Si(e,t,n,i){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var o=a.dependencies;if(null!==o){var l=a.child;o=o.firstContext;e:for(;null!==o;){var s=o;o=a;for(var u=0;u<t.length;u++)if(s.context===t[u]){o.lanes|=n,null!==(s=o.alternate)&&(s.lanes|=n),xi(o.return,n,e),i||(l=null);break e}o=s.next}}else if(18===a.tag){if(null===(l=a.return))throw Error(r(341));l.lanes|=n,null!==(o=l.alternate)&&(o.lanes|=n),xi(l,n,e),l=null}else l=a.child;if(null!==l)l.return=a;else for(l=a;null!==l;){if(l===e){l=null;break}if(null!==(a=l.sibling)){a.return=l.return,l=a;break}l=l.return}a=l}}function Ei(e,t,n,i){e=null;for(var a=t,o=!1;null!==a;){if(!o)if(524288&a.flags)o=!0;else if(262144&a.flags)break;if(10===a.tag){var l=a.alternate;if(null===l)throw Error(r(387));if(null!==(l=l.memoizedProps)){var s=a.type;Xn(a.pendingProps.value,l.value)||(null!==e?e.push(s):e=[s])}}else if(a===Q.current){if(null===(l=a.alternate))throw Error(r(387));l.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(Zd):e=[Zd])}a=a.return}null!==e&&Si(t,e,n,i),t.flags|=262144}function Ci(e){for(e=e.firstContext;null!==e;){if(!Xn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ti(e){yi=e,_i=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Li(e){return Ni(yi,e)}function Ai(e,t){return null===yi&&Ti(e),Ni(e,t)}function Ni(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===_i){if(null===e)throw Error(r(308));_i=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else _i=_i.next=t;return n}var Pi="undefined"!=typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach((function(e){return e()}))}},Oi=e.unstable_scheduleCallback,ji=e.unstable_NormalPriority,zi={$$typeof:_,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Mi(){return{controller:new Pi,data:new Map,refCount:0}}function Di(e){e.refCount--,0===e.refCount&&Oi(ji,(function(){e.controller.abort()}))}var Ii=null,Fi=0,$i=0,Hi=null;function Ri(){if(0===--Fi&&null!==Ii){null!==Hi&&(Hi.status="fulfilled");var e=Ii;Ii=null,$i=0,Hi=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Vi=D.S;D.S=function(e,t){"object"==typeof t&&null!==t&&"function"==typeof t.then&&function(e,t){if(null===Ii){var n=Ii=[];Fi=0,$i=Pc(),Hi={status:"pending",value:void 0,then:function(e){n.push(e)}}}Fi++,t.then(Ri,Ri)}(0,t),null!==Vi&&Vi(e,t)};var Bi=R(null);function Ui(){var e=Bi.current;return null!==e?e:au.pooledCache}function Wi(e,t){B(Bi,null===t?Bi.current:t.pool)}function qi(){var e=Ui();return null===e?null:{parent:zi._currentValue,pool:e}}var Qi=Error(r(460)),Ki=Error(r(474)),Yi=Error(r(542)),Zi={then:function(){}};function Xi(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Gi(){}function Ji(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Gi,Gi),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw na(e=t.reason),e;default:if("string"==typeof t.status)t.then(Gi,Gi);else{if(null!==(e=au)&&100<e.shellSuspendCounter)throw Error(r(482));(e=t).status="pending",e.then((function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}}),(function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}}))}switch(t.status){case"fulfilled":return t.value;case"rejected":throw na(e=t.reason),e}throw ea=t,Qi}}var ea=null;function ta(){if(null===ea)throw Error(r(459));var e=ea;return ea=null,e}function na(e){if(e===Qi||e===Yi)throw Error(r(483))}var ra=!1;function ia(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function aa(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function oa(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function la(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&iu){var i=r.pending;return null===i?t.next=t:(t.next=i.next,i.next=t),r.pending=t,t=Mr(e),zr(e,null,n),t}return Pr(e,r,t,n),Mr(e)}function sa(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Ae(e,n)}}function ua(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var i=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var o={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===a?i=a=o:a=a.next=o,n=n.next}while(null!==n);null===a?i=a=t:a=a.next=t}else i=a=t;return n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:a,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var ca=!1;function da(){if(ca){if(null!==Hi)throw Hi}}function fa(e,t,n,r){ca=!1;var i=e.updateQueue;ra=!1;var a=i.firstBaseUpdate,o=i.lastBaseUpdate,l=i.shared.pending;if(null!==l){i.shared.pending=null;var s=l,u=s.next;s.next=null,null===o?a=u:o.next=u,o=s;var c=e.alternate;null!==c&&((l=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===l?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=s))}if(null!==a){var f=i.baseState;for(o=0,c=u=s=null,l=a;;){var p=-536870913&l.lane,h=p!==l.lane;if(h?(lu&p)===p:(r&p)===p){0!==p&&p===$i&&(ca=!0),null!==c&&(c=c.next={lane:0,tag:l.tag,payload:l.payload,callback:null,next:null});e:{var m=e,g=l;p=t;var v=n;switch(g.tag){case 1:if("function"==typeof(m=g.payload)){f=m.call(v,f,p);break e}f=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null==(p="function"==typeof(m=g.payload)?m.call(v,f,p):m))break e;f=d({},f,p);break e;case 2:ra=!0}}null!==(p=l.callback)&&(e.flags|=64,h&&(e.flags|=8192),null===(h=i.callbacks)?i.callbacks=[p]:h.push(p))}else h={lane:p,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===c?(u=c=h,s=f):c=c.next=h,o|=p;if(null===(l=l.next)){if(null===(l=i.shared.pending))break;l=(h=l).next,h.next=null,i.lastBaseUpdate=h,i.shared.pending=null}}null===c&&(s=f),i.baseState=s,i.firstBaseUpdate=u,i.lastBaseUpdate=c,null===a&&(i.shared.lanes=0),mu|=o,e.lanes=o,e.memoizedState=f}}function pa(e,t){if("function"!=typeof e)throw Error(r(191,e));e.call(t)}function ha(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)pa(n[e],t)}var ma=R(null),ga=R(0);function va(e,t){B(ga,e=pu),B(ma,t),pu=e|t.baseLanes}function ba(){B(ga,pu),B(ma,ma.current)}function ya(){pu=ga.current,V(ma),V(ga)}var _a=0,wa=null,ka=null,xa=null,Sa=!1,Ea=!1,Ca=!1,Ta=0,La=0,Aa=null,Na=0;function Pa(){throw Error(r(321))}function Oa(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Xn(e[n],t[n]))return!1;return!0}function ja(e,t,n,r,i,a){return _a=a,wa=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,D.H=null===e||null===e.memoizedState?Ko:Yo,Ca=!1,a=n(r,i),Ca=!1,Ea&&(a=Ma(t,n,r,i)),za(e),a}function za(e){D.H=Qo;var t=null!==ka&&null!==ka.next;if(_a=0,xa=ka=wa=null,Sa=!1,La=0,Aa=null,t)throw Error(r(300));null===e||Al||null!==(e=e.dependencies)&&Ci(e)&&(Al=!0)}function Ma(e,t,n,i){wa=e;var a=0;do{if(Ea&&(Aa=null),La=0,Ea=!1,25<=a)throw Error(r(301));if(a+=1,xa=ka=null,null!=e.updateQueue){var o=e.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,null!=o.memoCache&&(o.memoCache.index=0)}D.H=Zo,o=t(n,i)}while(Ea);return o}function Da(){var e=D.H,t=e.useState()[0];return t="function"==typeof t.then?Va(t):t,e=e.useState()[0],(null!==ka?ka.memoizedState:null)!==e&&(wa.flags|=1024),t}function Ia(){var e=0!==Ta;return Ta=0,e}function Fa(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function $a(e){if(Sa){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}Sa=!1}_a=0,xa=ka=wa=null,Ea=!1,La=Ta=0,Aa=null}function Ha(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===xa?wa.memoizedState=xa=e:xa=xa.next=e,xa}function Ra(){if(null===ka){var e=wa.alternate;e=null!==e?e.memoizedState:null}else e=ka.next;var t=null===xa?wa.memoizedState:xa.next;if(null!==t)xa=t,ka=e;else{if(null===e){if(null===wa.alternate)throw Error(r(467));throw Error(r(310))}e={memoizedState:(ka=e).memoizedState,baseState:ka.baseState,baseQueue:ka.baseQueue,queue:ka.queue,next:null},null===xa?wa.memoizedState=xa=e:xa=xa.next=e}return xa}function Va(e){var t=La;return La+=1,null===Aa&&(Aa=[]),e=Ji(Aa,e,t),t=wa,null===(null===xa?t.memoizedState:xa.next)&&(t=t.alternate,D.H=null===t||null===t.memoizedState?Ko:Yo),e}function Ba(e){if(null!==e&&"object"==typeof e){if("function"==typeof e.then)return Va(e);if(e.$$typeof===_)return Li(e)}throw Error(r(438,String(e)))}function Ua(e){var t=null,n=wa.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=wa.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map((function(e){return e.slice()})),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},wa.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=N;return t.index++,n}function Wa(e,t){return"function"==typeof t?t(e):t}function qa(e){return Qa(Ra(),ka,e)}function Qa(e,t,n){var i=e.queue;if(null===i)throw Error(r(311));i.lastRenderedReducer=n;var a=e.baseQueue,o=i.pending;if(null!==o){if(null!==a){var l=a.next;a.next=o.next,o.next=l}t.baseQueue=a=o,i.pending=null}if(o=e.baseState,null===a)e.memoizedState=o;else{var s=l=null,u=null,c=t=a.next,d=!1;do{var f=-536870913&c.lane;if(f!==c.lane?(lu&f)===f:(_a&f)===f){var p=c.revertLane;if(0===p)null!==u&&(u=u.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===$i&&(d=!0);else{if((_a&p)===p){c=c.next,p===$i&&(d=!0);continue}f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=f,l=o):u=u.next=f,wa.lanes|=p,mu|=p}f=c.action,Ca&&n(o,f),o=c.hasEagerState?c.eagerState:n(o,f)}else p={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=p,l=o):u=u.next=p,wa.lanes|=f,mu|=f;c=c.next}while(null!==c&&c!==t);if(null===u?l=o:u.next=s,!Xn(o,e.memoizedState)&&(Al=!0,d&&null!==(n=Hi)))throw n;e.memoizedState=o,e.baseState=l,e.baseQueue=u,i.lastRenderedState=o}return null===a&&(i.lanes=0),[e.memoizedState,i.dispatch]}function Ka(e){var t=Ra(),n=t.queue;if(null===n)throw Error(r(311));n.lastRenderedReducer=e;var i=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var l=a=a.next;do{o=e(o,l.action),l=l.next}while(l!==a);Xn(o,t.memoizedState)||(Al=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,i]}function Ya(e,t,n){var i=wa,a=Ra(),o=li;if(o){if(void 0===n)throw Error(r(407));n=n()}else n=t();var l=!Xn((ka||a).memoizedState,n);if(l&&(a.memoizedState=n,Al=!0),a=a.queue,yo(2048,8,Ga.bind(null,i,a,e),[e]),a.getSnapshot!==t||l||null!==xa&&1&xa.memoizedState.tag){if(i.flags|=2048,go(9,{destroy:void 0,resource:void 0},Xa.bind(null,i,a,n,t),null),null===au)throw Error(r(349));o||124&_a||Za(i,t,n)}return n}function Za(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=wa.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},wa.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Xa(e,t,n,r){t.value=n,t.getSnapshot=r,Ja(t)&&eo(e)}function Ga(e,t,n){return n((function(){Ja(t)&&eo(e)}))}function Ja(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Xn(e,n)}catch(r){return!0}}function eo(e){var t=jr(e,2);null!==t&&Fu(t,e,2)}function to(e){var t=Ha();if("function"==typeof e){var n=e;if(e=n(),Ca){he(!0);try{n()}finally{he(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wa,lastRenderedState:e},t}function no(e,t,n,r){return e.baseState=n,Qa(e,ka,"function"==typeof r?r:Wa)}function ro(e,t,n,i,a){if(Uo(e))throw Error(r(485));if(null!==(e=t.action)){var o={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){o.listeners.push(e)}};null!==D.T?n(!0):o.isTransition=!1,i(o),null===(n=t.pending)?(o.next=t.pending=o,io(t,o)):(o.next=n.next,t.pending=n.next=o)}}function io(e,t){var n=t.action,r=t.payload,i=e.state;if(t.isTransition){var a=D.T,o={};D.T=o;try{var l=n(i,r),s=D.S;null!==s&&s(o,l),ao(e,t,l)}catch(u){lo(e,t,u)}finally{D.T=a}}else try{ao(e,t,a=n(i,r))}catch(c){lo(e,t,c)}}function ao(e,t,n){null!==n&&"object"==typeof n&&"function"==typeof n.then?n.then((function(n){oo(e,t,n)}),(function(n){return lo(e,t,n)})):oo(e,t,n)}function oo(e,t,n){t.status="fulfilled",t.value=n,so(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,io(e,n)))}function lo(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,so(t),t=t.next}while(t!==r)}e.action=null}function so(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function uo(e,t){return t}function co(e,t){if(li){var n=au.formState;if(null!==n){e:{var r=wa;if(li){if(oi){t:{for(var i=oi,a=ui;8!==i.nodeType;){if(!a){i=null;break t}if(null===(i=yd(i.nextSibling))){i=null;break t}}i="F!"===(a=i.data)||"F"===a?i:null}if(i){oi=yd(i.nextSibling),r="F!"===i.data;break e}}di(r)}r=!1}r&&(t=n[0])}}return(n=Ha()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:uo,lastRenderedState:t},n.queue=r,n=Ro.bind(null,wa,r),r.dispatch=n,r=to(!1),a=Bo.bind(null,wa,!1,r.queue),i={state:t,dispatch:null,action:e,pending:null},(r=Ha()).queue=i,n=ro.bind(null,wa,i,a,n),i.dispatch=n,r.memoizedState=e,[t,n,!1]}function fo(e){return po(Ra(),ka,e)}function po(e,t,n){if(t=Qa(e,t,uo)[0],e=qa(Wa)[0],"object"==typeof t&&null!==t&&"function"==typeof t.then)try{var r=Va(t)}catch(o){if(o===Qi)throw Yi;throw o}else r=t;var i=(t=Ra()).queue,a=i.dispatch;return n!==t.memoizedState&&(wa.flags|=2048,go(9,{destroy:void 0,resource:void 0},ho.bind(null,i,n),null)),[r,a,e]}function ho(e,t){e.action=t}function mo(e){var t=Ra(),n=ka;if(null!==n)return po(t,n,e);Ra(),t=t.memoizedState;var r=(n=Ra()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function go(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=wa.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},wa.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function vo(){return Ra().memoizedState}function bo(e,t,n,r){var i=Ha();r=void 0===r?null:r,wa.flags|=e,i.memoizedState=go(1|t,{destroy:void 0,resource:void 0},n,r)}function yo(e,t,n,r){var i=Ra();r=void 0===r?null:r;var a=i.memoizedState.inst;null!==ka&&null!==r&&Oa(r,ka.memoizedState.deps)?i.memoizedState=go(t,a,n,r):(wa.flags|=e,i.memoizedState=go(1|t,a,n,r))}function _o(e,t){bo(8390656,8,e,t)}function wo(e,t){yo(2048,8,e,t)}function ko(e,t){return yo(4,2,e,t)}function xo(e,t){return yo(4,4,e,t)}function So(e,t){if("function"==typeof t){e=e();var n=t(e);return function(){"function"==typeof n?n():t(null)}}if(null!=t)return e=e(),t.current=e,function(){t.current=null}}function Eo(e,t,n){n=null!=n?n.concat([e]):null,yo(4,4,So.bind(null,t,e),n)}function Co(){}function To(e,t){var n=Ra();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&Oa(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Lo(e,t){var n=Ra();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&Oa(t,r[1]))return r[0];if(r=e(),Ca){he(!0);try{e()}finally{he(!1)}}return n.memoizedState=[r,t],r}function Ao(e,t,n){return void 0===n||1073741824&_a?e.memoizedState=t:(e.memoizedState=n,e=Iu(),wa.lanes|=e,mu|=e,n)}function No(e,t,n,r){return Xn(n,t)?n:null!==ma.current?(e=Ao(e,n,r),Xn(e,t)||(Al=!0),e):42&_a?(e=Iu(),wa.lanes|=e,mu|=e,t):(Al=!0,e.memoizedState=n)}function Po(e,t,n,r,i){var a=I.p;I.p=0!==a&&8>a?a:8;var o,l,s,u=D.T,c={};D.T=c,Bo(e,!1,t,n);try{var d=i(),f=D.S;if(null!==f&&f(c,d),null!==d&&"object"==typeof d&&"function"==typeof d.then)Vo(e,t,(o=r,l=[],s={status:"pending",value:null,reason:null,then:function(e){l.push(e)}},d.then((function(){s.status="fulfilled",s.value=o;for(var e=0;e<l.length;e++)(0,l[e])(o)}),(function(e){for(s.status="rejected",s.reason=e,e=0;e<l.length;e++)(0,l[e])(void 0)})),s),Du());else Vo(e,t,r,Du())}catch(p){Vo(e,t,{then:function(){},status:"rejected",reason:p},Du())}finally{I.p=a,D.T=u}}function Oo(){}function jo(e,t,n,i){if(5!==e.tag)throw Error(r(476));var a=zo(e).queue;Po(e,a,t,F,null===n?Oo:function(){return Mo(e),n(i)})}function zo(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:F,baseState:F,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wa,lastRenderedState:F},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wa,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Mo(e){Vo(e,zo(e).next.queue,{},Du())}function Do(){return Li(Zd)}function Io(){return Ra().memoizedState}function Fo(){return Ra().memoizedState}function $o(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Du(),r=la(t,e=oa(n),n);return null!==r&&(Fu(r,t,n),sa(r,t,n)),t={cache:Mi()},void(e.payload=t)}t=t.return}}function Ho(e,t,n){var r=Du();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Uo(e)?Wo(t,n):null!==(n=Or(e,t,n,r))&&(Fu(n,e,r),qo(n,t,r))}function Ro(e,t,n){Vo(e,t,n,Du())}function Vo(e,t,n,r){var i={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Uo(e))Wo(t,i);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var o=t.lastRenderedState,l=a(o,n);if(i.hasEagerState=!0,i.eagerState=l,Xn(l,o))return Pr(e,t,i,0),null===au&&Nr(),!1}catch(s){}if(null!==(n=Or(e,t,i,r)))return Fu(n,e,r),qo(n,t,r),!0}return!1}function Bo(e,t,n,i){if(i={lane:2,revertLane:Pc(),action:i,hasEagerState:!1,eagerState:null,next:null},Uo(e)){if(t)throw Error(r(479))}else null!==(t=Or(e,n,i,2))&&Fu(t,e,2)}function Uo(e){var t=e.alternate;return e===wa||null!==t&&t===wa}function Wo(e,t){Ea=Sa=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function qo(e,t,n){if(4194048&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Ae(e,n)}}var Qo={readContext:Li,use:Ba,useCallback:Pa,useContext:Pa,useEffect:Pa,useImperativeHandle:Pa,useLayoutEffect:Pa,useInsertionEffect:Pa,useMemo:Pa,useReducer:Pa,useRef:Pa,useState:Pa,useDebugValue:Pa,useDeferredValue:Pa,useTransition:Pa,useSyncExternalStore:Pa,useId:Pa,useHostTransitionStatus:Pa,useFormState:Pa,useActionState:Pa,useOptimistic:Pa,useMemoCache:Pa,useCacheRefresh:Pa},Ko={readContext:Li,use:Ba,useCallback:function(e,t){return Ha().memoizedState=[e,void 0===t?null:t],e},useContext:Li,useEffect:_o,useImperativeHandle:function(e,t,n){n=null!=n?n.concat([e]):null,bo(4194308,4,So.bind(null,t,e),n)},useLayoutEffect:function(e,t){return bo(4194308,4,e,t)},useInsertionEffect:function(e,t){bo(4,2,e,t)},useMemo:function(e,t){var n=Ha();t=void 0===t?null:t;var r=e();if(Ca){he(!0);try{e()}finally{he(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Ha();if(void 0!==n){var i=n(t);if(Ca){he(!0);try{n(t)}finally{he(!1)}}}else i=t;return r.memoizedState=r.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},r.queue=e,e=e.dispatch=Ho.bind(null,wa,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ha().memoizedState=e},useState:function(e){var t=(e=to(e)).queue,n=Ro.bind(null,wa,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Co,useDeferredValue:function(e,t){return Ao(Ha(),e,t)},useTransition:function(){var e=to(!1);return e=Po.bind(null,wa,e.queue,!0,!1),Ha().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var i=wa,a=Ha();if(li){if(void 0===n)throw Error(r(407));n=n()}else{if(n=t(),null===au)throw Error(r(349));124&lu||Za(i,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,_o(Ga.bind(null,i,o,e),[e]),i.flags|=2048,go(9,{destroy:void 0,resource:void 0},Xa.bind(null,i,o,n,t),null),n},useId:function(){var e=Ha(),t=au.identifierPrefix;if(li){var n=ei;t="«"+t+"R"+(n=(Jr&~(1<<32-me(Jr)-1)).toString(32)+n),0<(n=Ta++)&&(t+="H"+n.toString(32)),t+="»"}else t="«"+t+"r"+(n=Na++).toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Do,useFormState:co,useActionState:co,useOptimistic:function(e){var t=Ha();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Bo.bind(null,wa,!0,n),n.dispatch=t,[e,t]},useMemoCache:Ua,useCacheRefresh:function(){return Ha().memoizedState=$o.bind(null,wa)}},Yo={readContext:Li,use:Ba,useCallback:To,useContext:Li,useEffect:wo,useImperativeHandle:Eo,useInsertionEffect:ko,useLayoutEffect:xo,useMemo:Lo,useReducer:qa,useRef:vo,useState:function(){return qa(Wa)},useDebugValue:Co,useDeferredValue:function(e,t){return No(Ra(),ka.memoizedState,e,t)},useTransition:function(){var e=qa(Wa)[0],t=Ra().memoizedState;return["boolean"==typeof e?e:Va(e),t]},useSyncExternalStore:Ya,useId:Io,useHostTransitionStatus:Do,useFormState:fo,useActionState:fo,useOptimistic:function(e,t){return no(Ra(),0,e,t)},useMemoCache:Ua,useCacheRefresh:Fo},Zo={readContext:Li,use:Ba,useCallback:To,useContext:Li,useEffect:wo,useImperativeHandle:Eo,useInsertionEffect:ko,useLayoutEffect:xo,useMemo:Lo,useReducer:Ka,useRef:vo,useState:function(){return Ka(Wa)},useDebugValue:Co,useDeferredValue:function(e,t){var n=Ra();return null===ka?Ao(n,e,t):No(n,ka.memoizedState,e,t)},useTransition:function(){var e=Ka(Wa)[0],t=Ra().memoizedState;return["boolean"==typeof e?e:Va(e),t]},useSyncExternalStore:Ya,useId:Io,useHostTransitionStatus:Do,useFormState:mo,useActionState:mo,useOptimistic:function(e,t){var n=Ra();return null!==ka?no(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Ua,useCacheRefresh:Fo},Xo=null,Go=0;function Jo(e){var t=Go;return Go+=1,null===Xo&&(Xo=[]),Ji(Xo,e,t)}function el(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function tl(e,t){if(t.$$typeof===f)throw Error(r(525));throw e=Object.prototype.toString.call(t),Error(r(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function nl(e){return(0,e._init)(e._payload)}function rl(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function i(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function a(e,t){return(e=Hr(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function l(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Ur(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var i=n.type;return i===m?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===i||"object"==typeof i&&null!==i&&i.$$typeof===T&&nl(i)===t.type)?(el(t=a(t,n.props),n),t.return=e,t):(el(t=Vr(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Wr(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,i){return null===t||7!==t.tag?((t=Br(n,e.mode,r,i)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t||"bigint"==typeof t)return(t=Ur(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case p:return el(n=Vr(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case h:return(t=Wr(t,e.mode,n)).return=e,t;case T:return f(e,t=(0,t._init)(t._payload),n)}if(M(t)||O(t))return(t=Br(t,e.mode,n,null)).return=e,t;if("function"==typeof t.then)return f(e,Jo(t),n);if(t.$$typeof===_)return f(e,Ai(e,t),n);tl(e,t)}return null}function g(e,t,n,r){var i=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n||"bigint"==typeof n)return null!==i?null:s(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case p:return n.key===i?u(e,t,n,r):null;case h:return n.key===i?c(e,t,n,r):null;case T:return g(e,t,n=(i=n._init)(n._payload),r)}if(M(n)||O(n))return null!==i?null:d(e,t,n,r,null);if("function"==typeof n.then)return g(e,t,Jo(n),r);if(n.$$typeof===_)return g(e,t,Ai(e,n),r);tl(e,n)}return null}function v(e,t,n,r,i){if("string"==typeof r&&""!==r||"number"==typeof r||"bigint"==typeof r)return s(t,e=e.get(n)||null,""+r,i);if("object"==typeof r&&null!==r){switch(r.$$typeof){case p:return u(t,e=e.get(null===r.key?n:r.key)||null,r,i);case h:return c(t,e=e.get(null===r.key?n:r.key)||null,r,i);case T:return v(e,t,n,r=(0,r._init)(r._payload),i)}if(M(r)||O(r))return d(t,e=e.get(n)||null,r,i,null);if("function"==typeof r.then)return v(e,t,n,Jo(r),i);if(r.$$typeof===_)return v(e,t,n,Ai(t,r),i);tl(t,r)}return null}function b(s,u,c,d){if("object"==typeof c&&null!==c&&c.type===m&&null===c.key&&(c=c.props.children),"object"==typeof c&&null!==c){switch(c.$$typeof){case p:e:{for(var y=c.key;null!==u;){if(u.key===y){if((y=c.type)===m){if(7===u.tag){n(s,u.sibling),(d=a(u,c.props.children)).return=s,s=d;break e}}else if(u.elementType===y||"object"==typeof y&&null!==y&&y.$$typeof===T&&nl(y)===u.type){n(s,u.sibling),el(d=a(u,c.props),c),d.return=s,s=d;break e}n(s,u);break}t(s,u),u=u.sibling}c.type===m?((d=Br(c.props.children,s.mode,d,c.key)).return=s,s=d):(el(d=Vr(c.type,c.key,c.props,null,s.mode,d),c),d.return=s,s=d)}return l(s);case h:e:{for(y=c.key;null!==u;){if(u.key===y){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){n(s,u.sibling),(d=a(u,c.children||[])).return=s,s=d;break e}n(s,u);break}t(s,u),u=u.sibling}(d=Wr(c,s.mode,d)).return=s,s=d}return l(s);case T:return b(s,u,c=(y=c._init)(c._payload),d)}if(M(c))return function(r,a,l,s){for(var u=null,c=null,d=a,p=a=0,h=null;null!==d&&p<l.length;p++){d.index>p?(h=d,d=null):h=d.sibling;var m=g(r,d,l[p],s);if(null===m){null===d&&(d=h);break}e&&d&&null===m.alternate&&t(r,d),a=o(m,a,p),null===c?u=m:c.sibling=m,c=m,d=h}if(p===l.length)return n(r,d),li&&ti(r,p),u;if(null===d){for(;p<l.length;p++)null!==(d=f(r,l[p],s))&&(a=o(d,a,p),null===c?u=d:c.sibling=d,c=d);return li&&ti(r,p),u}for(d=i(d);p<l.length;p++)null!==(h=v(d,r,p,l[p],s))&&(e&&null!==h.alternate&&d.delete(null===h.key?p:h.key),a=o(h,a,p),null===c?u=h:c.sibling=h,c=h);return e&&d.forEach((function(e){return t(r,e)})),li&&ti(r,p),u}(s,u,c,d);if(O(c)){if("function"!=typeof(y=O(c)))throw Error(r(150));return function(a,l,s,u){if(null==s)throw Error(r(151));for(var c=null,d=null,p=l,h=l=0,m=null,b=s.next();null!==p&&!b.done;h++,b=s.next()){p.index>h?(m=p,p=null):m=p.sibling;var y=g(a,p,b.value,u);if(null===y){null===p&&(p=m);break}e&&p&&null===y.alternate&&t(a,p),l=o(y,l,h),null===d?c=y:d.sibling=y,d=y,p=m}if(b.done)return n(a,p),li&&ti(a,h),c;if(null===p){for(;!b.done;h++,b=s.next())null!==(b=f(a,b.value,u))&&(l=o(b,l,h),null===d?c=b:d.sibling=b,d=b);return li&&ti(a,h),c}for(p=i(p);!b.done;h++,b=s.next())null!==(b=v(p,a,h,b.value,u))&&(e&&null!==b.alternate&&p.delete(null===b.key?h:b.key),l=o(b,l,h),null===d?c=b:d.sibling=b,d=b);return e&&p.forEach((function(e){return t(a,e)})),li&&ti(a,h),c}(s,u,c=y.call(c),d)}if("function"==typeof c.then)return b(s,u,Jo(c),d);if(c.$$typeof===_)return b(s,u,Ai(s,c),d);tl(s,c)}return"string"==typeof c&&""!==c||"number"==typeof c||"bigint"==typeof c?(c=""+c,null!==u&&6===u.tag?(n(s,u.sibling),(d=a(u,c)).return=s,s=d):(n(s,u),(d=Ur(c,s.mode,d)).return=s,s=d),l(s)):n(s,u)}return function(e,t,n,r){try{Go=0;var i=b(e,t,n,r);return Xo=null,i}catch(o){if(o===Qi||o===Yi)throw o;var a=Fr(29,o,null,e.mode);return a.lanes=r,a.return=e,a}}}var il=rl(!0),al=rl(!1),ol=R(null),ll=null;function sl(e){var t=e.alternate;B(fl,1&fl.current),B(ol,e),null===ll&&(null===t||null!==ma.current||null!==t.memoizedState)&&(ll=e)}function ul(e){if(22===e.tag){if(B(fl,fl.current),B(ol,e),null===ll){var t=e.alternate;null!==t&&null!==t.memoizedState&&(ll=e)}}else cl()}function cl(){B(fl,fl.current),B(ol,ol.current)}function dl(e){V(ol),ll===e&&(ll=null),V(fl)}var fl=R(0);function pl(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||bd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function hl(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:d({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ml={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Du(),i=oa(r);i.payload=t,null!=n&&(i.callback=n),null!==(t=la(e,i,r))&&(Fu(t,e,r),sa(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Du(),i=oa(r);i.tag=1,i.payload=t,null!=n&&(i.callback=n),null!==(t=la(e,i,r))&&(Fu(t,e,r),sa(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Du(),r=oa(n);r.tag=2,null!=t&&(r.callback=t),null!==(t=la(e,r,n))&&(Fu(t,e,n),sa(t,e,n))}};function gl(e,t,n,r,i,a,o){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,o):!t.prototype||!t.prototype.isPureReactComponent||(!Gn(n,r)||!Gn(i,a))}function vl(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ml.enqueueReplaceState(t,t.state,null)}function bl(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var i in n===t&&(n=d({},n)),e)void 0===n[i]&&(n[i]=e[i]);return n}var yl="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e)};function _l(e){yl(e)}function wl(e){}function kl(e){yl(e)}function xl(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout((function(){throw n}))}}function Sl(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout((function(){throw r}))}}function El(e,t,n){return(n=oa(n)).tag=3,n.payload={element:null},n.callback=function(){xl(e,t)},n}function Cl(e){return(e=oa(e)).tag=3,e}function Tl(e,t,n,r){var i=n.type.getDerivedStateFromError;if("function"==typeof i){var a=r.value;e.payload=function(){return i(a)},e.callback=function(){Sl(t,n,r)}}var o=n.stateNode;null!==o&&"function"==typeof o.componentDidCatch&&(e.callback=function(){Sl(t,n,r),"function"!=typeof i&&(null===Cu?Cu=new Set([this]):Cu.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Ll=Error(r(461)),Al=!1;function Nl(e,t,n,r){t.child=null===e?al(t,null,n,r):il(t,e.child,n,r)}function Pl(e,t,n,r,i){n=n.render;var a=t.ref;if("ref"in r){var o={};for(var l in r)"ref"!==l&&(o[l]=r[l])}else o=r;return Ti(t),r=ja(e,t,n,o,a,i),l=Ia(),null===e||Al?(li&&l&&ri(t),t.flags|=1,Nl(e,t,r,i),t.child):(Fa(e,t,i),Xl(e,t,i))}function Ol(e,t,n,r,i){if(null===e){var a=n.type;return"function"!=typeof a||$r(a)||void 0!==a.defaultProps||null!==n.compare?((e=Vr(n.type,null,r,t,t.mode,i)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,jl(e,t,a,r,i))}if(a=e.child,!Gl(e,i)){var o=a.memoizedProps;if((n=null!==(n=n.compare)?n:Gn)(o,r)&&e.ref===t.ref)return Xl(e,t,i)}return t.flags|=1,(e=Hr(a,r)).ref=t.ref,e.return=t,t.child=e}function jl(e,t,n,r,i){if(null!==e){var a=e.memoizedProps;if(Gn(a,r)&&e.ref===t.ref){if(Al=!1,t.pendingProps=r=a,!Gl(e,i))return t.lanes=e.lanes,Xl(e,t,i);131072&e.flags&&(Al=!0)}}return Il(e,t,n,r,i)}function zl(e,t,n){var r=t.pendingProps,i=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(128&t.flags){if(r=null!==a?a.baseLanes|n:n,null!==e){for(i=t.child=e.child,a=0;null!==i;)a=a|i.lanes|i.childLanes,i=i.sibling;t.childLanes=a&~r}else t.childLanes=0,t.child=null;return Ml(e,t,r,n)}if(!(536870912&n))return t.lanes=t.childLanes=536870912,Ml(e,t,null!==a?a.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Wi(0,null!==a?a.cachePool:null),null!==a?va(t,a):ba(),ul(t)}else null!==a?(Wi(0,a.cachePool),va(t,a),cl(),t.memoizedState=null):(null!==e&&Wi(0,null),ba(),cl());return Nl(e,t,i,n),t.child}function Ml(e,t,n,r){var i=Ui();return i=null===i?null:{parent:zi._currentValue,pool:i},t.memoizedState={baseLanes:n,cachePool:i},null!==e&&Wi(0,null),ba(),ul(t),null!==e&&Ei(e,t,r,!0),null}function Dl(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!=typeof n&&"object"!=typeof n)throw Error(r(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Il(e,t,n,r,i){return Ti(t),n=ja(e,t,n,r,void 0,i),r=Ia(),null===e||Al?(li&&r&&ri(t),t.flags|=1,Nl(e,t,n,i),t.child):(Fa(e,t,i),Xl(e,t,i))}function Fl(e,t,n,r,i,a){return Ti(t),t.updateQueue=null,n=Ma(t,r,n,i),za(e),r=Ia(),null===e||Al?(li&&r&&ri(t),t.flags|=1,Nl(e,t,n,a),t.child):(Fa(e,t,a),Xl(e,t,a))}function $l(e,t,n,r,i){if(Ti(t),null===t.stateNode){var a=Dr,o=n.contextType;"object"==typeof o&&null!==o&&(a=Li(o)),a=new n(r,a),t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,a.updater=ml,t.stateNode=a,a._reactInternals=t,(a=t.stateNode).props=r,a.state=t.memoizedState,a.refs={},ia(t),o=n.contextType,a.context="object"==typeof o&&null!==o?Li(o):Dr,a.state=t.memoizedState,"function"==typeof(o=n.getDerivedStateFromProps)&&(hl(t,n,o,r),a.state=t.memoizedState),"function"==typeof n.getDerivedStateFromProps||"function"==typeof a.getSnapshotBeforeUpdate||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||(o=a.state,"function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),o!==a.state&&ml.enqueueReplaceState(a,a.state,null),fa(t,r,a,i),da(),a.state=t.memoizedState),"function"==typeof a.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){a=t.stateNode;var l=t.memoizedProps,s=bl(n,l);a.props=s;var u=a.context,c=n.contextType;o=Dr,"object"==typeof c&&null!==c&&(o=Li(c));var d=n.getDerivedStateFromProps;c="function"==typeof d||"function"==typeof a.getSnapshotBeforeUpdate,l=t.pendingProps!==l,c||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(l||u!==o)&&vl(t,a,r,o),ra=!1;var f=t.memoizedState;a.state=f,fa(t,r,a,i),da(),u=t.memoizedState,l||f!==u||ra?("function"==typeof d&&(hl(t,n,d,r),u=t.memoizedState),(s=ra||gl(t,n,s,r,f,u,o))?(c||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||("function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"==typeof a.componentDidMount&&(t.flags|=4194308)):("function"==typeof a.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),a.props=r,a.state=u,a.context=o,r=s):("function"==typeof a.componentDidMount&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,aa(e,t),c=bl(n,o=t.memoizedProps),a.props=c,d=t.pendingProps,f=a.context,u=n.contextType,s=Dr,"object"==typeof u&&null!==u&&(s=Li(u)),(u="function"==typeof(l=n.getDerivedStateFromProps)||"function"==typeof a.getSnapshotBeforeUpdate)||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(o!==d||f!==s)&&vl(t,a,r,s),ra=!1,f=t.memoizedState,a.state=f,fa(t,r,a,i),da();var p=t.memoizedState;o!==d||f!==p||ra||null!==e&&null!==e.dependencies&&Ci(e.dependencies)?("function"==typeof l&&(hl(t,n,l,r),p=t.memoizedState),(c=ra||gl(t,n,c,r,f,p,s)||null!==e&&null!==e.dependencies&&Ci(e.dependencies))?(u||"function"!=typeof a.UNSAFE_componentWillUpdate&&"function"!=typeof a.componentWillUpdate||("function"==typeof a.componentWillUpdate&&a.componentWillUpdate(r,p,s),"function"==typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,p,s)),"function"==typeof a.componentDidUpdate&&(t.flags|=4),"function"==typeof a.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof a.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),a.props=r,a.state=p,a.context=s,r=c):("function"!=typeof a.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return a=r,Dl(e,t),r=!!(128&t.flags),a||r?(a=t.stateNode,n=r&&"function"!=typeof n.getDerivedStateFromError?null:a.render(),t.flags|=1,null!==e&&r?(t.child=il(t,e.child,null,i),t.child=il(t,null,n,i)):Nl(e,t,n,i),t.memoizedState=a.state,e=t.child):e=Xl(e,t,i),e}function Hl(e,t,n,r){return mi(),t.flags|=256,Nl(e,t,n,r),t.child}var Rl={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Vl(e){return{baseLanes:e,cachePool:qi()}}function Bl(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=bu),e}function Ul(e,t,n){var i,a=t.pendingProps,o=!1,l=!!(128&t.flags);if((i=l)||(i=(null===e||null!==e.memoizedState)&&!!(2&fl.current)),i&&(o=!0,t.flags&=-129),i=!!(32&t.flags),t.flags&=-33,null===e){if(li){if(o?sl(t):cl(),li){var s,u=oi;if(s=u){e:{for(s=u,u=ui;8!==s.nodeType;){if(!u){u=null;break e}if(null===(s=yd(s.nextSibling))){u=null;break e}}u=s}null!==u?(t.memoizedState={dehydrated:u,treeContext:null!==Gr?{id:Jr,overflow:ei}:null,retryLane:536870912,hydrationErrors:null},(s=Fr(18,null,null,0)).stateNode=u,s.return=t,t.child=s,ai=t,oi=null,s=!0):s=!1}s||di(t)}if(null!==(u=t.memoizedState)&&null!==(u=u.dehydrated))return bd(u)?t.lanes=32:t.lanes=536870912,null;dl(t)}return u=a.children,a=a.fallback,o?(cl(),u=ql({mode:"hidden",children:u},o=t.mode),a=Br(a,o,n,null),u.return=t,a.return=t,u.sibling=a,t.child=u,(o=t.child).memoizedState=Vl(n),o.childLanes=Bl(e,i,n),t.memoizedState=Rl,a):(sl(t),Wl(t,u))}if(null!==(s=e.memoizedState)&&null!==(u=s.dehydrated)){if(l)256&t.flags?(sl(t),t.flags&=-257,t=Ql(e,t,n)):null!==t.memoizedState?(cl(),t.child=e.child,t.flags|=128,t=null):(cl(),o=a.fallback,u=t.mode,a=ql({mode:"visible",children:a.children},u),(o=Br(o,u,n,null)).flags|=2,a.return=t,o.return=t,a.sibling=o,t.child=a,il(t,e.child,null,n),(a=t.child).memoizedState=Vl(n),a.childLanes=Bl(e,i,n),t.memoizedState=Rl,t=o);else if(sl(t),bd(u)){if(i=u.nextSibling&&u.nextSibling.dataset)var c=i.dgst;i=c,(a=Error(r(419))).stack="",a.digest=i,vi({value:a,source:null,stack:null}),t=Ql(e,t,n)}else if(Al||Ei(e,t,n,!1),i=0!==(n&e.childLanes),Al||i){if(null!==(i=au)&&(0!==(a=0!==((a=42&(a=n&-n)?1:Ne(a))&(i.suspendedLanes|n))?0:a)&&a!==s.retryLane))throw s.retryLane=a,jr(e,a),Fu(i,e,a),Ll;"$?"===u.data||Yu(),t=Ql(e,t,n)}else"$?"===u.data?(t.flags|=192,t.child=e.child,t=null):(e=s.treeContext,oi=yd(u.nextSibling),ai=t,li=!0,si=null,ui=!1,null!==e&&(Zr[Xr++]=Jr,Zr[Xr++]=ei,Zr[Xr++]=Gr,Jr=e.id,ei=e.overflow,Gr=t),(t=Wl(t,a.children)).flags|=4096);return t}return o?(cl(),o=a.fallback,u=t.mode,c=(s=e.child).sibling,(a=Hr(s,{mode:"hidden",children:a.children})).subtreeFlags=65011712&s.subtreeFlags,null!==c?o=Hr(c,o):(o=Br(o,u,n,null)).flags|=2,o.return=t,a.return=t,a.sibling=o,t.child=a,a=o,o=t.child,null===(u=e.child.memoizedState)?u=Vl(n):(null!==(s=u.cachePool)?(c=zi._currentValue,s=s.parent!==c?{parent:c,pool:c}:s):s=qi(),u={baseLanes:u.baseLanes|n,cachePool:s}),o.memoizedState=u,o.childLanes=Bl(e,i,n),t.memoizedState=Rl,a):(sl(t),e=(n=e.child).sibling,(n=Hr(n,{mode:"visible",children:a.children})).return=t,n.sibling=null,null!==e&&(null===(i=t.deletions)?(t.deletions=[e],t.flags|=16):i.push(e)),t.child=n,t.memoizedState=null,n)}function Wl(e,t){return(t=ql({mode:"visible",children:t},e.mode)).return=e,e.child=t}function ql(e,t){return(e=Fr(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Ql(e,t,n){return il(t,e.child,null,n),(e=Wl(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Kl(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),xi(e.return,t,n)}function Yl(e,t,n,r,i){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=i)}function Zl(e,t,n){var r=t.pendingProps,i=r.revealOrder,a=r.tail;if(Nl(e,t,r.children,n),2&(r=fl.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Kl(e,n,t);else if(19===e.tag)Kl(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(B(fl,r),i){case"forwards":for(n=t.child,i=null;null!==n;)null!==(e=n.alternate)&&null===pl(e)&&(i=n),n=n.sibling;null===(n=i)?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Yl(t,!1,i,n,a);break;case"backwards":for(n=null,i=t.child,t.child=null;null!==i;){if(null!==(e=i.alternate)&&null===pl(e)){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Yl(t,!0,n,null,a);break;case"together":Yl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Xl(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),mu|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Ei(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(r(153));if(null!==t.child){for(n=Hr(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Hr(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Gl(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!Ci(e))}function Jl(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Al=!0;else{if(!(Gl(e,n)||128&t.flags))return Al=!1,function(e,t,n){switch(t.tag){case 3:K(t,t.stateNode.containerInfo),wi(0,zi,e.memoizedState.cache),mi();break;case 27:case 5:Z(t);break;case 4:K(t,t.stateNode.containerInfo);break;case 10:wi(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(sl(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Ul(e,t,n):(sl(t),null!==(e=Xl(e,t,n))?e.sibling:null);sl(t);break;case 19:var i=!!(128&e.flags);if((r=0!==(n&t.childLanes))||(Ei(e,t,n,!1),r=0!==(n&t.childLanes)),i){if(r)return Zl(e,t,n);t.flags|=128}if(null!==(i=t.memoizedState)&&(i.rendering=null,i.tail=null,i.lastEffect=null),B(fl,fl.current),r)break;return null;case 22:case 23:return t.lanes=0,zl(e,t,n);case 24:wi(0,zi,e.memoizedState.cache)}return Xl(e,t,n)}(e,t,n);Al=!!(131072&e.flags)}else Al=!1,li&&1048576&t.flags&&ni(t,Yr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var i=t.elementType,a=i._init;if(i=a(i._payload),t.type=i,"function"!=typeof i){if(null!=i){if((a=i.$$typeof)===w){t.tag=11,t=Pl(null,t,i,e,n);break e}if(a===E){t.tag=14,t=Ol(null,t,i,e,n);break e}}throw t=z(i)||i,Error(r(306,t,""))}$r(i)?(e=bl(i,e),t.tag=1,t=$l(null,t,i,e,n)):(t.tag=0,t=Il(null,t,i,e,n))}return t;case 0:return Il(e,t,t.type,t.pendingProps,n);case 1:return $l(e,t,i=t.type,a=bl(i,t.pendingProps),n);case 3:e:{if(K(t,t.stateNode.containerInfo),null===e)throw Error(r(387));i=t.pendingProps;var o=t.memoizedState;a=o.element,aa(e,t),fa(t,i,null,n);var l=t.memoizedState;if(i=l.cache,wi(0,zi,i),i!==o.cache&&Si(t,[zi],n,!0),da(),i=l.element,o.isDehydrated){if(o={element:i,isDehydrated:!1,cache:l.cache},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Hl(e,t,i,n);break e}if(i!==a){vi(a=Cr(Error(r(424)),t)),t=Hl(e,t,i,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(oi=yd(e.firstChild),ai=t,li=!0,si=null,ui=!0,n=al(t,null,i,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(mi(),i===a){t=Xl(e,t,n);break e}Nl(e,t,i,n)}t=t.child}return t;case 26:return Dl(e,t),null===e?(n=Nd(t.type,null,t.pendingProps,null))?t.memoizedState=n:li||(n=t.type,e=t.pendingProps,(i=ad(q.current).createElement(n))[ze]=t,i[Me]=e,nd(i,n,e),Qe(i),t.stateNode=i):t.memoizedState=Nd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Z(t),null===e&&li&&(i=t.stateNode=kd(t.type,t.pendingProps,q.current),ai=t,ui=!0,a=oi,md(t.type)?(_d=a,oi=yd(i.firstChild)):oi=a),Nl(e,t,t.pendingProps.children,n),Dl(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&li&&((a=i=oi)&&(null!==(i=function(e,t,n,r){for(;1===e.nodeType;){var i=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Re])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(a=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(a!==i.rel||e.getAttribute("href")!==(null==i.href||""===i.href?null:i.href)||e.getAttribute("crossorigin")!==(null==i.crossOrigin?null:i.crossOrigin)||e.getAttribute("title")!==(null==i.title?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((a=e.getAttribute("src"))!==(null==i.src?null:i.src)||e.getAttribute("type")!==(null==i.type?null:i.type)||e.getAttribute("crossorigin")!==(null==i.crossOrigin?null:i.crossOrigin))&&a&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var a=null==i.name?null:""+i.name;if("hidden"===i.type&&e.getAttribute("name")===a)return e}if(null===(e=yd(e.nextSibling)))break}return null}(i,t.type,t.pendingProps,ui))?(t.stateNode=i,ai=t,oi=yd(i.firstChild),ui=!1,a=!0):a=!1),a||di(t)),Z(t),a=t.type,o=t.pendingProps,l=null!==e?e.memoizedProps:null,i=o.children,sd(a,o)?i=null:null!==l&&sd(a,l)&&(t.flags|=32),null!==t.memoizedState&&(a=ja(e,t,Da,null,null,n),Zd._currentValue=a),Dl(e,t),Nl(e,t,i,n),t.child;case 6:return null===e&&li&&((e=n=oi)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=yd(e.nextSibling)))return null}return e}(n,t.pendingProps,ui))?(t.stateNode=n,ai=t,oi=null,e=!0):e=!1),e||di(t)),null;case 13:return Ul(e,t,n);case 4:return K(t,t.stateNode.containerInfo),i=t.pendingProps,null===e?t.child=il(t,null,i,n):Nl(e,t,i,n),t.child;case 11:return Pl(e,t,t.type,t.pendingProps,n);case 7:return Nl(e,t,t.pendingProps,n),t.child;case 8:case 12:return Nl(e,t,t.pendingProps.children,n),t.child;case 10:return i=t.pendingProps,wi(0,t.type,i.value),Nl(e,t,i.children,n),t.child;case 9:return a=t.type._context,i=t.pendingProps.children,Ti(t),i=i(a=Li(a)),t.flags|=1,Nl(e,t,i,n),t.child;case 14:return Ol(e,t,t.type,t.pendingProps,n);case 15:return jl(e,t,t.type,t.pendingProps,n);case 19:return Zl(e,t,n);case 31:return i=t.pendingProps,n=t.mode,i={mode:i.mode,children:i.children},null===e?((n=ql(i,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Hr(e.child,i)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return zl(e,t,n);case 24:return Ti(t),i=Li(zi),null===e?(null===(a=Ui())&&(a=au,o=Mi(),a.pooledCache=o,o.refCount++,null!==o&&(a.pooledCacheLanes|=n),a=o),t.memoizedState={parent:i,cache:a},ia(t),wi(0,zi,a)):(0!==(e.lanes&n)&&(aa(e,t),fa(t,null,null,n),da()),a=e.memoizedState,o=t.memoizedState,a.parent!==i?(a={parent:i,cache:i},t.memoizedState=a,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=a),wi(0,zi,i)):(i=o.cache,wi(0,zi,i),i!==a.cache&&Si(t,[zi],n,!0))),Nl(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(r(156,t.tag))}function es(e){e.flags|=4}function ts(e,t){if("stylesheet"!==t.type||4&t.state.loading)e.flags&=-16777217;else if(e.flags|=16777216,!Bd(t)){if(null!==(t=ol.current)&&((4194048&lu)===lu?null!==ll:(62914560&lu)!==lu&&!(536870912&lu)||t!==ll))throw ea=Zi,Ki;e.flags|=8192}}function ns(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Ee():536870912,e.lanes|=t,yu|=t)}function rs(e,t){if(!li)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function is(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=65011712&i.subtreeFlags,r|=65011712&i.flags,i.return=e,i=i.sibling;else for(i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function as(e,t,n){var i=t.pendingProps;switch(ii(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return is(t),null;case 3:return n=t.stateNode,i=null,null!==e&&(i=e.memoizedState.cache),t.memoizedState.cache!==i&&(t.flags|=2048),ki(zi),Y(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(hi(t)?es(t):null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,gi())),is(t),null;case 26:return n=t.memoizedState,null===e?(es(t),null!==n?(is(t),ts(t,n)):(is(t),t.flags&=-16777217)):n?n!==e.memoizedState?(es(t),is(t),ts(t,n)):(is(t),t.flags&=-16777217):(e.memoizedProps!==i&&es(t),is(t),t.flags&=-16777217),null;case 27:X(t),n=q.current;var a=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==i&&es(t);else{if(!i){if(null===t.stateNode)throw Error(r(166));return is(t),null}e=U.current,hi(t)?fi(t):(e=kd(a,i,n),t.stateNode=e,es(t))}return is(t),null;case 5:if(X(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==i&&es(t);else{if(!i){if(null===t.stateNode)throw Error(r(166));return is(t),null}if(e=U.current,hi(t))fi(t);else{switch(a=ad(q.current),e){case 1:e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"==typeof i.is?a.createElement("select",{is:i.is}):a.createElement("select"),i.multiple?e.multiple=!0:i.size&&(e.size=i.size);break;default:e="string"==typeof i.is?a.createElement(n,{is:i.is}):a.createElement(n)}}e[ze]=t,e[Me]=i;e:for(a=t.child;null!==a;){if(5===a.tag||6===a.tag)e.appendChild(a.stateNode);else if(4!==a.tag&&27!==a.tag&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break e;for(;null===a.sibling;){if(null===a.return||a.return===t)break e;a=a.return}a.sibling.return=a.return,a=a.sibling}t.stateNode=e;e:switch(nd(e,n,i),n){case"button":case"input":case"select":case"textarea":e=!!i.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&es(t)}}return is(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==i&&es(t);else{if("string"!=typeof i&&null===t.stateNode)throw Error(r(166));if(e=q.current,hi(t)){if(e=t.stateNode,n=t.memoizedProps,i=null,null!==(a=ai))switch(a.tag){case 27:case 5:i=a.memoizedProps}e[ze]=t,(e=!!(e.nodeValue===n||null!==i&&!0===i.suppressHydrationWarning||Gc(e.nodeValue,n)))||di(t)}else(e=ad(e).createTextNode(i))[ze]=t,t.stateNode=e}return is(t),null;case 13:if(i=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=hi(t),null!==i&&null!==i.dehydrated){if(null===e){if(!a)throw Error(r(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(r(317));a[ze]=t}else mi(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;is(t),a=!1}else a=gi(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return 256&t.flags?(dl(t),t):(dl(t),null)}if(dl(t),128&t.flags)return t.lanes=n,t;if(n=null!==i,e=null!==e&&null!==e.memoizedState,n){a=null,null!==(i=t.child).alternate&&null!==i.alternate.memoizedState&&null!==i.alternate.memoizedState.cachePool&&(a=i.alternate.memoizedState.cachePool.pool);var o=null;null!==i.memoizedState&&null!==i.memoizedState.cachePool&&(o=i.memoizedState.cachePool.pool),o!==a&&(i.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),ns(t,t.updateQueue),is(t),null;case 4:return Y(),null===e&&Vc(t.stateNode.containerInfo),is(t),null;case 10:return ki(t.type),is(t),null;case 19:if(V(fl),null===(a=t.memoizedState))return is(t),null;if(i=!!(128&t.flags),null===(o=a.rendering))if(i)rs(a,!1);else{if(0!==hu||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(o=pl(e))){for(t.flags|=128,rs(a,!1),e=o.updateQueue,t.updateQueue=e,ns(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Rr(n,e),n=n.sibling;return B(fl,1&fl.current|2),t.child}e=e.sibling}null!==a.tail&&re()>Su&&(t.flags|=128,i=!0,rs(a,!1),t.lanes=4194304)}else{if(!i)if(null!==(e=pl(o))){if(t.flags|=128,i=!0,e=e.updateQueue,t.updateQueue=e,ns(t,e),rs(a,!0),null===a.tail&&"hidden"===a.tailMode&&!o.alternate&&!li)return is(t),null}else 2*re()-a.renderingStartTime>Su&&536870912!==n&&(t.flags|=128,i=!0,rs(a,!1),t.lanes=4194304);a.isBackwards?(o.sibling=t.child,t.child=o):(null!==(e=a.last)?e.sibling=o:t.child=o,a.last=o)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=re(),t.sibling=null,e=fl.current,B(fl,i?1&e|2:1&e),t):(is(t),null);case 22:case 23:return dl(t),ya(),i=null!==t.memoizedState,null!==e?null!==e.memoizedState!==i&&(t.flags|=8192):i&&(t.flags|=8192),i?!!(536870912&n)&&!(128&t.flags)&&(is(t),6&t.subtreeFlags&&(t.flags|=8192)):is(t),null!==(n=t.updateQueue)&&ns(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),i=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(i=t.memoizedState.cachePool.pool),i!==n&&(t.flags|=2048),null!==e&&V(Bi),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),ki(zi),is(t),null;case 25:case 30:return null}throw Error(r(156,t.tag))}function os(e,t){switch(ii(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return ki(zi),Y(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return X(t),null;case 13:if(dl(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(r(340));mi()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return V(fl),null;case 4:return Y(),null;case 10:return ki(t.type),null;case 22:case 23:return dl(t),ya(),null!==e&&V(Bi),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return ki(zi),null;default:return null}}function ls(e,t){switch(ii(t),t.tag){case 3:ki(zi),Y();break;case 26:case 27:case 5:X(t);break;case 4:Y();break;case 13:dl(t);break;case 19:V(fl);break;case 10:ki(t.type);break;case 22:case 23:dl(t),ya(),null!==e&&V(Bi);break;case 24:ki(zi)}}function ss(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var i=r.next;n=i;do{if((n.tag&e)===e){r=void 0;var a=n.create,o=n.inst;r=a(),o.destroy=r}n=n.next}while(n!==i)}}catch(l){fc(t,t.return,l)}}function us(e,t,n){try{var r=t.updateQueue,i=null!==r?r.lastEffect:null;if(null!==i){var a=i.next;r=a;do{if((r.tag&e)===e){var o=r.inst,l=o.destroy;if(void 0!==l){o.destroy=void 0,i=t;var s=n,u=l;try{u()}catch(c){fc(i,s,c)}}}r=r.next}while(r!==a)}}catch(c){fc(t,t.return,c)}}function cs(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{ha(t,n)}catch(r){fc(e,e.return,r)}}}function ds(e,t,n){n.props=bl(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){fc(e,t,r)}}function fs(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"==typeof n?e.refCleanup=n(r):n.current=r}}catch(i){fc(e,t,i)}}function ps(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"==typeof r)try{r()}catch(i){fc(e,t,i)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"==typeof n)try{n(null)}catch(a){fc(e,t,a)}else n.current=null}function hs(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(i){fc(e,e.return,i)}}function ms(e,t,n){try{var i=e.stateNode;!function(e,t,n,i){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,o=null,l=null,s=null,u=null,c=null,d=null;for(h in n){var f=n[h];if(n.hasOwnProperty(h)&&null!=f)switch(h){case"checked":case"value":break;case"defaultValue":u=f;default:i.hasOwnProperty(h)||ed(e,t,h,null,i,f)}}for(var p in i){var h=i[p];if(f=n[p],i.hasOwnProperty(p)&&(null!=h||null!=f))switch(p){case"type":o=h;break;case"name":a=h;break;case"checked":c=h;break;case"defaultChecked":d=h;break;case"value":l=h;break;case"defaultValue":s=h;break;case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(r(137,t));break;default:h!==f&&ed(e,t,p,h,i,f)}}return void bt(e,l,s,u,c,d,o,a);case"select":for(o in h=l=s=p=null,n)if(u=n[o],n.hasOwnProperty(o)&&null!=u)switch(o){case"value":break;case"multiple":h=u;default:i.hasOwnProperty(o)||ed(e,t,o,null,i,u)}for(a in i)if(o=i[a],u=n[a],i.hasOwnProperty(a)&&(null!=o||null!=u))switch(a){case"value":p=o;break;case"defaultValue":s=o;break;case"multiple":l=o;default:o!==u&&ed(e,t,a,o,i,u)}return t=s,n=l,i=h,void(null!=p?wt(e,!!n,p,!1):!!i!=!!n&&(null!=t?wt(e,!!n,t,!0):wt(e,!!n,n?[]:"",!1)));case"textarea":for(s in h=p=null,n)if(a=n[s],n.hasOwnProperty(s)&&null!=a&&!i.hasOwnProperty(s))switch(s){case"value":case"children":break;default:ed(e,t,s,null,i,a)}for(l in i)if(a=i[l],o=n[l],i.hasOwnProperty(l)&&(null!=a||null!=o))switch(l){case"value":p=a;break;case"defaultValue":h=a;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(r(91));break;default:a!==o&&ed(e,t,l,a,i,o)}return void kt(e,p,h);case"option":for(var m in n)if(p=n[m],n.hasOwnProperty(m)&&null!=p&&!i.hasOwnProperty(m))if("selected"===m)e.selected=!1;else ed(e,t,m,null,i,p);for(u in i)if(p=i[u],h=n[u],i.hasOwnProperty(u)&&p!==h&&(null!=p||null!=h))if("selected"===u)e.selected=p&&"function"!=typeof p&&"symbol"!=typeof p;else ed(e,t,u,p,i,h);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)p=n[g],n.hasOwnProperty(g)&&null!=p&&!i.hasOwnProperty(g)&&ed(e,t,g,null,i,p);for(c in i)if(p=i[c],h=n[c],i.hasOwnProperty(c)&&p!==h&&(null!=p||null!=h))switch(c){case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(r(137,t));break;default:ed(e,t,c,p,i,h)}return;default:if(Lt(t)){for(var v in n)p=n[v],n.hasOwnProperty(v)&&void 0!==p&&!i.hasOwnProperty(v)&&td(e,t,v,void 0,i,p);for(d in i)p=i[d],h=n[d],!i.hasOwnProperty(d)||p===h||void 0===p&&void 0===h||td(e,t,d,p,i,h);return}}for(var b in n)p=n[b],n.hasOwnProperty(b)&&null!=p&&!i.hasOwnProperty(b)&&ed(e,t,b,null,i,p);for(f in i)p=i[f],h=n[f],!i.hasOwnProperty(f)||p===h||null==p&&null==h||ed(e,t,f,p,i,h)}(i,e.type,n,t),i[Me]=t}catch(a){fc(e,e.return,a)}}function gs(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&md(e.type)||4===e.tag}function vs(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||gs(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&md(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function bs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Jc));else if(4!==r&&(27===r&&md(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(bs(e,t,n),e=e.sibling;null!==e;)bs(e,t,n),e=e.sibling}function ys(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&md(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(ys(e,t,n),e=e.sibling;null!==e;)ys(e,t,n),e=e.sibling}function _s(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);nd(t,r,n),t[ze]=e,t[Me]=n}catch(a){fc(e,e.return,a)}}var ws=!1,ks=!1,xs=!1,Ss="function"==typeof WeakSet?WeakSet:Set,Es=null;function Cs(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:$s(e,n),4&r&&ss(5,n);break;case 1:if($s(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(o){fc(n,n.return,o)}else{var i=bl(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(l){fc(n,n.return,l)}}64&r&&cs(n),512&r&&fs(n,n.return);break;case 3:if($s(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{ha(e,t)}catch(o){fc(n,n.return,o)}}break;case 27:null===t&&4&r&&_s(n);case 26:case 5:$s(e,n),null===t&&4&r&&hs(n),512&r&&fs(n,n.return);break;case 12:$s(e,n);break;case 13:$s(e,n),4&r&&Os(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=gc.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||ws)){t=null!==t&&null!==t.memoizedState||ks,i=ws;var a=ks;ws=r,(ks=t)&&!a?Rs(e,n,!!(8772&n.subtreeFlags)):$s(e,n),ws=i,ks=a}break;case 30:break;default:$s(e,n)}}function Ts(e){var t=e.alternate;null!==t&&(e.alternate=null,Ts(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&Ve(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ls=null,As=!1;function Ns(e,t,n){for(n=n.child;null!==n;)Ps(e,t,n),n=n.sibling}function Ps(e,t,n){if(pe&&"function"==typeof pe.onCommitFiberUnmount)try{pe.onCommitFiberUnmount(fe,n)}catch(a){}switch(n.tag){case 26:ks||ps(n,t),Ns(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:ks||ps(n,t);var r=Ls,i=As;md(n.type)&&(Ls=n.stateNode,As=!1),Ns(e,t,n),xd(n.stateNode),Ls=r,As=i;break;case 5:ks||ps(n,t);case 6:if(r=Ls,i=As,Ls=null,Ns(e,t,n),As=i,null!==(Ls=r))if(As)try{(9===Ls.nodeType?Ls.body:"HTML"===Ls.nodeName?Ls.ownerDocument.body:Ls).removeChild(n.stateNode)}catch(o){fc(n,t,o)}else try{Ls.removeChild(n.stateNode)}catch(o){fc(n,t,o)}break;case 18:null!==Ls&&(As?(gd(9===(e=Ls).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Nf(e)):gd(Ls,n.stateNode));break;case 4:r=Ls,i=As,Ls=n.stateNode.containerInfo,As=!0,Ns(e,t,n),Ls=r,As=i;break;case 0:case 11:case 14:case 15:ks||us(2,n,t),ks||us(4,n,t),Ns(e,t,n);break;case 1:ks||(ps(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount&&ds(n,t,r)),Ns(e,t,n);break;case 21:Ns(e,t,n);break;case 22:ks=(r=ks)||null!==n.memoizedState,Ns(e,t,n),ks=r;break;default:Ns(e,t,n)}}function Os(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Nf(e)}catch(n){fc(t,t.return,n)}}function js(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new Ss),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new Ss),t;default:throw Error(r(435,e.tag))}}(e);t.forEach((function(t){var r=vc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}function zs(e,t){var n=t.deletions;if(null!==n)for(var i=0;i<n.length;i++){var a=n[i],o=e,l=t,s=l;e:for(;null!==s;){switch(s.tag){case 27:if(md(s.type)){Ls=s.stateNode,As=!1;break e}break;case 5:Ls=s.stateNode,As=!1;break e;case 3:case 4:Ls=s.stateNode.containerInfo,As=!0;break e}s=s.return}if(null===Ls)throw Error(r(160));Ps(o,l,a),Ls=null,As=!1,null!==(o=a.alternate)&&(o.return=null),a.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Ds(t,e),t=t.sibling}var Ms=null;function Ds(e,t){var n=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:zs(t,e),Is(e),4&i&&(us(3,e,e.return),ss(3,e),us(5,e,e.return));break;case 1:zs(t,e),Is(e),512&i&&(ks||null===n||ps(n,n.return)),64&i&&ws&&(null!==(e=e.updateQueue)&&(null!==(i=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?i:n.concat(i))));break;case 26:var a=Ms;if(zs(t,e),Is(e),512&i&&(ks||null===n||ps(n,n.return)),4&i){var o=null!==n?n.memoizedState:null;if(i=e.memoizedState,null===n)if(null===i)if(null===e.stateNode){e:{i=e.type,n=e.memoizedProps,a=a.ownerDocument||a;t:switch(i){case"title":(!(o=a.getElementsByTagName("title")[0])||o[Re]||o[ze]||"http://www.w3.org/2000/svg"===o.namespaceURI||o.hasAttribute("itemprop"))&&(o=a.createElement(i),a.head.insertBefore(o,a.querySelector("head > title"))),nd(o,i,n),o[ze]=e,Qe(o),i=o;break e;case"link":var l=Rd("link","href",a).get(i+(n.href||""));if(l)for(var s=0;s<l.length;s++)if((o=l[s]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&o.getAttribute("rel")===(null==n.rel?null:n.rel)&&o.getAttribute("title")===(null==n.title?null:n.title)&&o.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){l.splice(s,1);break t}nd(o=a.createElement(i),i,n),a.head.appendChild(o);break;case"meta":if(l=Rd("meta","content",a).get(i+(n.content||"")))for(s=0;s<l.length;s++)if((o=l[s]).getAttribute("content")===(null==n.content?null:""+n.content)&&o.getAttribute("name")===(null==n.name?null:n.name)&&o.getAttribute("property")===(null==n.property?null:n.property)&&o.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&o.getAttribute("charset")===(null==n.charSet?null:n.charSet)){l.splice(s,1);break t}nd(o=a.createElement(i),i,n),a.head.appendChild(o);break;default:throw Error(r(468,i))}o[ze]=e,Qe(o),i=o}e.stateNode=i}else Vd(a,e.type,e.stateNode);else e.stateNode=Dd(a,i,e.memoizedProps);else o!==i?(null===o?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):o.count--,null===i?Vd(a,e.type,e.stateNode):Dd(a,i,e.memoizedProps)):null===i&&null!==e.stateNode&&ms(e,e.memoizedProps,n.memoizedProps)}break;case 27:zs(t,e),Is(e),512&i&&(ks||null===n||ps(n,n.return)),null!==n&&4&i&&ms(e,e.memoizedProps,n.memoizedProps);break;case 5:if(zs(t,e),Is(e),512&i&&(ks||null===n||ps(n,n.return)),32&e.flags){a=e.stateNode;try{St(a,"")}catch(h){fc(e,e.return,h)}}4&i&&null!=e.stateNode&&ms(e,a=e.memoizedProps,null!==n?n.memoizedProps:a),1024&i&&(xs=!0);break;case 6:if(zs(t,e),Is(e),4&i){if(null===e.stateNode)throw Error(r(162));i=e.memoizedProps,n=e.stateNode;try{n.nodeValue=i}catch(h){fc(e,e.return,h)}}break;case 3:if(Hd=null,a=Ms,Ms=Cd(t.containerInfo),zs(t,e),Ms=a,Is(e),4&i&&null!==n&&n.memoizedState.isDehydrated)try{Nf(t.containerInfo)}catch(h){fc(e,e.return,h)}xs&&(xs=!1,Fs(e));break;case 4:i=Ms,Ms=Cd(e.stateNode.containerInfo),zs(t,e),Is(e),Ms=i;break;case 12:default:zs(t,e),Is(e);break;case 13:zs(t,e),Is(e),8192&e.child.flags&&null!==e.memoizedState!=(null!==n&&null!==n.memoizedState)&&(xu=re()),4&i&&(null!==(i=e.updateQueue)&&(e.updateQueue=null,js(e,i)));break;case 22:a=null!==e.memoizedState;var u=null!==n&&null!==n.memoizedState,c=ws,d=ks;if(ws=c||a,ks=d||u,zs(t,e),ks=d,ws=c,Is(e),8192&i)e:for(t=e.stateNode,t._visibility=a?-2&t._visibility:1|t._visibility,a&&(null===n||u||ws||ks||Hs(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){u=n=t;try{if(o=u.stateNode,a)"function"==typeof(l=o.style).setProperty?l.setProperty("display","none","important"):l.display="none";else{s=u.stateNode;var f=u.memoizedProps.style,p=null!=f&&f.hasOwnProperty("display")?f.display:null;s.style.display=null==p||"boolean"==typeof p?"":(""+p).trim()}}catch(h){fc(u,u.return,h)}}}else if(6===t.tag){if(null===n){u=t;try{u.stateNode.nodeValue=a?"":u.memoizedProps}catch(h){fc(u,u.return,h)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&i&&(null!==(i=e.updateQueue)&&(null!==(n=i.retryQueue)&&(i.retryQueue=null,js(e,n))));break;case 19:zs(t,e),Is(e),4&i&&(null!==(i=e.updateQueue)&&(e.updateQueue=null,js(e,i)));case 30:case 21:}}function Is(e){var t=e.flags;if(2&t){try{for(var n,i=e.return;null!==i;){if(gs(i)){n=i;break}i=i.return}if(null==n)throw Error(r(160));switch(n.tag){case 27:var a=n.stateNode;ys(e,vs(e),a);break;case 5:var o=n.stateNode;32&n.flags&&(St(o,""),n.flags&=-33),ys(e,vs(e),o);break;case 3:case 4:var l=n.stateNode.containerInfo;bs(e,vs(e),l);break;default:throw Error(r(161))}}catch(s){fc(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Fs(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;Fs(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function $s(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)Cs(e,t.alternate,t),t=t.sibling}function Hs(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:us(4,t,t.return),Hs(t);break;case 1:ps(t,t.return);var n=t.stateNode;"function"==typeof n.componentWillUnmount&&ds(t,t.return,n),Hs(t);break;case 27:xd(t.stateNode);case 26:case 5:ps(t,t.return),Hs(t);break;case 22:null===t.memoizedState&&Hs(t);break;default:Hs(t)}e=e.sibling}}function Rs(e,t,n){for(n=n&&!!(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,i=e,a=t,o=a.flags;switch(a.tag){case 0:case 11:case 15:Rs(i,a,n),ss(4,a);break;case 1:if(Rs(i,a,n),"function"==typeof(i=(r=a).stateNode).componentDidMount)try{i.componentDidMount()}catch(u){fc(r,r.return,u)}if(null!==(i=(r=a).updateQueue)){var l=r.stateNode;try{var s=i.shared.hiddenCallbacks;if(null!==s)for(i.shared.hiddenCallbacks=null,i=0;i<s.length;i++)pa(s[i],l)}catch(u){fc(r,r.return,u)}}n&&64&o&&cs(a),fs(a,a.return);break;case 27:_s(a);case 26:case 5:Rs(i,a,n),n&&null===r&&4&o&&hs(a),fs(a,a.return);break;case 12:Rs(i,a,n);break;case 13:Rs(i,a,n),n&&4&o&&Os(i,a);break;case 22:null===a.memoizedState&&Rs(i,a,n),fs(a,a.return);break;case 30:break;default:Rs(i,a,n)}t=t.sibling}}function Vs(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Di(n))}function Bs(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Di(e))}function Us(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Ws(e,t,n,r),t=t.sibling}function Ws(e,t,n,r){var i=t.flags;switch(t.tag){case 0:case 11:case 15:Us(e,t,n,r),2048&i&&ss(9,t);break;case 1:case 13:default:Us(e,t,n,r);break;case 3:Us(e,t,n,r),2048&i&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Di(e)));break;case 12:if(2048&i){Us(e,t,n,r),e=t.stateNode;try{var a=t.memoizedProps,o=a.id,l=a.onPostCommit;"function"==typeof l&&l(o,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(s){fc(t,t.return,s)}}else Us(e,t,n,r);break;case 23:break;case 22:a=t.stateNode,o=t.alternate,null!==t.memoizedState?2&a._visibility?Us(e,t,n,r):Qs(e,t):2&a._visibility?Us(e,t,n,r):(a._visibility|=2,qs(e,t,n,r,!!(10256&t.subtreeFlags))),2048&i&&Vs(o,t);break;case 24:Us(e,t,n,r),2048&i&&Bs(t.alternate,t)}}function qs(e,t,n,r,i){for(i=i&&!!(10256&t.subtreeFlags),t=t.child;null!==t;){var a=e,o=t,l=n,s=r,u=o.flags;switch(o.tag){case 0:case 11:case 15:qs(a,o,l,s,i),ss(8,o);break;case 23:break;case 22:var c=o.stateNode;null!==o.memoizedState?2&c._visibility?qs(a,o,l,s,i):Qs(a,o):(c._visibility|=2,qs(a,o,l,s,i)),i&&2048&u&&Vs(o.alternate,o);break;case 24:qs(a,o,l,s,i),i&&2048&u&&Bs(o.alternate,o);break;default:qs(a,o,l,s,i)}t=t.sibling}}function Qs(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,i=r.flags;switch(r.tag){case 22:Qs(n,r),2048&i&&Vs(r.alternate,r);break;case 24:Qs(n,r),2048&i&&Bs(r.alternate,r);break;default:Qs(n,r)}t=t.sibling}}var Ks=8192;function Ys(e){if(e.subtreeFlags&Ks)for(e=e.child;null!==e;)Zs(e),e=e.sibling}function Zs(e){switch(e.tag){case 26:Ys(e),e.flags&Ks&&null!==e.memoizedState&&function(e,t,n){if(null===Ud)throw Error(r(475));var i=Ud;if(!("stylesheet"!==t.type||"string"==typeof n.media&&!1===matchMedia(n.media).matches||4&t.state.loading)){if(null===t.instance){var a=Pd(n.href),o=e.querySelector(Od(a));if(o)return null!==(e=o._p)&&"object"==typeof e&&"function"==typeof e.then&&(i.count++,i=qd.bind(i),e.then(i,i)),t.state.loading|=4,t.instance=o,void Qe(o);o=e.ownerDocument||e,n=jd(n),(a=Sd.get(a))&&Fd(n,a),Qe(o=o.createElement("link"));var l=o;l._p=new Promise((function(e,t){l.onload=e,l.onerror=t})),nd(o,"link",n),t.instance=o}null===i.stylesheets&&(i.stylesheets=new Map),i.stylesheets.set(t,e),(e=t.state.preload)&&!(3&t.state.loading)&&(i.count++,t=qd.bind(i),e.addEventListener("load",t),e.addEventListener("error",t))}}(Ms,e.memoizedState,e.memoizedProps);break;case 5:default:Ys(e);break;case 3:case 4:var t=Ms;Ms=Cd(e.stateNode.containerInfo),Ys(e),Ms=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Ks,Ks=16777216,Ys(e),Ks=t):Ys(e))}}function Xs(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Gs(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Es=r,tu(r,e)}Xs(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Js(e),e=e.sibling}function Js(e){switch(e.tag){case 0:case 11:case 15:Gs(e),2048&e.flags&&us(9,e,e.return);break;case 3:case 12:default:Gs(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,eu(e)):Gs(e)}}function eu(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Es=r,tu(r,e)}Xs(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:us(8,t,t.return),eu(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,eu(t));break;default:eu(t)}e=e.sibling}}function tu(e,t){for(;null!==Es;){var n=Es;switch(n.tag){case 0:case 11:case 15:us(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Di(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,Es=r;else e:for(n=e;null!==Es;){var i=(r=Es).sibling,a=r.return;if(Ts(r),r===n){Es=null;break e}if(null!==i){i.return=a,Es=i;break e}Es=a}}}var nu={getCacheForType:function(e){var t=Li(zi),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},ru="function"==typeof WeakMap?WeakMap:Map,iu=0,au=null,ou=null,lu=0,su=0,uu=null,cu=!1,du=!1,fu=!1,pu=0,hu=0,mu=0,gu=0,vu=0,bu=0,yu=0,_u=null,wu=null,ku=!1,xu=0,Su=1/0,Eu=null,Cu=null,Tu=0,Lu=null,Au=null,Nu=0,Pu=0,Ou=null,ju=null,zu=0,Mu=null;function Du(){if(2&iu&&0!==lu)return lu&-lu;if(null!==D.T){return 0!==$i?$i:Pc()}return Oe()}function Iu(){0===bu&&(bu=536870912&lu&&!li?536870912:Se());var e=ol.current;return null!==e&&(e.flags|=32),bu}function Fu(e,t,n){(e!==au||2!==su&&9!==su)&&null===e.cancelPendingCommit||(Wu(e,0),Vu(e,lu,bu,!1)),Te(e,n),2&iu&&e===au||(e===au&&(!(2&iu)&&(gu|=n),4===hu&&Vu(e,lu,bu,!1)),Sc(e))}function $u(e,t,n){if(6&iu)throw Error(r(327));for(var i=!n&&!(124&t)&&0===(t&e.expiredLanes)||ke(e,t),a=i?function(e,t){var n=iu;iu|=2;var i=Qu(),a=Ku();au!==e||lu!==t?(Eu=null,Su=re()+500,Wu(e,t)):du=ke(e,t);e:for(;;)try{if(0!==su&&null!==ou){t=ou;var o=uu;t:switch(su){case 1:su=0,uu=null,tc(e,t,o,1);break;case 2:case 9:if(Xi(o)){su=0,uu=null,ec(t);break}t=function(){2!==su&&9!==su||au!==e||(su=7),Sc(e)},o.then(t,t);break e;case 3:su=7;break e;case 4:su=5;break e;case 7:Xi(o)?(su=0,uu=null,ec(t)):(su=0,uu=null,tc(e,t,o,7));break;case 5:var l=null;switch(ou.tag){case 26:l=ou.memoizedState;case 5:case 27:var s=ou;if(!l||Bd(l)){su=0,uu=null;var u=s.sibling;if(null!==u)ou=u;else{var c=s.return;null!==c?(ou=c,nc(c)):ou=null}break t}}su=0,uu=null,tc(e,t,o,5);break;case 6:su=0,uu=null,tc(e,t,o,6);break;case 8:Uu(),hu=6;break e;default:throw Error(r(462))}}Gu();break}catch(d){qu(e,d)}return _i=yi=null,D.H=i,D.A=a,iu=n,null!==ou?0:(au=null,lu=0,Nr(),hu)}(e,t):Zu(e,t,!0),o=i;;){if(0===a){du&&!i&&Vu(e,t,0,!1);break}if(n=e.current.alternate,!o||Ru(n)){if(2===a){if(o=t,e.errorRecoveryDisabledLanes&o)var l=0;else l=0!==(l=-536870913&e.pendingLanes)?l:536870912&l?536870912:0;if(0!==l){t=l;e:{var s=e;a=_u;var u=s.current.memoizedState.isDehydrated;if(u&&(Wu(s,l).flags|=256),2!==(l=Zu(s,l,!1))){if(fu&&!u){s.errorRecoveryDisabledLanes|=o,gu|=o,a=4;break e}o=wu,wu=a,null!==o&&(null===wu?wu=o:wu.push.apply(wu,o))}a=l}if(o=!1,2!==a)continue}}if(1===a){Wu(e,0),Vu(e,t,0,!0);break}e:{switch(i=e,o=a){case 0:case 1:throw Error(r(345));case 4:if((4194048&t)!==t)break;case 6:Vu(i,t,bu,!cu);break e;case 2:wu=null;break;case 3:case 5:break;default:throw Error(r(329))}if((62914560&t)===t&&10<(a=xu+300-re())){if(Vu(i,t,bu,!cu),0!==we(i,0,!0))break e;i.timeoutHandle=cd(Hu.bind(null,i,n,wu,Eu,ku,t,bu,gu,yu,cu,o,2,-0,0),a)}else Hu(i,n,wu,Eu,ku,t,bu,gu,yu,cu,o,0,-0,0)}break}a=Zu(e,t,!1),o=!1}Sc(e)}function Hu(e,t,n,i,a,o,l,s,u,c,d,f,p,h){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||!(16785408&~f))&&(Ud={stylesheets:null,count:0,unsuspend:Wd},Zs(t),null!==(f=function(){if(null===Ud)throw Error(r(475));var e=Ud;return e.stylesheets&&0===e.count&&Kd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout((function(){if(e.stylesheets&&Kd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}}),6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(ic.bind(null,e,t,o,n,i,a,l,s,u,d,1,p,h)),void Vu(e,o,l,!c);ic(e,t,o,n,i,a,l,s,u)}function Ru(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var i=n[r],a=i.getSnapshot;i=i.value;try{if(!Xn(a(),i))return!1}catch(o){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Vu(e,t,n,r){t&=~vu,t&=~gu,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var i=t;0<i;){var a=31-me(i),o=1<<a;r[a]=-1,i&=~o}0!==n&&Le(e,n,t)}function Bu(){return!!(6&iu)||(Ec(0),!1)}function Uu(){if(null!==ou){if(0===su)var e=ou.return;else _i=yi=null,$a(e=ou),Xo=null,Go=0,e=ou;for(;null!==e;)ls(e.alternate,e),e=e.return;ou=null}}function Wu(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,dd(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Uu(),au=e,ou=n=Hr(e.current,null),lu=t,su=0,uu=null,cu=!1,du=ke(e,t),fu=!1,yu=bu=vu=gu=mu=hu=0,wu=_u=null,ku=!1,8&t&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var i=31-me(r),a=1<<i;t|=e[i],r&=~a}return pu=t,Nr(),n}function qu(e,t){wa=null,D.H=Qo,t===Qi||t===Yi?(t=ta(),su=3):t===Ki?(t=ta(),su=4):su=t===Ll?8:null!==t&&"object"==typeof t&&"function"==typeof t.then?6:1,uu=t,null===ou&&(hu=1,xl(e,Cr(t,e.current)))}function Qu(){var e=D.H;return D.H=Qo,null===e?Qo:e}function Ku(){var e=D.A;return D.A=nu,e}function Yu(){hu=4,cu||(4194048&lu)!==lu&&null!==ol.current||(du=!0),!(134217727&mu)&&!(134217727&gu)||null===au||Vu(au,lu,bu,!1)}function Zu(e,t,n){var r=iu;iu|=2;var i=Qu(),a=Ku();au===e&&lu===t||(Eu=null,Wu(e,t)),t=!1;var o=hu;e:for(;;)try{if(0!==su&&null!==ou){var l=ou,s=uu;switch(su){case 8:Uu(),o=6;break e;case 3:case 2:case 9:case 6:null===ol.current&&(t=!0);var u=su;if(su=0,uu=null,tc(e,l,s,u),n&&du){o=0;break e}break;default:u=su,su=0,uu=null,tc(e,l,s,u)}}Xu(),o=hu;break}catch(c){qu(e,c)}return t&&e.shellSuspendCounter++,_i=yi=null,iu=r,D.H=i,D.A=a,null===ou&&(au=null,lu=0,Nr()),o}function Xu(){for(;null!==ou;)Ju(ou)}function Gu(){for(;null!==ou&&!te();)Ju(ou)}function Ju(e){var t=Jl(e.alternate,e,pu);e.memoizedProps=e.pendingProps,null===t?nc(e):ou=t}function ec(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Fl(n,t,t.pendingProps,t.type,void 0,lu);break;case 11:t=Fl(n,t,t.pendingProps,t.type.render,t.ref,lu);break;case 5:$a(t);default:ls(n,t),t=Jl(n,t=ou=Rr(t,pu),pu)}e.memoizedProps=e.pendingProps,null===t?nc(e):ou=t}function tc(e,t,n,i){_i=yi=null,$a(t),Xo=null,Go=0;var a=t.return;try{if(function(e,t,n,i,a){if(n.flags|=32768,null!==i&&"object"==typeof i&&"function"==typeof i.then){if(null!==(t=n.alternate)&&Ei(t,n,a,!0),null!==(n=ol.current)){switch(n.tag){case 13:return null===ll?Yu():null===n.alternate&&0===hu&&(hu=3),n.flags&=-257,n.flags|=65536,n.lanes=a,i===Zi?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([i]):t.add(i),pc(e,i,a)),!1;case 22:return n.flags|=65536,i===Zi?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([i])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([i]):n.add(i),pc(e,i,a)),!1}throw Error(r(435,n.tag))}return pc(e,i,a),Yu(),!1}if(li)return null!==(t=ol.current)?(!(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=a,i!==ci&&vi(Cr(e=Error(r(422),{cause:i}),n))):(i!==ci&&vi(Cr(t=Error(r(423),{cause:i}),n)),(e=e.current.alternate).flags|=65536,a&=-a,e.lanes|=a,i=Cr(i,n),ua(e,a=El(e.stateNode,i,a)),4!==hu&&(hu=2)),!1;var o=Error(r(520),{cause:i});if(o=Cr(o,n),null===_u?_u=[o]:_u.push(o),4!==hu&&(hu=2),null===t)return!0;i=Cr(i,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=a&-a,n.lanes|=e,ua(n,e=El(n.stateNode,i,e)),!1;case 1:if(t=n.type,o=n.stateNode,!(128&n.flags||"function"!=typeof t.getDerivedStateFromError&&(null===o||"function"!=typeof o.componentDidCatch||null!==Cu&&Cu.has(o))))return n.flags|=65536,a&=-a,n.lanes|=a,Tl(a=Cl(a),e,n,i),ua(n,a),!1}n=n.return}while(null!==n);return!1}(e,a,t,n,lu))return hu=1,xl(e,Cr(n,e.current)),void(ou=null)}catch(o){if(null!==a)throw ou=a,o;return hu=1,xl(e,Cr(n,e.current)),void(ou=null)}32768&t.flags?(li||1===i?e=!0:du||536870912&lu?e=!1:(cu=e=!0,(2===i||9===i||3===i||6===i)&&(null!==(i=ol.current)&&13===i.tag&&(i.flags|=16384))),rc(t,e)):nc(t)}function nc(e){var t=e;do{if(32768&t.flags)return void rc(t,cu);e=t.return;var n=as(t.alternate,t,pu);if(null!==n)return void(ou=n);if(null!==(t=t.sibling))return void(ou=t);ou=t=e}while(null!==t);0===hu&&(hu=5)}function rc(e,t){do{var n=os(e.alternate,e);if(null!==n)return n.flags&=32767,void(ou=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(ou=e);ou=e=n}while(null!==e);hu=6,ou=null}function ic(e,t,n,i,a,o,l,s,u){e.cancelPendingCommit=null;do{uc()}while(0!==Tu);if(6&iu)throw Error(r(327));if(null!==t){if(t===e.current)throw Error(r(177));if(o=t.lanes|t.childLanes,function(e,t,n,r,i,a){var o=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var l=e.entanglements,s=e.expirationTimes,u=e.hiddenUpdates;for(n=o&~n;0<n;){var c=31-me(n),d=1<<c;l[c]=0,s[c]=-1;var f=u[c];if(null!==f)for(u[c]=null,c=0;c<f.length;c++){var p=f[c];null!==p&&(p.lane&=-536870913)}n&=~d}0!==r&&Le(e,r,0),0!==a&&0===i&&0!==e.tag&&(e.suspendedLanes|=a&~(o&~t))}(e,n,o|=Ar,l,s,u),e===au&&(ou=au=null,lu=0),Au=t,Lu=e,Nu=n,Pu=o,Ou=a,ju=i,10256&t.subtreeFlags||10256&t.flags?(e.callbackNode=null,e.callbackPriority=0,J(le,(function(){return cc(),null}))):(e.callbackNode=null,e.callbackPriority=0),i=!!(13878&t.flags),13878&t.subtreeFlags||i){i=D.T,D.T=null,a=I.p,I.p=2,l=iu,iu|=4;try{!function(e,t){if(e=e.containerInfo,rd=af,rr(e=nr(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var i=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(i&&0!==i.rangeCount){n=i.anchorNode;var a=i.anchorOffset,o=i.focusNode;i=i.focusOffset;try{n.nodeType,o.nodeType}catch(g){n=null;break e}var l=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==a&&3!==f.nodeType||(s=l+a),f!==o||0!==i&&3!==f.nodeType||(u=l+i),3===f.nodeType&&(l+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===a&&(s=l),p===o&&++d===i&&(u=l),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(id={focusedElem:e,selectionRange:n},af=!1,Es=t;null!==Es;)if(e=(t=Es).child,1024&t.subtreeFlags&&null!==e)e.return=t,Es=e;else for(;null!==Es;){switch(o=(t=Es).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(1024&e&&null!==o){e=void 0,n=t,a=o.memoizedProps,o=o.memoizedState,i=n.stateNode;try{var m=bl(n.type,a,(n.elementType,n.type));e=i.getSnapshotBeforeUpdate(m,o),i.__reactInternalSnapshotBeforeUpdate=e}catch(v){fc(n,n.return,v)}}break;case 3:if(1024&e)if(9===(n=(e=t.stateNode.containerInfo).nodeType))vd(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":vd(e);break;default:e.textContent=""}break;default:if(1024&e)throw Error(r(163))}if(null!==(e=t.sibling)){e.return=t.return,Es=e;break}Es=t.return}}(e,t)}finally{iu=l,I.p=a,D.T=i}}Tu=1,ac(),oc(),lc()}}function ac(){if(1===Tu){Tu=0;var e=Lu,t=Au,n=!!(13878&t.flags);if(13878&t.subtreeFlags||n){n=D.T,D.T=null;var r=I.p;I.p=2;var i=iu;iu|=4;try{Ds(t,e);var a=id,o=nr(e.containerInfo),l=a.focusedElem,s=a.selectionRange;if(o!==l&&l&&l.ownerDocument&&tr(l.ownerDocument.documentElement,l)){if(null!==s&&rr(l)){var u=s.start,c=s.end;if(void 0===c&&(c=u),"selectionStart"in l)l.selectionStart=u,l.selectionEnd=Math.min(c,l.value.length);else{var d=l.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var p=f.getSelection(),h=l.textContent.length,m=Math.min(s.start,h),g=void 0===s.end?m:Math.min(s.end,h);!p.extend&&m>g&&(o=g,g=m,m=o);var v=er(l,m),b=er(l,g);if(v&&b&&(1!==p.rangeCount||p.anchorNode!==v.node||p.anchorOffset!==v.offset||p.focusNode!==b.node||p.focusOffset!==b.offset)){var y=d.createRange();y.setStart(v.node,v.offset),p.removeAllRanges(),m>g?(p.addRange(y),p.extend(b.node,b.offset)):(y.setEnd(b.node,b.offset),p.addRange(y))}}}}for(d=[],p=l;p=p.parentNode;)1===p.nodeType&&d.push({element:p,left:p.scrollLeft,top:p.scrollTop});for("function"==typeof l.focus&&l.focus(),l=0;l<d.length;l++){var _=d[l];_.element.scrollLeft=_.left,_.element.scrollTop=_.top}}af=!!rd,id=rd=null}finally{iu=i,I.p=r,D.T=n}}e.current=t,Tu=2}}function oc(){if(2===Tu){Tu=0;var e=Lu,t=Au,n=!!(8772&t.flags);if(8772&t.subtreeFlags||n){n=D.T,D.T=null;var r=I.p;I.p=2;var i=iu;iu|=4;try{Cs(e,t.alternate,t)}finally{iu=i,I.p=r,D.T=n}}Tu=3}}function lc(){if(4===Tu||3===Tu){Tu=0,ne();var e=Lu,t=Au,n=Nu,r=ju;10256&t.subtreeFlags||10256&t.flags?Tu=5:(Tu=0,Au=Lu=null,sc(e,e.pendingLanes));var i=e.pendingLanes;if(0===i&&(Cu=null),Pe(n),t=t.stateNode,pe&&"function"==typeof pe.onCommitFiberRoot)try{pe.onCommitFiberRoot(fe,t,void 0,!(128&~t.current.flags))}catch(s){}if(null!==r){t=D.T,i=I.p,I.p=2,D.T=null;try{for(var a=e.onRecoverableError,o=0;o<r.length;o++){var l=r[o];a(l.value,{componentStack:l.stack})}}finally{D.T=t,I.p=i}}3&Nu&&uc(),Sc(e),i=e.pendingLanes,4194090&n&&42&i?e===Mu?zu++:(zu=0,Mu=e):zu=0,Ec(0)}}function sc(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Di(t)))}function uc(e){return ac(),oc(),lc(),cc()}function cc(){if(5!==Tu)return!1;var e=Lu,t=Pu;Pu=0;var n=Pe(Nu),i=D.T,a=I.p;try{I.p=32>n?32:n,D.T=null,n=Ou,Ou=null;var o=Lu,l=Nu;if(Tu=0,Au=Lu=null,Nu=0,6&iu)throw Error(r(331));var s=iu;if(iu|=4,Js(o.current),Ws(o,o.current,l,n),iu=s,Ec(0,!1),pe&&"function"==typeof pe.onPostCommitFiberRoot)try{pe.onPostCommitFiberRoot(fe,o)}catch(u){}return!0}finally{I.p=a,D.T=i,sc(e,t)}}function dc(e,t,n){t=Cr(n,t),null!==(e=la(e,t=El(e.stateNode,t,2),2))&&(Te(e,2),Sc(e))}function fc(e,t,n){if(3===e.tag)dc(e,e,n);else for(;null!==t;){if(3===t.tag){dc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Cu||!Cu.has(r))){e=Cr(n,e),null!==(r=la(t,n=Cl(2),2))&&(Tl(n,r,t,e),Te(r,2),Sc(r));break}}t=t.return}}function pc(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new ru;var i=new Set;r.set(t,i)}else void 0===(i=r.get(t))&&(i=new Set,r.set(t,i));i.has(n)||(fu=!0,i.add(n),e=hc.bind(null,e,t,n),t.then(e,e))}function hc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,au===e&&(lu&n)===n&&(4===hu||3===hu&&(62914560&lu)===lu&&300>re()-xu?!(2&iu)&&Wu(e,0):vu|=n,yu===lu&&(yu=0)),Sc(e)}function mc(e,t){0===t&&(t=Ee()),null!==(e=jr(e,t))&&(Te(e,t),Sc(e))}function gc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),mc(e,n)}function vc(e,t){var n=0;switch(e.tag){case 13:var i=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:i=e.stateNode;break;case 22:i=e.stateNode._retryCache;break;default:throw Error(r(314))}null!==i&&i.delete(t),mc(e,n)}var bc=null,yc=null,_c=!1,wc=!1,kc=!1,xc=0;function Sc(e){e!==yc&&null===e.next&&(null===yc?bc=yc=e:yc=yc.next=e),wc=!0,_c||(_c=!0,pd((function(){6&iu?J(ae,Cc):Tc()})))}function Ec(e,t){if(!kc&&wc){kc=!0;do{for(var n=!1,r=bc;null!==r;){if(0!==e){var i=r.pendingLanes;if(0===i)var a=0;else{var o=r.suspendedLanes,l=r.pingedLanes;a=(1<<31-me(42|e)+1)-1,a=201326741&(a&=i&~(o&~l))?201326741&a|1:a?2|a:0}0!==a&&(n=!0,Nc(r,a))}else a=lu,!(3&(a=we(r,r===au?a:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||ke(r,a)||(n=!0,Nc(r,a));r=r.next}}while(n);kc=!1}}function Cc(){Tc()}function Tc(){wc=_c=!1;var e=0;0!==xc&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==ud&&(ud=e,!0);return ud=null,!1}()&&(e=xc),xc=0);for(var t=re(),n=null,r=bc;null!==r;){var i=r.next,a=Lc(r,t);0===a?(r.next=null,null===n?bc=i:n.next=i,null===i&&(yc=n)):(n=r,(0!==e||3&a)&&(wc=!0)),r=i}Ec(e)}function Lc(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,a=-62914561&e.pendingLanes;0<a;){var o=31-me(a),l=1<<o,s=i[o];-1===s?0!==(l&n)&&0===(l&r)||(i[o]=xe(l,t)):s<=t&&(e.expiredLanes|=l),a&=~l}if(n=lu,n=we(e,e===(t=au)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===su||9===su)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&ee(r),e.callbackNode=null,e.callbackPriority=0;if(!(3&n)||ke(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&ee(r),Pe(n)){case 2:case 8:n=oe;break;case 32:default:n=le;break;case 268435456:n=ue}return r=Ac.bind(null,e),n=J(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&ee(r),e.callbackPriority=2,e.callbackNode=null,2}function Ac(e,t){if(0!==Tu&&5!==Tu)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(uc()&&e.callbackNode!==n)return null;var r=lu;return 0===(r=we(e,e===au?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:($u(e,r,t),Lc(e,re()),null!=e.callbackNode&&e.callbackNode===n?Ac.bind(null,e):null)}function Nc(e,t){if(uc())return null;$u(e,t,!0)}function Pc(){return 0===xc&&(xc=Se()),xc}function Oc(e){return null==e||"symbol"==typeof e||"boolean"==typeof e?null:"function"==typeof e?e:Pt(""+e)}function jc(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var zc=0;zc<xr.length;zc++){var Mc=xr[zc];Sr(Mc.toLowerCase(),"on"+(Mc[0].toUpperCase()+Mc.slice(1)))}Sr(mr,"onAnimationEnd"),Sr(gr,"onAnimationIteration"),Sr(vr,"onAnimationStart"),Sr("dblclick","onDoubleClick"),Sr("focusin","onFocus"),Sr("focusout","onBlur"),Sr(br,"onTransitionRun"),Sr(yr,"onTransitionStart"),Sr(_r,"onTransitionCancel"),Sr(wr,"onTransitionEnd"),Xe("onMouseEnter",["mouseout","mouseover"]),Xe("onMouseLeave",["mouseout","mouseover"]),Xe("onPointerEnter",["pointerout","pointerover"]),Xe("onPointerLeave",["pointerout","pointerover"]),Ze("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ze("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ze("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ze("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ze("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ze("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Dc="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ic=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Dc));function Fc(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var o=r.length-1;0<=o;o--){var l=r[o],s=l.instance,u=l.currentTarget;if(l=l.listener,s!==a&&i.isPropagationStopped())break e;a=l,i.currentTarget=u;try{a(i)}catch(c){yl(c)}i.currentTarget=null,a=s}else for(o=0;o<r.length;o++){if(s=(l=r[o]).instance,u=l.currentTarget,l=l.listener,s!==a&&i.isPropagationStopped())break e;a=l,i.currentTarget=u;try{a(i)}catch(c){yl(c)}i.currentTarget=null,a=s}}}}function $c(e,t){var n=t[Ie];void 0===n&&(n=t[Ie]=new Set);var r=e+"__bubble";n.has(r)||(Bc(t,e,2,!1),n.add(r))}function Hc(e,t,n){var r=0;t&&(r|=4),Bc(n,e,r,t)}var Rc="_reactListening"+Math.random().toString(36).slice(2);function Vc(e){if(!e[Rc]){e[Rc]=!0,Ke.forEach((function(t){"selectionchange"!==t&&(Ic.has(t)||Hc(t,!1,e),Hc(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Rc]||(t[Rc]=!0,Hc("selectionchange",!1,t))}}function Bc(e,t,n,r){switch(ff(t)){case 2:var i=of;break;case 8:i=lf;break;default:i=sf}n=i.bind(null,t,n,e),i=void 0,!Rt||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(i=!0),r?void 0!==i?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):void 0!==i?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Uc(e,t,n,r,i){var a=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var s=r.stateNode.containerInfo;if(s===i)break;if(4===o)for(o=r.return;null!==o;){var u=o.tag;if((3===u||4===u)&&o.stateNode.containerInfo===i)return;o=o.return}for(;null!==s;){if(null===(o=Be(s)))return;if(5===(u=o.tag)||6===u||26===u||27===u){r=a=o;continue e}s=s.parentNode}}r=r.return}Ft((function(){var r=a,i=jt(n),o=[];e:{var s=kr.get(e);if(void 0!==s){var u=tn,c=e;switch(e){case"keypress":if(0===Qt(n))break e;case"keydown":case"keyup":u=vn;break;case"focusin":c="focus",u=sn;break;case"focusout":c="blur",u=sn;break;case"beforeblur":case"afterblur":u=sn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=on;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=ln;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=yn;break;case mr:case gr:case vr:u=un;break;case wr:u=_n;break;case"scroll":case"scrollend":u=rn;break;case"wheel":u=wn;break;case"copy":case"cut":case"paste":u=cn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=bn;break;case"toggle":case"beforetoggle":u=kn}var d=!!(4&t),f=!d&&("scroll"===e||"scrollend"===e),p=d?null!==s?s+"Capture":null:s;d=[];for(var h,m=r;null!==m;){var g=m;if(h=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===h||null===p||null!=(g=$t(m,p))&&d.push(Wc(m,g,h)),f)break;m=m.return}0<d.length&&(s=new u(s,c,null,n,i),o.push({event:s,listeners:d}))}}if(!(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===Ot||!(c=n.relatedTarget||n.fromElement)||!Be(c)&&!c[De])&&(u||s)&&(s=i.window===i?i:(s=i.ownerDocument)?s.defaultView||s.parentWindow:window,u?(u=r,null!==(c=(c=n.relatedTarget||n.toElement)?Be(c):null)&&(f=l(c),d=c.tag,c!==f||5!==d&&27!==d&&6!==d)&&(c=null)):(u=null,c=r),u!==c)){if(d=on,g="onMouseLeave",p="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(d=bn,g="onPointerLeave",p="onPointerEnter",m="pointer"),f=null==u?s:We(u),h=null==c?s:We(c),(s=new d(g,m+"leave",u,n,i)).target=f,s.relatedTarget=h,g=null,Be(i)===r&&((d=new d(p,m+"enter",c,n,i)).target=h,d.relatedTarget=f,g=d),f=g,u&&c)e:{for(p=c,m=0,h=d=u;h;h=Qc(h))m++;for(h=0,g=p;g;g=Qc(g))h++;for(;0<m-h;)d=Qc(d),m--;for(;0<h-m;)p=Qc(p),h--;for(;m--;){if(d===p||null!==p&&d===p.alternate)break e;d=Qc(d),p=Qc(p)}d=null}else d=null;null!==u&&Kc(o,s,u,d,!1),null!==c&&null!==f&&Kc(o,f,c,d,!0)}if("select"===(u=(s=r?We(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===u&&"file"===s.type)var v=Hn;else if(zn(s))if(Rn)v=Zn;else{v=Kn;var b=Qn}else!(u=s.nodeName)||"input"!==u.toLowerCase()||"checkbox"!==s.type&&"radio"!==s.type?r&&Lt(r.elementType)&&(v=Hn):v=Yn;switch(v&&(v=v(e,r))?Mn(o,v,n,i):(b&&b(e,s,r),"focusout"===e&&r&&"number"===s.type&&null!=r.memoizedProps.value&&_t(s,"number",s.value)),b=r?We(r):window,e){case"focusin":(zn(b)||"true"===b.contentEditable)&&(ar=b,or=r,lr=null);break;case"focusout":lr=or=ar=null;break;case"mousedown":sr=!0;break;case"contextmenu":case"mouseup":case"dragend":sr=!1,ur(o,n,i);break;case"selectionchange":if(ir)break;case"keydown":case"keyup":ur(o,n,i)}var y;if(Sn)e:{switch(e){case"compositionstart":var _="onCompositionStart";break e;case"compositionend":_="onCompositionEnd";break e;case"compositionupdate":_="onCompositionUpdate";break e}_=void 0}else On?Nn(e,n)&&(_="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(_="onCompositionStart");_&&(Tn&&"ko"!==n.locale&&(On||"onCompositionStart"!==_?"onCompositionEnd"===_&&On&&(y=qt()):(Ut="value"in(Bt=i)?Bt.value:Bt.textContent,On=!0)),0<(b=qc(r,_)).length&&(_=new dn(_,e,null,n,i),o.push({event:_,listeners:b}),y?_.data=y:null!==(y=Pn(n))&&(_.data=y))),(y=Cn?function(e,t){switch(e){case"compositionend":return Pn(t);case"keypress":return 32!==t.which?null:(An=!0,Ln);case"textInput":return(e=t.data)===Ln&&An?null:e;default:return null}}(e,n):function(e,t){if(On)return"compositionend"===e||!Sn&&Nn(e,t)?(e=qt(),Wt=Ut=Bt=null,On=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Tn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(_=qc(r,"onBeforeInput")).length&&(b=new dn("onBeforeInput","beforeinput",null,n,i),o.push({event:b,listeners:_}),b.data=y)),function(e,t,n,r,i){if("submit"===t&&n&&n.stateNode===i){var a=Oc((i[Me]||null).action),o=r.submitter;o&&null!==(t=(t=o[Me]||null)?Oc(t.formAction):o.getAttribute("formAction"))&&(a=t,o=null);var l=new tn("action","action",null,r,i);e.push({event:l,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==xc){var e=o?jc(i,o):new FormData(i);jo(n,{pending:!0,data:e,method:i.method,action:a},null,e)}}else"function"==typeof a&&(l.preventDefault(),e=o?jc(i,o):new FormData(i),jo(n,{pending:!0,data:e,method:i.method,action:a},a,e))},currentTarget:i}]})}}(o,e,r,n,i)}Fc(o,t)}))}function Wc(e,t,n){return{instance:e,listener:t,currentTarget:n}}function qc(e,t){for(var n=t+"Capture",r=[];null!==e;){var i=e,a=i.stateNode;if(5!==(i=i.tag)&&26!==i&&27!==i||null===a||(null!=(i=$t(e,n))&&r.unshift(Wc(e,i,a)),null!=(i=$t(e,t))&&r.push(Wc(e,i,a))),3===e.tag)return r;e=e.return}return[]}function Qc(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Kc(e,t,n,r,i){for(var a=t._reactName,o=[];null!==n&&n!==r;){var l=n,s=l.alternate,u=l.stateNode;if(l=l.tag,null!==s&&s===r)break;5!==l&&26!==l&&27!==l||null===u||(s=u,i?null!=(u=$t(n,a))&&o.unshift(Wc(n,u,s)):i||null!=(u=$t(n,a))&&o.push(Wc(n,u,s))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Yc=/\r\n?/g,Zc=/\u0000|\uFFFD/g;function Xc(e){return("string"==typeof e?e:""+e).replace(Yc,"\n").replace(Zc,"")}function Gc(e,t){return t=Xc(t),Xc(e)===t}function Jc(){}function ed(e,t,n,i,a,o){switch(n){case"children":"string"==typeof i?"body"===t||"textarea"===t&&""===i||St(e,i):("number"==typeof i||"bigint"==typeof i)&&"body"!==t&&St(e,""+i);break;case"className":it(e,"class",i);break;case"tabIndex":it(e,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":it(e,n,i);break;case"style":Tt(e,i,o);break;case"data":if("object"!==t){it(e,"data",i);break}case"src":case"href":if(""===i&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==i||"function"==typeof i||"symbol"==typeof i||"boolean"==typeof i){e.removeAttribute(n);break}i=Pt(""+i),e.setAttribute(n,i);break;case"action":case"formAction":if("function"==typeof i){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"==typeof o&&("formAction"===n?("input"!==t&&ed(e,t,"name",a.name,a,null),ed(e,t,"formEncType",a.formEncType,a,null),ed(e,t,"formMethod",a.formMethod,a,null),ed(e,t,"formTarget",a.formTarget,a,null)):(ed(e,t,"encType",a.encType,a,null),ed(e,t,"method",a.method,a,null),ed(e,t,"target",a.target,a,null))),null==i||"symbol"==typeof i||"boolean"==typeof i){e.removeAttribute(n);break}i=Pt(""+i),e.setAttribute(n,i);break;case"onClick":null!=i&&(e.onclick=Jc);break;case"onScroll":null!=i&&$c("scroll",e);break;case"onScrollEnd":null!=i&&$c("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=i){if("object"!=typeof i||!("__html"in i))throw Error(r(61));if(null!=(n=i.__html)){if(null!=a.children)throw Error(r(60));e.innerHTML=n}}break;case"multiple":e.multiple=i&&"function"!=typeof i&&"symbol"!=typeof i;break;case"muted":e.muted=i&&"function"!=typeof i&&"symbol"!=typeof i;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==i||"function"==typeof i||"boolean"==typeof i||"symbol"==typeof i){e.removeAttribute("xlink:href");break}n=Pt(""+i),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=i&&"function"!=typeof i&&"symbol"!=typeof i?e.setAttribute(n,""+i):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&"function"!=typeof i&&"symbol"!=typeof i?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===i?e.setAttribute(n,""):!1!==i&&null!=i&&"function"!=typeof i&&"symbol"!=typeof i?e.setAttribute(n,i):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=i&&"function"!=typeof i&&"symbol"!=typeof i&&!isNaN(i)&&1<=i?e.setAttribute(n,i):e.removeAttribute(n);break;case"rowSpan":case"start":null==i||"function"==typeof i||"symbol"==typeof i||isNaN(i)?e.removeAttribute(n):e.setAttribute(n,i);break;case"popover":$c("beforetoggle",e),$c("toggle",e),rt(e,"popover",i);break;case"xlinkActuate":at(e,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":at(e,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":at(e,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":at(e,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":at(e,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":at(e,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":at(e,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":at(e,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":at(e,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":rt(e,"is",i);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&rt(e,n=At.get(n)||n,i)}}function td(e,t,n,i,a,o){switch(n){case"style":Tt(e,i,o);break;case"dangerouslySetInnerHTML":if(null!=i){if("object"!=typeof i||!("__html"in i))throw Error(r(61));if(null!=(n=i.__html)){if(null!=a.children)throw Error(r(60));e.innerHTML=n}}break;case"children":"string"==typeof i?St(e,i):("number"==typeof i||"bigint"==typeof i)&&St(e,""+i);break;case"onScroll":null!=i&&$c("scroll",e);break;case"onScrollEnd":null!=i&&$c("scrollend",e);break;case"onClick":null!=i&&(e.onclick=Jc);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Ye.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(a=n.endsWith("Capture"),t=n.slice(2,a?n.length-7:void 0),"function"==typeof(o=null!=(o=e[Me]||null)?o[n]:null)&&e.removeEventListener(t,o,a),"function"!=typeof i)?n in e?e[n]=i:!0===i?e.setAttribute(n,""):rt(e,n,i):("function"!=typeof o&&null!==o&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,i,a)))}}function nd(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":$c("error",e),$c("load",e);var i,a=!1,o=!1;for(i in n)if(n.hasOwnProperty(i)){var l=n[i];if(null!=l)switch(i){case"src":a=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:ed(e,t,i,l,n,null)}}return o&&ed(e,t,"srcSet",n.srcSet,n,null),void(a&&ed(e,t,"src",n.src,n,null));case"input":$c("invalid",e);var s=i=l=o=null,u=null,c=null;for(a in n)if(n.hasOwnProperty(a)){var d=n[a];if(null!=d)switch(a){case"name":o=d;break;case"type":l=d;break;case"checked":u=d;break;case"defaultChecked":c=d;break;case"value":i=d;break;case"defaultValue":s=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(r(137,t));break;default:ed(e,t,a,d,n,null)}}return yt(e,i,s,u,c,l,o,!1),void pt(e);case"select":for(o in $c("invalid",e),a=l=i=null,n)if(n.hasOwnProperty(o)&&null!=(s=n[o]))switch(o){case"value":i=s;break;case"defaultValue":l=s;break;case"multiple":a=s;default:ed(e,t,o,s,n,null)}return t=i,n=l,e.multiple=!!a,void(null!=t?wt(e,!!a,t,!1):null!=n&&wt(e,!!a,n,!0));case"textarea":for(l in $c("invalid",e),i=o=a=null,n)if(n.hasOwnProperty(l)&&null!=(s=n[l]))switch(l){case"value":a=s;break;case"defaultValue":o=s;break;case"children":i=s;break;case"dangerouslySetInnerHTML":if(null!=s)throw Error(r(91));break;default:ed(e,t,l,s,n,null)}return xt(e,a,o,i),void pt(e);case"option":for(u in n)if(n.hasOwnProperty(u)&&null!=(a=n[u]))if("selected"===u)e.selected=a&&"function"!=typeof a&&"symbol"!=typeof a;else ed(e,t,u,a,n,null);return;case"dialog":$c("beforetoggle",e),$c("toggle",e),$c("cancel",e),$c("close",e);break;case"iframe":case"object":$c("load",e);break;case"video":case"audio":for(a=0;a<Dc.length;a++)$c(Dc[a],e);break;case"image":$c("error",e),$c("load",e);break;case"details":$c("toggle",e);break;case"embed":case"source":case"link":$c("error",e),$c("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&null!=(a=n[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:ed(e,t,c,a,n,null)}return;default:if(Lt(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(a=n[d])&&td(e,t,d,a,n,void 0));return}}for(s in n)n.hasOwnProperty(s)&&(null!=(a=n[s])&&ed(e,t,s,a,n,null))}var rd=null,id=null;function ad(e){return 9===e.nodeType?e:e.ownerDocument}function od(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function ld(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function sd(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"bigint"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ud=null;var cd="function"==typeof setTimeout?setTimeout:void 0,dd="function"==typeof clearTimeout?clearTimeout:void 0,fd="function"==typeof Promise?Promise:void 0,pd="function"==typeof queueMicrotask?queueMicrotask:void 0!==fd?function(e){return fd.resolve(null).then(e).catch(hd)}:cd;function hd(e){setTimeout((function(){throw e}))}function md(e){return"head"===e}function gd(e,t){var n=t,r=0,i=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0<r&&8>r){n=r;var o=e.ownerDocument;if(1&n&&xd(o.documentElement),2&n&&xd(o.body),4&n)for(xd(n=o.head),o=n.firstChild;o;){var l=o.nextSibling,s=o.nodeName;o[Re]||"SCRIPT"===s||"STYLE"===s||"LINK"===s&&"stylesheet"===o.rel.toLowerCase()||n.removeChild(o),o=l}}if(0===i)return e.removeChild(a),void Nf(t);i--}else"$"===n||"$?"===n||"$!"===n?i++:r=n.charCodeAt(0)-48;else r=0;n=a}while(n);Nf(t)}function vd(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":vd(n),Ve(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function bd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function yd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var _d=null;function wd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function kd(e,t,n){switch(t=ad(n),e){case"html":if(!(e=t.documentElement))throw Error(r(452));return e;case"head":if(!(e=t.head))throw Error(r(453));return e;case"body":if(!(e=t.body))throw Error(r(454));return e;default:throw Error(r(451))}}function xd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ve(e)}var Sd=new Map,Ed=new Set;function Cd(e){return"function"==typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Td=I.d;I.d={f:function(){var e=Td.f(),t=Bu();return e||t},r:function(e){var t=Ue(e);null!==t&&5===t.tag&&"form"===t.type?Mo(t):Td.r(e)},D:function(e){Td.D(e),Ad("dns-prefetch",e,null)},C:function(e,t){Td.C(e,t),Ad("preconnect",e,t)},L:function(e,t,n){Td.L(e,t,n);var r=Ld;if(r&&e&&t){var i='link[rel="preload"][as="'+vt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(i+='[imagesrcset="'+vt(n.imageSrcSet)+'"]',"string"==typeof n.imageSizes&&(i+='[imagesizes="'+vt(n.imageSizes)+'"]')):i+='[href="'+vt(e)+'"]';var a=i;switch(t){case"style":a=Pd(e);break;case"script":a=zd(e)}Sd.has(a)||(e=d({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),Sd.set(a,e),null!==r.querySelector(i)||"style"===t&&r.querySelector(Od(a))||"script"===t&&r.querySelector(Md(a))||(nd(t=r.createElement("link"),"link",e),Qe(t),r.head.appendChild(t)))}},m:function(e,t){Td.m(e,t);var n=Ld;if(n&&e){var r=t&&"string"==typeof t.as?t.as:"script",i='link[rel="modulepreload"][as="'+vt(r)+'"][href="'+vt(e)+'"]',a=i;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":a=zd(e)}if(!Sd.has(a)&&(e=d({rel:"modulepreload",href:e},t),Sd.set(a,e),null===n.querySelector(i))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Md(a)))return}nd(r=n.createElement("link"),"link",e),Qe(r),n.head.appendChild(r)}}},X:function(e,t){Td.X(e,t);var n=Ld;if(n&&e){var r=qe(n).hoistableScripts,i=zd(e),a=r.get(i);a||((a=n.querySelector(Md(i)))||(e=d({src:e,async:!0},t),(t=Sd.get(i))&&$d(e,t),Qe(a=n.createElement("script")),nd(a,"link",e),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},r.set(i,a))}},S:function(e,t,n){Td.S(e,t,n);var r=Ld;if(r&&e){var i=qe(r).hoistableStyles,a=Pd(e);t=t||"default";var o=i.get(a);if(!o){var l={loading:0,preload:null};if(o=r.querySelector(Od(a)))l.loading=5;else{e=d({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Sd.get(a))&&Fd(e,n);var s=o=r.createElement("link");Qe(s),nd(s,"link",e),s._p=new Promise((function(e,t){s.onload=e,s.onerror=t})),s.addEventListener("load",(function(){l.loading|=1})),s.addEventListener("error",(function(){l.loading|=2})),l.loading|=4,Id(o,t,r)}o={type:"stylesheet",instance:o,count:1,state:l},i.set(a,o)}}},M:function(e,t){Td.M(e,t);var n=Ld;if(n&&e){var r=qe(n).hoistableScripts,i=zd(e),a=r.get(i);a||((a=n.querySelector(Md(i)))||(e=d({src:e,async:!0,type:"module"},t),(t=Sd.get(i))&&$d(e,t),Qe(a=n.createElement("script")),nd(a,"link",e),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},r.set(i,a))}}};var Ld="undefined"==typeof document?null:document;function Ad(e,t,n){var r=Ld;if(r&&"string"==typeof t&&t){var i=vt(t);i='link[rel="'+e+'"][href="'+i+'"]',"string"==typeof n&&(i+='[crossorigin="'+n+'"]'),Ed.has(i)||(Ed.add(i),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(i)&&(nd(t=r.createElement("link"),"link",e),Qe(t),r.head.appendChild(t)))}}function Nd(e,t,n,i){var a,o,l,s,u=(u=q.current)?Cd(u):null;if(!u)throw Error(r(446));switch(e){case"meta":case"title":return null;case"style":return"string"==typeof n.precedence&&"string"==typeof n.href?(t=Pd(n.href),(i=(n=qe(u).hoistableStyles).get(t))||(i={type:"style",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"==typeof n.href&&"string"==typeof n.precedence){e=Pd(n.href);var c=qe(u).hoistableStyles,d=c.get(e);if(d||(u=u.ownerDocument||u,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=u.querySelector(Od(e)))&&!c._p&&(d.instance=c,d.state.loading=5),Sd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Sd.set(e,n),c||(a=u,o=e,l=n,s=d.state,a.querySelector('link[rel="preload"][as="style"]['+o+"]")?s.loading=1:(o=a.createElement("link"),s.preload=o,o.addEventListener("load",(function(){return s.loading|=1})),o.addEventListener("error",(function(){return s.loading|=2})),nd(o,"link",l),Qe(o),a.head.appendChild(o))))),t&&null===i)throw Error(r(528,""));return d}if(t&&null!==i)throw Error(r(529,""));return null;case"script":return t=n.async,"string"==typeof(n=n.src)&&t&&"function"!=typeof t&&"symbol"!=typeof t?(t=zd(n),(i=(n=qe(u).hoistableScripts).get(t))||(i={type:"script",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,e))}}function Pd(e){return'href="'+vt(e)+'"'}function Od(e){return'link[rel="stylesheet"]['+e+"]"}function jd(e){return d({},e,{"data-precedence":e.precedence,precedence:null})}function zd(e){return'[src="'+vt(e)+'"]'}function Md(e){return"script[async]"+e}function Dd(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var i=e.querySelector('style[data-href~="'+vt(n.href)+'"]');if(i)return t.instance=i,Qe(i),i;var a=d({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return Qe(i=(e.ownerDocument||e).createElement("style")),nd(i,"style",a),Id(i,n.precedence,e),t.instance=i;case"stylesheet":a=Pd(n.href);var o=e.querySelector(Od(a));if(o)return t.state.loading|=4,t.instance=o,Qe(o),o;i=jd(n),(a=Sd.get(a))&&Fd(i,a),Qe(o=(e.ownerDocument||e).createElement("link"));var l=o;return l._p=new Promise((function(e,t){l.onload=e,l.onerror=t})),nd(o,"link",i),t.state.loading|=4,Id(o,n.precedence,e),t.instance=o;case"script":return o=zd(n.src),(a=e.querySelector(Md(o)))?(t.instance=a,Qe(a),a):(i=n,(a=Sd.get(o))&&$d(i=d({},n),a),Qe(a=(e=e.ownerDocument||e).createElement("script")),nd(a,"link",i),e.head.appendChild(a),t.instance=a);case"void":return null;default:throw Error(r(443,t.type))}else"stylesheet"===t.type&&!(4&t.state.loading)&&(i=t.instance,t.state.loading|=4,Id(i,n.precedence,e));return t.instance}function Id(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=r.length?r[r.length-1]:null,a=i,o=0;o<r.length;o++){var l=r[o];if(l.dataset.precedence===t)a=l;else if(a!==i)break}a?a.parentNode.insertBefore(e,a.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Fd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function $d(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Hd=null;function Rd(e,t,n){if(null===Hd){var r=new Map,i=Hd=new Map;i.set(n,r)}else(r=(i=Hd).get(n))||(r=new Map,i.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),i=0;i<n.length;i++){var a=n[i];if(!(a[Re]||a[ze]||"link"===e&&"stylesheet"===a.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==a.namespaceURI){var o=a.getAttribute(t)||"";o=e+o;var l=r.get(o);l?l.push(a):r.set(o,[a])}}return r}function Vd(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Bd(e){return!!("stylesheet"!==e.type||3&e.state.loading)}var Ud=null;function Wd(){}function qd(){if(this.count--,0===this.count)if(this.stylesheets)Kd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Qd=null;function Kd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Qd=new Map,t.forEach(Yd,e),Qd=null,qd.call(e))}function Yd(e,t){if(!(4&t.state.loading)){var n=Qd.get(e);if(n)var r=n.get(null);else{n=new Map,Qd.set(e,n);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),a=0;a<i.length;a++){var o=i[a];"LINK"!==o.nodeName&&"not all"===o.getAttribute("media")||(n.set(o.dataset.precedence,o),r=o)}r&&n.set(null,r)}o=(i=t.instance).getAttribute("data-precedence"),(a=n.get(o)||r)===r&&n.set(null,i),n.set(o,i),this.count++,r=qd.bind(this),i.addEventListener("load",r),i.addEventListener("error",r),a?a.parentNode.insertBefore(i,a.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(i,e.firstChild),t.state.loading|=4}}var Zd={$$typeof:_,Provider:null,Consumer:null,_currentValue:F,_currentValue2:F,_threadCount:0};function Xd(e,t,n,r,i,a,o,l){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ce(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ce(0),this.hiddenUpdates=Ce(null),this.identifierPrefix=r,this.onUncaughtError=i,this.onCaughtError=a,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=l,this.incompleteTransitions=new Map}function Gd(e,t,n,r,i,a,o,l,s,u,c,d){return e=new Xd(e,t,n,o,l,s,u,d),t=1,!0===a&&(t|=24),a=Fr(3,null,null,t),e.current=a,a.stateNode=e,(t=Mi()).refCount++,e.pooledCache=t,t.refCount++,a.memoizedState={element:r,isDehydrated:n,cache:t},ia(a),e}function Jd(e){return e?e=Dr:Dr}function ef(e,t,n,r,i,a){i=Jd(i),null===r.context?r.context=i:r.pendingContext=i,(r=oa(t)).payload={element:n},null!==(a=void 0===a?null:a)&&(r.callback=a),null!==(n=la(e,r,t))&&(Fu(n,0,t),sa(n,e,t))}function tf(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function nf(e,t){tf(e,t),(e=e.alternate)&&tf(e,t)}function rf(e){if(13===e.tag){var t=jr(e,67108864);null!==t&&Fu(t,0,67108864),nf(e,67108864)}}var af=!0;function of(e,t,n,r){var i=D.T;D.T=null;var a=I.p;try{I.p=2,sf(e,t,n,r)}finally{I.p=a,D.T=i}}function lf(e,t,n,r){var i=D.T;D.T=null;var a=I.p;try{I.p=8,sf(e,t,n,r)}finally{I.p=a,D.T=i}}function sf(e,t,n,r){if(af){var i=uf(r);if(null===i)Uc(e,t,r,cf,n),wf(e,r);else if(function(e,t,n,r,i){switch(t){case"focusin":return hf=kf(hf,e,t,n,r,i),!0;case"dragenter":return mf=kf(mf,e,t,n,r,i),!0;case"mouseover":return gf=kf(gf,e,t,n,r,i),!0;case"pointerover":var a=i.pointerId;return vf.set(a,kf(vf.get(a)||null,e,t,n,r,i)),!0;case"gotpointercapture":return a=i.pointerId,bf.set(a,kf(bf.get(a)||null,e,t,n,r,i)),!0}return!1}(i,e,t,n,r))r.stopPropagation();else if(wf(e,r),4&t&&-1<_f.indexOf(e)){for(;null!==i;){var a=Ue(i);if(null!==a)switch(a.tag){case 3:if((a=a.stateNode).current.memoizedState.isDehydrated){var o=_e(a.pendingLanes);if(0!==o){var l=a;for(l.pendingLanes|=2,l.entangledLanes|=2;o;){var s=1<<31-me(o);l.entanglements[1]|=s,o&=~s}Sc(a),!(6&iu)&&(Su=re()+500,Ec(0))}}break;case 13:null!==(l=jr(a,2))&&Fu(l,0,2),Bu(),nf(a,2)}if(null===(a=uf(r))&&Uc(e,t,r,cf,n),a===i)break;i=a}null!==i&&r.stopPropagation()}else Uc(e,t,r,null,n)}}function uf(e){return df(e=jt(e))}var cf=null;function df(e){if(cf=null,null!==(e=Be(e))){var t=l(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=s(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return cf=e,null}function ff(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ie()){case ae:return 2;case oe:return 8;case le:case se:return 32;case ue:return 268435456;default:return 32}default:return 32}}var pf=!1,hf=null,mf=null,gf=null,vf=new Map,bf=new Map,yf=[],_f="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function wf(e,t){switch(e){case"focusin":case"focusout":hf=null;break;case"dragenter":case"dragleave":mf=null;break;case"mouseover":case"mouseout":gf=null;break;case"pointerover":case"pointerout":vf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":bf.delete(t.pointerId)}}function kf(e,t,n,r,i,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[i]},null!==t&&(null!==(t=Ue(t))&&rf(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==i&&-1===t.indexOf(i)&&t.push(i),e)}function xf(e){var t=Be(e.target);if(null!==t){var n=l(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=s(n)))return e.blockedOn=t,void function(e,t){var n=I.p;try{return I.p=e,t()}finally{I.p=n}}(e.priority,(function(){if(13===n.tag){var e=Du();e=Ne(e);var t=jr(n,e);null!==t&&Fu(t,0,e),nf(n,e)}}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Sf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=uf(e.nativeEvent);if(null!==n)return null!==(t=Ue(n))&&rf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Ot=r,n.target.dispatchEvent(r),Ot=null,t.shift()}return!0}function Ef(e,t,n){Sf(e)&&n.delete(t)}function Cf(){pf=!1,null!==hf&&Sf(hf)&&(hf=null),null!==mf&&Sf(mf)&&(mf=null),null!==gf&&Sf(gf)&&(gf=null),vf.forEach(Ef),bf.forEach(Ef)}function Tf(t,n){t.blockedOn===n&&(t.blockedOn=null,pf||(pf=!0,e.unstable_scheduleCallback(e.unstable_NormalPriority,Cf)))}var Lf=null;function Af(t){Lf!==t&&(Lf=t,e.unstable_scheduleCallback(e.unstable_NormalPriority,(function(){Lf===t&&(Lf=null);for(var e=0;e<t.length;e+=3){var n=t[e],r=t[e+1],i=t[e+2];if("function"!=typeof r){if(null===df(r||n))continue;break}var a=Ue(n);null!==a&&(t.splice(e,3),e-=3,jo(a,{pending:!0,data:i,method:n.method,action:r},r,i))}})))}function Nf(e){function t(t){return Tf(t,e)}null!==hf&&Tf(hf,e),null!==mf&&Tf(mf,e),null!==gf&&Tf(gf,e),vf.forEach(t),bf.forEach(t);for(var n=0;n<yf.length;n++){var r=yf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<yf.length&&null===(n=yf[0]).blockedOn;)xf(n),null===n.blockedOn&&yf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var i=n[r],a=n[r+1],o=i[Me]||null;if("function"==typeof a)o||Af(n);else if(o){var l=null;if(a&&a.hasAttribute("formAction")){if(i=a,o=a[Me]||null)l=o.formAction;else if(null!==df(i))continue}else l=o.action;"function"==typeof l?n[r+1]=l:(n.splice(r,3),r-=3),Af(n)}}}function Pf(e){this._internalRoot=e}function Of(e){this._internalRoot=e}Of.prototype.render=Pf.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(r(409));ef(t.current,Du(),e,t,null,null)},Of.prototype.unmount=Pf.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;ef(e.current,2,null,e,null,null),Bu(),t[De]=null}},Of.prototype.unstable_scheduleHydration=function(e){if(e){var t=Oe();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yf.length&&0!==t&&t<yf[n].priority;n++);yf.splice(n,0,e),0===n&&xf(e)}};var jf=t.version;if("19.1.0"!==jf)throw Error(r(527,jf,"19.1.0"));I.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(r(188));throw e=Object.keys(e).join(","),Error(r(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=l(e)))throw Error(r(188));return t!==e?null:e}for(var n=e,i=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(i=a.return)){n=i;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return u(a),e;if(o===i)return u(a),t;o=o.sibling}throw Error(r(188))}if(n.return!==i.return)n=a,i=o;else{for(var s=!1,c=a.child;c;){if(c===n){s=!0,n=a,i=o;break}if(c===i){s=!0,i=a,n=o;break}c=c.sibling}if(!s){for(c=o.child;c;){if(c===n){s=!0,n=o,i=a;break}if(c===i){s=!0,i=o,n=a;break}c=c.sibling}if(!s)throw Error(r(189))}}if(n.alternate!==i)throw Error(r(190))}if(3!==n.tag)throw Error(r(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?c(e):null)?null:e.stateNode};var zf={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:D,reconcilerVersion:"19.1.0"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Mf=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Mf.isDisabled&&Mf.supportsFiber)try{fe=Mf.inject(zf),pe=Mf}catch(If){}}return C.createRoot=function(e,t){if(!o(e))throw Error(r(299));var n=!1,i="",a=_l,l=wl,s=kl;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(i=t.identifierPrefix),void 0!==t.onUncaughtError&&(a=t.onUncaughtError),void 0!==t.onCaughtError&&(l=t.onCaughtError),void 0!==t.onRecoverableError&&(s=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Gd(e,1,!1,null,0,n,i,a,l,s,0,null),e[De]=t.current,Vc(e),new Pf(t)},C.hydrateRoot=function(e,t,n){if(!o(e))throw Error(r(299));var i=!1,a="",l=_l,s=wl,u=kl,c=null;return null!=n&&(!0===n.unstable_strictMode&&(i=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onUncaughtError&&(l=n.onUncaughtError),void 0!==n.onCaughtError&&(s=n.onCaughtError),void 0!==n.onRecoverableError&&(u=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(c=n.formState)),(t=Gd(e,1,!0,t,0,i,a,l,s,u,0,c)).context=Jd(null),n=t.current,(a=oa(i=Ne(i=Du()))).callback=null,la(n,a,i),n=i,t.current.lanes=n,Te(t,n),Sc(t),e[De]=t.current,Vc(e),new Of(t)},C.version="19.1.0",C}const P=o((x||(x=1,function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){}}(),E.exports=N()),E.exports)),O={},j=function(e,t,n){let r=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map((e=>Promise.resolve(e).then((e=>({status:"fulfilled",value:e})),(e=>({status:"rejected",reason:e}))))))};document.getElementsByTagName("link");const n=document.querySelector("meta[property=csp-nonce]"),i=(null==n?void 0:n.nonce)||(null==n?void 0:n.getAttribute("nonce"));r=e(t.map((e=>{if((e=function(e){return"/"+e}(e))in O)return;O[e]=!0;const t=e.endsWith(".css"),n=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${n}`))return;const r=document.createElement("link");return r.rel=t?"stylesheet":"modulepreload",t||(r.as="script"),r.crossOrigin="",r.href=e,i&&r.setAttribute("nonce",i),document.head.appendChild(r),t?new Promise(((t,n)=>{r.addEventListener("load",t),r.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function i(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return r.then((t=>{for(const e of t||[])"rejected"===e.status&&i(e.reason);return e().catch(i)}))};var z,M;const D=o(function(){if(M)return z;M=1;var e="undefined"!=typeof Element,t="function"==typeof Map,n="function"==typeof Set,r="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;function i(a,o){if(a===o)return!0;if(a&&o&&"object"==typeof a&&"object"==typeof o){if(a.constructor!==o.constructor)return!1;var l,s,u,c;if(Array.isArray(a)){if((l=a.length)!=o.length)return!1;for(s=l;0!==s--;)if(!i(a[s],o[s]))return!1;return!0}if(t&&a instanceof Map&&o instanceof Map){if(a.size!==o.size)return!1;for(c=a.entries();!(s=c.next()).done;)if(!o.has(s.value[0]))return!1;for(c=a.entries();!(s=c.next()).done;)if(!i(s.value[1],o.get(s.value[0])))return!1;return!0}if(n&&a instanceof Set&&o instanceof Set){if(a.size!==o.size)return!1;for(c=a.entries();!(s=c.next()).done;)if(!o.has(s.value[0]))return!1;return!0}if(r&&ArrayBuffer.isView(a)&&ArrayBuffer.isView(o)){if((l=a.length)!=o.length)return!1;for(s=l;0!==s--;)if(a[s]!==o[s])return!1;return!0}if(a.constructor===RegExp)return a.source===o.source&&a.flags===o.flags;if(a.valueOf!==Object.prototype.valueOf&&"function"==typeof a.valueOf&&"function"==typeof o.valueOf)return a.valueOf()===o.valueOf();if(a.toString!==Object.prototype.toString&&"function"==typeof a.toString&&"function"==typeof o.toString)return a.toString()===o.toString();if((l=(u=Object.keys(a)).length)!==Object.keys(o).length)return!1;for(s=l;0!==s--;)if(!Object.prototype.hasOwnProperty.call(o,u[s]))return!1;if(e&&a instanceof Element)return!1;for(s=l;0!==s--;)if(("_owner"!==u[s]&&"__v"!==u[s]&&"__o"!==u[s]||!a.$$typeof)&&!i(a[u[s]],o[u[s]]))return!1;return!0}return a!=a&&o!=o}return z=function(e,t){try{return i(e,t)}catch(n){if((n.message||"").match(/stack|recursion/i))return!1;throw n}}}());var I,F;const $=o(F?I:(F=1,I=function(e,t,n,r,i,a,o,l){if(!e){var s;if(void 0===t)s=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var u=[n,r,i,a,o,l],c=0;(s=new Error(t.replace(/%s/g,(function(){return u[c++]})))).name="Invariant Violation"}throw s.framesToPop=1,s}}));var H,R;const V=o(R?H:(R=1,H=function(e,t,n,r){var i=n?n.call(r,e,t):void 0;if(void 0!==i)return!!i;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;var a=Object.keys(e),o=Object.keys(t);if(a.length!==o.length)return!1;for(var l=Object.prototype.hasOwnProperty.bind(t),s=0;s<a.length;s++){var u=a[s];if(!l(u))return!1;var c=e[u],d=t[u];if(!1===(i=n?n.call(r,c,d,u):void 0)||void 0===i&&c!==d)return!1}return!0}));var B=(e=>(e.BASE="base",e.BODY="body",e.HEAD="head",e.HTML="html",e.LINK="link",e.META="meta",e.NOSCRIPT="noscript",e.SCRIPT="script",e.STYLE="style",e.TITLE="title",e.FRAGMENT="Symbol(react.fragment)",e))(B||{}),U={rel:["amphtml","canonical","alternate"]},W={type:["application/ld+json"]},q={charset:"",name:["generator","robots","description"],property:["og:type","og:title","og:url","og:image","og:image:alt","og:description","twitter:url","twitter:title","twitter:description","twitter:image","twitter:image:alt","twitter:card","twitter:site"]},Q=Object.values(B),K={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},Y=Object.entries(K).reduce(((e,[t,n])=>(e[n]=t,e)),{}),Z="data-rh",X="defaultTitle",G="defer",J="encodeSpecialCharacters",ee="onChangeClientState",te="titleTemplate",ne="prioritizeSeoTags",re=(e,t)=>{for(let n=e.length-1;n>=0;n-=1){const r=e[n];if(Object.prototype.hasOwnProperty.call(r,t))return r[t]}return null},ie=e=>{let t=re(e,"title");const n=re(e,te);if(Array.isArray(t)&&(t=t.join("")),n&&t)return n.replace(/%s/g,(()=>t));const r=re(e,X);return t||r||void 0},ae=e=>re(e,ee)||(()=>{}),oe=(e,t)=>t.filter((t=>void 0!==t[e])).map((t=>t[e])).reduce(((e,t)=>({...e,...t})),{}),le=(e,t)=>t.filter((e=>void 0!==e.base)).map((e=>e.base)).reverse().reduce(((t,n)=>{if(!t.length){const r=Object.keys(n);for(let i=0;i<r.length;i+=1){const a=r[i].toLowerCase();if(-1!==e.indexOf(a)&&n[a])return t.concat(n)}}return t}),[]),se=(e,t,n)=>{const r={};return n.filter((t=>!!Array.isArray(t[e])||(void 0!==t[e]&&(t[e],console&&console.warn),!1))).map((t=>t[e])).reverse().reduce(((e,n)=>{const i={};n.filter((e=>{let n;const a=Object.keys(e);for(let r=0;r<a.length;r+=1){const i=a[r],o=i.toLowerCase();-1===t.indexOf(o)||"rel"===n&&"canonical"===e[n].toLowerCase()||"rel"===o&&"stylesheet"===e[o].toLowerCase()||(n=o),-1===t.indexOf(i)||"innerHTML"!==i&&"cssText"!==i&&"itemprop"!==i||(n=i)}if(!n||!e[n])return!1;const o=e[n].toLowerCase();return r[n]||(r[n]={}),i[n]||(i[n]={}),!r[n][o]&&(i[n][o]=!0,!0)})).reverse().forEach((t=>e.push(t)));const a=Object.keys(i);for(let t=0;t<a.length;t+=1){const e=a[t],n={...r[e],...i[e]};r[e]=n}return e}),[]).reverse()},ue=(e,t)=>{if(Array.isArray(e)&&e.length)for(let n=0;n<e.length;n+=1){if(e[n][t])return!0}return!1},ce=e=>Array.isArray(e)?e.join(""):e,de=(e,t)=>Array.isArray(e)?e.reduce(((e,n)=>(((e,t)=>{const n=Object.keys(e);for(let r=0;r<n.length;r+=1)if(t[n[r]]&&t[n[r]].includes(e[n[r]]))return!0;return!1})(n,t)?e.priority.push(n):e.default.push(n),e)),{priority:[],default:[]}):{default:e,priority:[]},fe=(e,t)=>({...e,[t]:void 0}),pe=["noscript","script","style"],he=(e,t=!0)=>!1===t?String(e):String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;"),me=e=>Object.keys(e).reduce(((t,n)=>{const r=void 0!==e[n]?`${n}="${e[n]}"`:`${n}`;return t?`${t} ${r}`:r}),""),ge=(e,t={})=>Object.keys(e).reduce(((t,n)=>(t[K[n]||n]=e[n],t)),t),ve=(e,t)=>t.map(((t,n)=>{const r={key:n,[Z]:!0};return Object.keys(t).forEach((e=>{const n=K[e]||e;if("innerHTML"===n||"cssText"===n){const e=t.innerHTML||t.cssText;r.dangerouslySetInnerHTML={__html:e}}else r[n]=t[e]})),s.createElement(e,r)})),be=(e,t,n=!0)=>{switch(e){case"title":return{toComponent:()=>((e,t,n)=>{const r=ge(n,{key:t,[Z]:!0});return[s.createElement("title",r,t)]})(0,t.title,t.titleAttributes),toString:()=>((e,t,n,r)=>{const i=me(n),a=ce(t);return i?`<${e} ${Z}="true" ${i}>${he(a,r)}</${e}>`:`<${e} ${Z}="true">${he(a,r)}</${e}>`})(e,t.title,t.titleAttributes,n)};case"bodyAttributes":case"htmlAttributes":return{toComponent:()=>ge(t),toString:()=>me(t)};default:return{toComponent:()=>ve(e,t),toString:()=>((e,t,n=!0)=>t.reduce(((t,r)=>{const i=r,a=Object.keys(i).filter((e=>!("innerHTML"===e||"cssText"===e))).reduce(((e,t)=>{const r=void 0===i[t]?t:`${t}="${he(i[t],n)}"`;return e?`${e} ${r}`:r}),""),o=i.innerHTML||i.cssText||"",l=-1===pe.indexOf(e);return`${t}<${e} ${Z}="true" ${a}${l?"/>":`>${o}</${e}>`}`}),""))(e,t,n)}}},ye=e=>{const{baseTag:t,bodyAttributes:n,encode:r=!0,htmlAttributes:i,noscriptTags:a,styleTags:o,title:l="",titleAttributes:s,prioritizeSeoTags:u}=e;let{linkTags:c,metaTags:d,scriptTags:f}=e,p={toComponent:()=>{},toString:()=>""};return u&&({priorityMethods:p,linkTags:c,metaTags:d,scriptTags:f}=(({metaTags:e,linkTags:t,scriptTags:n,encode:r})=>{const i=de(e,q),a=de(t,U),o=de(n,W);return{priorityMethods:{toComponent:()=>[...ve("meta",i.priority),...ve("link",a.priority),...ve("script",o.priority)],toString:()=>`${be("meta",i.priority,r)} ${be("link",a.priority,r)} ${be("script",o.priority,r)}`},metaTags:i.default,linkTags:a.default,scriptTags:o.default}})(e)),{priority:p,base:be("base",t,r),bodyAttributes:be("bodyAttributes",n,r),htmlAttributes:be("htmlAttributes",i,r),link:be("link",c,r),meta:be("meta",d,r),noscript:be("noscript",a,r),script:be("script",f,r),style:be("style",o,r),title:be("title",{title:l,titleAttributes:s},r)}},_e=[],we=!("undefined"==typeof window||!window.document||!window.document.createElement),ke=class{constructor(e,t){r(this,"instances",[]),r(this,"canUseDOM",we),r(this,"context"),r(this,"value",{setHelmet:e=>{this.context.helmet=e},helmetInstances:{get:()=>this.canUseDOM?_e:this.instances,add:e=>{(this.canUseDOM?_e:this.instances).push(e)},remove:e=>{const t=(this.canUseDOM?_e:this.instances).indexOf(e);(this.canUseDOM?_e:this.instances).splice(t,1)}}}),this.context=e,this.canUseDOM=t||!1,t||(e.helmet=ye({baseTag:[],bodyAttributes:{},htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}}))}},xe=s.createContext({}),Se=(e=class extends l.Component{constructor(t){super(t),r(this,"helmetData"),this.helmetData=new ke(this.props.context||{},e.canUseDOM)}render(){return s.createElement(xe.Provider,{value:this.helmetData.value},this.props.children)}},r(e,"canUseDOM",we),e),Ee=(e,t)=>{const n=document.head||document.querySelector("head"),r=n.querySelectorAll(`${e}[${Z}]`),i=[].slice.call(r),a=[];let o;return t&&t.length&&t.forEach((t=>{const n=document.createElement(e);for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e))if("innerHTML"===e)n.innerHTML=t.innerHTML;else if("cssText"===e)n.styleSheet?n.styleSheet.cssText=t.cssText:n.appendChild(document.createTextNode(t.cssText));else{const r=e,i=void 0===t[r]?"":t[r];n.setAttribute(e,i)}n.setAttribute(Z,"true"),i.some(((e,t)=>(o=t,n.isEqualNode(e))))?i.splice(o,1):a.push(n)})),i.forEach((e=>{var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)})),a.forEach((e=>n.appendChild(e))),{oldTags:i,newTags:a}},Ce=(e,t)=>{const n=document.getElementsByTagName(e)[0];if(!n)return;const r=n.getAttribute(Z),i=r?r.split(","):[],a=[...i],o=Object.keys(t);for(const l of o){const e=t[l]||"";n.getAttribute(l)!==e&&n.setAttribute(l,e),-1===i.indexOf(l)&&i.push(l);const r=a.indexOf(l);-1!==r&&a.splice(r,1)}for(let l=a.length-1;l>=0;l-=1)n.removeAttribute(a[l]);i.length===a.length?n.removeAttribute(Z):n.getAttribute(Z)!==o.join(",")&&n.setAttribute(Z,o.join(","))},Te=(e,t)=>{const{baseTag:n,bodyAttributes:r,htmlAttributes:i,linkTags:a,metaTags:o,noscriptTags:l,onChangeClientState:s,scriptTags:u,styleTags:c,title:d,titleAttributes:f}=e;Ce("body",r),Ce("html",i),((e,t)=>{void 0!==e&&document.title!==e&&(document.title=ce(e)),Ce("title",t)})(d,f);const p={baseTag:Ee("base",n),linkTags:Ee("link",a),metaTags:Ee("meta",o),noscriptTags:Ee("noscript",l),scriptTags:Ee("script",u),styleTags:Ee("style",c)},h={},m={};Object.keys(p).forEach((e=>{const{newTags:t,oldTags:n}=p[e];t.length&&(h[e]=t),n.length&&(m[e]=p[e].oldTags)})),t&&t(),s(e,h,m)},Le=null,Ae=e=>{Le&&cancelAnimationFrame(Le),e.defer?Le=requestAnimationFrame((()=>{Te(e,(()=>{Le=null}))})):(Te(e),Le=null)},Ne=class extends l.Component{constructor(){super(...arguments),r(this,"rendered",!1)}shouldComponentUpdate(e){return!V(e,this.props)}componentDidUpdate(){this.emitChange()}componentWillUnmount(){const{helmetInstances:e}=this.props.context;e.remove(this),this.emitChange()}emitChange(){const{helmetInstances:e,setHelmet:t}=this.props.context;let n=null;const r=(i=e.get().map((e=>{const t={...e.props};return delete t.context,t})),{baseTag:le(["href"],i),bodyAttributes:oe("bodyAttributes",i),defer:re(i,G),encode:re(i,J),htmlAttributes:oe("htmlAttributes",i),linkTags:se("link",["rel","href"],i),metaTags:se("meta",["name","charset","http-equiv","property","itemprop"],i),noscriptTags:se("noscript",["innerHTML"],i),onChangeClientState:ae(i),scriptTags:se("script",["src","innerHTML"],i),styleTags:se("style",["cssText"],i),title:ie(i),titleAttributes:oe("titleAttributes",i),prioritizeSeoTags:ue(i,ne)});var i;Se.canUseDOM?Ae(r):ye&&(n=ye(r)),t(n)}init(){if(this.rendered)return;this.rendered=!0;const{helmetInstances:e}=this.props.context;e.add(this),this.emitChange()}render(){return this.init(),null}},Pe=(t=class extends l.Component{shouldComponentUpdate(e){return!D(fe(this.props,"helmetData"),fe(e,"helmetData"))}mapNestedChildrenToProps(e,t){if(!t)return null;switch(e.type){case"script":case"noscript":return{innerHTML:t};case"style":return{cssText:t};default:throw new Error(`<${e.type} /> elements are self-closing and can not contain children. Refer to our API for more information.`)}}flattenArrayTypeChildren(e,t,n,r){return{...t,[e.type]:[...t[e.type]||[],{...n,...this.mapNestedChildrenToProps(e,r)}]}}mapObjectTypeChildren(e,t,n,r){switch(e.type){case"title":return{...t,[e.type]:r,titleAttributes:{...n}};case"body":return{...t,bodyAttributes:{...n}};case"html":return{...t,htmlAttributes:{...n}};default:return{...t,[e.type]:{...n}}}}mapArrayTypeChildrenToProps(e,t){let n={...t};return Object.keys(e).forEach((t=>{n={...n,[t]:e[t]}})),n}warnOnInvalidChildren(e,t){return $(Q.some((t=>e.type===t)),"function"==typeof e.type?"You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.":`Only elements types ${Q.join(", ")} are allowed. Helmet does not support rendering <${e.type}> elements. Refer to our API for more information.`),$(!t||"string"==typeof t||Array.isArray(t)&&!t.some((e=>"string"!=typeof e)),`Helmet expects a string as a child of <${e.type}>. Did you forget to wrap your children in braces? ( <${e.type}>{\`\`}</${e.type}> ) Refer to our API for more information.`),!0}mapChildrenToProps(e,t){let n={};return s.Children.forEach(e,(e=>{if(!e||!e.props)return;const{children:r,...i}=e.props,a=Object.keys(i).reduce(((e,t)=>(e[Y[t]||t]=i[t],e)),{});let{type:o}=e;switch("symbol"==typeof o?o=o.toString():this.warnOnInvalidChildren(e,r),o){case"Symbol(react.fragment)":t=this.mapChildrenToProps(r,t);break;case"link":case"meta":case"noscript":case"script":case"style":n=this.flattenArrayTypeChildren(e,n,a,r);break;default:t=this.mapObjectTypeChildren(e,t,a,r)}})),this.mapArrayTypeChildrenToProps(n,t)}render(){const{children:e,...t}=this.props;let n={...t},{helmetData:r}=t;if(e&&(n=this.mapChildrenToProps(e,n)),r&&!(r instanceof ke)){r=new ke(r.context,!0),delete n.helmetData}return r?s.createElement(Ne,{...n,context:r.value}):s.createElement(xe.Consumer,null,(e=>s.createElement(Ne,{...n,context:e})))}},r(t,"defaultProps",{defer:!0,encodeSpecialCharacters:!0,prioritizeSeoTags:!1}),t);const Oe={nav:"_nav_12ucu_2",frame1000006514:"_frame1000006514_12ucu_26",logoContainer:"_logoContainer_12ucu_36",logoImage:"_logoImage_12ucu_49",navLinksContainer:"_navLinksContainer_12ucu_58",navLinksGroup:"_navLinksGroup_12ucu_69",navLinkHome:"_navLinkHome_12ucu_87",navLinkHomeText:"_navLinkHomeText_12ucu_98",navLinkServices:"_navLinkServices_12ucu_112",navLinkServicesText:"_navLinkServicesText_12ucu_123",menuContainer:"_menuContainer_12ucu_137",menuButton:"_menuButton_12ucu_158",menuIcon:"_menuIcon_12ucu_171",menuText:"_menuText_12ucu_179",contactButton:"_contactButton_12ucu_193",contactText:"_contactText_12ucu_214",dropdownMenu:"_dropdownMenu_12ucu_229",dropdownColumns:"_dropdownColumns_12ucu_263",dropdownColumn:"_dropdownColumn_12ucu_263",mobileToggler:"_mobileToggler_12ucu_308",mobileMenu:"_mobileMenu_12ucu_318",mobileMenuList:"_mobileMenuList_12ucu_324",topNavCollapse:"_topNavCollapse_12ucu_354",navHidden:"_navHidden_12ucu_359"},je="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M2%202H7V7H2V2ZM9%202H14V7H9V2ZM2%209H7V14H2V9ZM9%209H14V14H9V9Z'%20fill='%23FF3141'/%3e%3c/svg%3e",ze=()=>{const[e,t]=l.useState(!1),[n,r]=l.useState(!1),[i,a]=l.useState(!0),[o,s]=l.useState(0),[c,d]=l.useState(!1),f=l.useRef(null);l.useEffect((()=>{const e=()=>{const e=window.scrollY;t(e>60),e<60?a(!0):e>o+10?a(!1):o>e+10&&a(!0),setTimeout((()=>{s(e)}),50)};return window.addEventListener("scroll",e,{passive:!0}),()=>{window.removeEventListener("scroll",e)}}),[o]),l.useEffect((()=>{const e=()=>{d(window.innerWidth<=768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]),l.useEffect((()=>{const e=e=>{f.current&&!f.current.contains(e.target)&&r(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}}),[]);return S.jsxs("nav",{className:`${Oe.nav} ${e?Oe.topNavCollapse:""} ${i?"":Oe.navHidden}`,children:[S.jsxs("div",{className:Oe.frame1000006514,children:[S.jsx(u,{className:Oe.logoContainer,to:"/",children:S.jsx("img",{className:Oe.logoImage,src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20width='100%25'%20height='100%25'%20viewBox='0%200%20291%20260'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20xml:space='preserve'%20xmlns:serif='http://www.serif.com/'%20style='fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;'%3e%3cg%20transform='matrix(1,0,0,1,-50.9992,-0.603858)'%3e%3cg%20id='Untitled'%3e%3cg%3e%3cpath%20d='M223.68,217.052L198.724,167.606C188.312,171.895%20178.66,175.441%20169.397,179.794C164.83,181.944%20161.04,182.446%20157.827,181.648L151.954,189.417C172.383,201.831%20197.041,207.296%20223.68,217.052Z'%20style='fill:rgb(103,125,124);fill-rule:nonzero;'/%3e%3cpath%20d='M146.846,170.464C134.537,143.926%20141.623,118.23%20166.4,102.449C155.3,80.389%20144.853,59.005%20133.739,37.986C131.44,33.633%20127.29,28.99%20122.897,27.249C100.155,18.253%2077.047,10.145%2050.999,0.604C55.642,9.487%2058.93,15.627%2062.07,21.843C83.715,64.719%20105.065,107.74%20127.102,150.421C134.249,164.263%20140.108,182.226%20151.954,189.426L157.827,181.657C153.091,180.47%20149.618,176.442%20146.846,170.464Z'%20style='fill:rgb(255,50,65);fill-rule:nonzero;'/%3e%3cpath%20d='M330.796,239.347C309.151,196.471%20287.801,153.451%20265.764,110.769C258.625,96.93%20252.766,78.967%20240.921,71.768L235.047,79.536C239.783,80.714%20243.257,84.759%20246.028,90.72C258.338,117.258%20251.252,142.954%20226.475,158.735C237.575,180.795%20248.022,202.179%20259.136,223.198C261.434,227.551%20265.584,232.194%20269.978,233.935C292.72,242.931%20315.828,251.039%20341.875,260.58C337.241,251.706%20333.944,245.566%20330.796,239.347Z'%20style='fill:rgb(103,125,124);fill-rule:nonzero;'/%3e%3cpath%20d='M169.194,44.133L194.151,93.578C204.563,89.289%20214.214,85.743%20223.477,81.39C228.045,79.24%20231.835,78.738%20235.047,79.536L240.921,71.768C220.491,59.353%20195.834,53.889%20169.194,44.133Z'%20style='fill:rgb(255,50,65);fill-rule:nonzero;'/%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e",alt:"ResearchSat Logo"})}),S.jsxs("div",{className:Oe.navLinksContainer,children:[S.jsxs("div",{className:Oe.navLinksGroup,children:[S.jsx(u,{className:Oe.navLinkHome,to:"/",onClick:()=>window.scrollTo(0,0),children:S.jsx("span",{className:Oe.navLinkHomeText,children:"Home"})}),S.jsx(u,{className:Oe.navLinkServices,to:"/about",children:S.jsx("span",{className:Oe.navLinkServicesText,children:"About"})})]}),S.jsxs("div",{className:Oe.menuContainer,onClick:()=>{r(!n)},ref:f,children:[S.jsxs("button",{className:Oe.menuButton,type:"button",children:[S.jsx("img",{className:Oe.menuIcon,src:"data:image/svg+xml,%3csvg%20width='25'%20height='24'%20viewBox='0%200%2025%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cmask%20id='mask0_180_31503'%20style='mask-type:alpha'%20maskUnits='userSpaceOnUse'%20x='0'%20y='0'%20width='25'%20height='24'%3e%3crect%20x='0.5'%20width='24'%20height='24'%20fill='%23D9D9D9'/%3e%3c/mask%3e%3cg%20mask='url(%23mask0_180_31503)'%3e%3cpath%20d='M11.05%2018.2L16.225%2012H12.225L12.95%206.325L8.325%2013H11.8L11.05%2018.2ZM8.5%2022L9.5%2015H4.5L13.5%202H15.5L14.5%2010H20.5L10.5%2022H8.5Z'%20fill='%23FF3241'/%3e%3c/g%3e%3c/svg%3e",alt:"Menu"}),S.jsx("span",{className:Oe.menuText,children:"Menu"})]}),n&&S.jsx("div",{className:Oe.dropdownMenu,children:S.jsxs("div",{className:Oe.dropdownColumns,children:[S.jsxs("div",{className:Oe.dropdownColumn,children:[S.jsxs(u,{to:"/",onClick:()=>r(!1),children:[S.jsx("img",{src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M8%201.5L1%207H3V14H7V10H9V14H13V7H15L8%201.5Z'%20fill='%23FF3141'/%3e%3c/svg%3e",alt:"Home",width:"16",height:"16"}),"Home"]}),S.jsxs(u,{to:"/about",onClick:()=>r(!1),children:[S.jsx("img",{src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M8%201C4.13401%201%201%204.13401%201%208C1%2011.866%204.13401%2015%208%2015C11.866%2015%2015%2011.866%2015%208C15%204.13401%2011.866%201%208%201ZM8%206.5C8.55228%206.5%209%206.94772%209%207.5V11.5C9%2012.0523%208.55228%2012.5%208%2012.5C7.44772%2012.5%207%2012.0523%207%2011.5V7.5C7%206.94772%207.44772%206.5%208%206.5ZM8%205.5C7.44772%205.5%207%205.05228%207%204.5C7%203.94772%207.44772%203.5%208%203.5C8.55228%203.5%209%203.94772%209%204.5C9%205.05228%208.55228%205.5%208%205.5Z'%20fill='%23FF3141'/%3e%3c/svg%3e",alt:"About",width:"16",height:"16"}),"About"]}),S.jsxs(u,{to:"/features",onClick:()=>r(!1),children:[S.jsx("img",{src:je,alt:"Features",width:"16",height:"16"}),"Features"]}),S.jsxs(u,{to:"/offerings",onClick:()=>r(!1),children:[S.jsx("img",{src:je,alt:"Offerings",width:"16",height:"16"}),"Our Offerings"]})]}),S.jsxs("div",{className:Oe.dropdownColumn,children:[S.jsxs(u,{to:"/missions",onClick:()=>r(!1),children:[S.jsx("img",{src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M8%201L9.93%205.2L14.5%205.8L11.25%209.1L12%2014L8%2011.7L4%2014L4.75%209.1L1.5%205.8L6.07%205.2L8%201Z'%20fill='%23FF3141'/%3e%3c/svg%3e",alt:"Mission",width:"16",height:"16"}),"Mission"]}),S.jsxs(u,{to:"/payloads",onClick:()=>r(!1),children:[S.jsx("img",{src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M8%201L1%204.5V7.5L8%2011L14%207.5V10.5H15V4.5L8%201ZM8%202.25L13%204.75L8%207.25L3%204.75L8%202.25ZM3%209.5V6L7.5%208.25V11.75L3%209.5ZM13%2012.5V14.5H3V12.5H2V15.5H14V12.5H13Z'%20fill='%23FF3141'/%3e%3c/svg%3e",alt:"Payloads",width:"16",height:"16"}),"Payloads"]})]}),S.jsxs("div",{className:Oe.dropdownColumn,children:[S.jsxs(u,{to:"/news",onClick:()=>r(!1),children:[S.jsx("img",{src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M14%202H2V3H14V2ZM14%204H2V5H14V4ZM14%206H2V7H14V6ZM14%208H2V9H14V8ZM11%2010H2V11H11V10ZM11%2012H2V13H11V12Z'%20fill='%23FF3141'/%3e%3c/svg%3e",alt:"News",width:"16",height:"16"}),"News"]}),S.jsxs(u,{to:"/careers",onClick:()=>r(!1),children:[S.jsx("img",{src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M6%202V4H10V2H12V4H14V6H12V12H14V14H2V12H4V6H2V4H4V2H6ZM6%206V12H10V6H6Z'%20fill='%23FF3141'/%3e%3c/svg%3e",alt:"Careers",width:"16",height:"16"}),"Careers"]}),S.jsxs(u,{to:"/contact",onClick:()=>r(!1),children:[S.jsx("img",{src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M8%202C6.34%202%205%203.34%205%205C5%206.66%206.34%208%208%208C9.66%208%2011%206.66%2011%205C11%203.34%209.66%202%208%202ZM8%209.5C5.33%209.5%200%2010.84%200%2013.5V15H16V13.5C16%2010.84%2010.67%209.5%208%209.5Z'%20fill='%23FF3141'/%3e%3c/svg%3e",alt:"Contact",width:"16",height:"16"}),"Contact"]})]})]})})]}),S.jsx(u,{className:Oe.contactButton,to:"/book-mission",children:S.jsx("span",{className:Oe.contactText,children:c?"Book":"Book Mission"})})]}),S.jsx("button",{className:Oe.mobileToggler,type:"button","data-bs-toggle":"collapse","data-bs-target":"#mobileNavMenu","aria-controls":"mobileNavMenu","aria-expanded":"false","aria-label":"Toggle navigation",children:S.jsx("span",{className:Oe.togglerIcon})})]}),S.jsx("div",{className:"collapse navbar-collapse",id:"mobileNavMenu",children:S.jsx("div",{className:Oe.mobileMenu,children:S.jsxs("ul",{className:Oe.mobileMenuList,children:[S.jsx("li",{children:S.jsx(u,{to:"/",onClick:()=>window.scrollTo(0,0),children:"Home"})}),S.jsx("li",{children:S.jsx(u,{to:"/about",children:"About"})}),S.jsx("li",{children:S.jsx(u,{to:"/features",children:"Features"})}),S.jsx("li",{children:S.jsx(u,{to:"/offerings",children:"Our Offerings"})}),S.jsx("li",{children:S.jsx(u,{to:"/missions",children:"Mission"})}),S.jsx("li",{children:S.jsx(u,{to:"/payloads",children:"Payloads"})}),S.jsx("li",{children:S.jsx(u,{to:"/news",children:"News"})}),S.jsx("li",{children:S.jsx(u,{to:"/careers",children:"Careers"})}),S.jsx("li",{children:S.jsx(u,{to:"/contact",children:"Contact"})}),S.jsx("li",{children:S.jsx(u,{to:"/book-mission",children:c?"Book":"Book Mission"})})]})})})]})},Me="_footer_aiyir_2",De="_footerTop_aiyir_10",Ie="_footerLogo_aiyir_16",Fe="_footerContent_aiyir_22",$e="_footerColumn_aiyir_29",He="_footerTitle_aiyir_40",Re="_footerText_aiyir_48",Ve="_footerLink_aiyir_55",Be="_footerAddress_aiyir_69",Ue="_footerContact_aiyir_74",We="_footerDivider_aiyir_80",qe="_footerBottom_aiyir_87",Qe="_footerCopyright_aiyir_96",Ke="_socialContainer_aiyir_104",Ye="_socialLinks_aiyir_109",Ze="_socialText_aiyir_116",Xe="_socialIcon_aiyir_125",Ge="_facebookIcon_aiyir_136",Je="_twitterIcon_aiyir_140",et="_instagramIcon_aiyir_140",tt="_linkedinIcon_aiyir_144",nt="_emailLabel_aiyir_152",rt="_newsletterForm_aiyir_160",it="_newsletterInput_aiyir_166",at="_newsletterButton_aiyir_180",ot=()=>{const[e,t]=l.useState("");return S.jsx("footer",{className:Me,children:S.jsxs("div",{className:"container",children:[S.jsx("div",{className:De,children:S.jsx("img",{src:"/assets/png/ResearchSatLogo-DQHoConw.png",alt:"ResearchSat Logo",className:Ie})}),S.jsxs("div",{className:Fe,children:[S.jsxs("div",{className:$e,children:[S.jsxs("div",{className:Be,children:[S.jsx("p",{className:Re,children:"9 Light Square,"}),S.jsx("p",{className:Re,children:"Adelaide, SA 5000"})]}),S.jsx("p",{className:Ue,children:"<EMAIL>"}),S.jsx("p",{className:Ue,children:"+61 124.459.8900"})]}),S.jsxs("div",{className:$e,children:[S.jsx(u,{to:"/",className:Ve,children:"Home"}),S.jsx(u,{to:"/about",className:Ve,children:"About"}),S.jsx(u,{to:"/missions",className:Ve,children:"Mission"}),S.jsx(u,{to:"/terms-conditions",className:Ve,children:"Terms & Conditions"})]}),S.jsxs("div",{className:$e,children:[S.jsx(u,{to:"/news",className:Ve,children:"News"}),S.jsx(u,{to:"/careers",className:Ve,children:"Careers"}),S.jsx(u,{to:"/contact",className:Ve,children:"Contact"}),S.jsx(u,{to:"/privacy-policy",className:Ve,children:"Privacy Policy"})]}),S.jsxs("div",{className:$e,children:[S.jsx(u,{to:"/payloads",className:Ve,children:"Payloads"}),S.jsx(u,{to:"/past-missions",className:Ve,children:"Past Missions"}),S.jsx(u,{to:"/copyright",className:Ve,children:"Copyright"}),S.jsx(u,{to:"/cookies-policy",className:Ve,children:"Cookies Policy"})]}),S.jsxs("div",{className:$e,children:[S.jsx("h3",{className:He,children:"Subscribe to our newsletter"}),S.jsx("p",{className:Re,children:"Stay updated on our latest missions &"}),S.jsx("p",{className:Re,children:"advancements"}),S.jsx("p",{className:nt,children:"Email ID"}),S.jsxs("form",{onSubmit:e=>{e.preventDefault(),t("")},className:rt,children:[S.jsx("input",{type:"email",placeholder:"Enter Email ID",className:it,value:e,onChange:e=>t(e.target.value),required:!0}),S.jsx("button",{type:"submit",className:at,children:S.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:S.jsx("path",{d:"M5 12H19M19 12L12 5M19 12L12 19",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})]})]}),S.jsx("div",{className:We}),S.jsxs("div",{className:qe,children:[S.jsxs("div",{className:Qe,children:["Copyright © ",(new Date).getFullYear()," ResearchSat - All rights reserved"]}),S.jsxs("div",{className:Ke,children:[S.jsx("div",{className:Ze,children:"Reach us"}),S.jsxs("div",{className:Ye,children:[S.jsx("a",{href:"https://www.facebook.com/researchsat/",target:"_blank",rel:"noopener noreferrer",className:Xe,children:S.jsx("img",{src:"data:image/svg+xml,%3csvg%20width='33'%20height='34'%20viewBox='0%200%2033%2034'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M26.8005%200.332031C30.2275%200.332031%2033%203.10126%2033%206.52434V27.1397C33%2030.5628%2030.2275%2033.332%2026.8005%2033.332H22.7958V20.5243H27.07L27.6861%2015.5628H22.7958V12.3705C22.7958%2010.9474%2023.1809%209.98588%2025.2602%209.98588L27.8786%209.94742V5.52434C27.4166%205.44742%2025.8378%205.33203%2024.028%205.33203C20.2544%205.33203%2017.6359%207.63972%2017.6359%2011.909V15.5628H13.3232V20.5243H17.6359V33.332H6.19953C2.77246%2033.332%200%2030.5628%200%2027.1397V6.52434C0%203.10126%202.77246%200.332031%206.19953%200.332031H26.8005Z'%20fill='%23636363'/%3e%3c/svg%3e",alt:"Facebook",className:Ge})}),S.jsx("a",{href:"https://twitter.com/researchsat",target:"_blank",rel:"noopener noreferrer",className:Xe,children:S.jsx("img",{src:"data:image/svg+xml,%3csvg%20width='30'%20height='30'%20viewBox='0%200%2030%2030'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_180_31482)'%3e%3cpath%20d='M17.7926%2012.6117L28.7205%200.332031H26.1307L16.6423%2010.9941L9.06352%200.332031H0.322266L11.7827%2016.4551L0.322266%2029.332H2.91211L12.9326%2018.0726L20.936%2029.332H29.6773L17.7919%2012.6117H17.7926ZM14.2455%2016.597L13.0842%2014.9915L3.84516%202.21658H7.82297L15.2787%2012.5265L16.4398%2014.132L26.1319%2027.5331H22.1545L14.2455%2016.5976V16.597Z'%20fill='%23636363'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_180_31482'%3e%3crect%20width='30'%20height='29'%20fill='white'%20transform='translate(0%200.332031)'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",alt:"Twitter",className:Je})}),S.jsx("a",{href:"https://www.instagram.com/researchsat/",target:"_blank",rel:"noopener noreferrer",className:Xe,children:S.jsx("img",{src:"/assets/svg/instagram-Bz-HIF1W.svg",alt:"Instagram",className:et})}),S.jsx("a",{href:"https://www.linkedin.com/company/researchsat/",target:"_blank",rel:"noopener noreferrer",className:Xe,children:S.jsx("img",{src:"data:image/svg+xml,%3csvg%20width='32'%20height='33'%20viewBox='0%200%2032%2033'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_180_31483)'%3e%3cpath%20d='M4.92882%2027.1106H9.74562V12.677H4.92882V27.1106ZM10.0817%208.20149C10.0443%206.78425%209.03617%205.70266%207.39323%205.70266C5.75029%205.70266%204.63011%206.78425%204.63011%208.20149C4.63011%209.58145%205.67561%2010.7003%207.31855%2010.7003C9.03617%2010.7003%2010.0817%209.58145%2010.0817%208.20149ZM22.2544%2027.1106H27.0712V18.8309C27.0712%2014.3926%2024.6814%2012.3414%2021.5449%2012.3414C18.9312%2012.3414%2017.811%2013.7586%2017.1762%2014.7656H17.2135V12.677H12.3967C12.3967%2012.677%2012.4714%2014.0197%2012.3967%2027.1106H17.2135V19.0173C17.2135%2018.6071%2017.2509%2018.1968%2017.3629%2017.8612C17.699%2017.0034%2018.5204%2016.1083%2019.8273%2016.1083C21.5823%2016.1083%2022.2544%2017.4136%2022.2544%2019.3903V27.1106ZM32%206.33669V26.3274C32%2029.6467%2029.3116%2032.332%2025.9883%2032.332H6.01167C2.68845%2032.332%200%2029.6467%200%2026.3274V6.33669C0%203.01735%202.68845%200.332031%206.01167%200.332031H25.9883C29.3116%200.332031%2032%203.01735%2032%206.33669Z'%20fill='%23636363'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_180_31483'%3e%3crect%20width='32'%20height='32'%20fill='white'%20transform='translate(0%200.332031)'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",alt:"LinkedIn",className:tt})})]})]})]})]})})},lt="_loadingContainer_1ozwo_2",st="_spinner_1ozwo_11",ut="_doubleBounce1_1ozwo_18",ct="_doubleBounce2_1ozwo_18",dt="_loadingText_1ozwo_34",ft=()=>S.jsxs("div",{className:lt,children:[S.jsxs("div",{className:st,children:[S.jsx("div",{className:ut}),S.jsx("div",{className:ct})]}),S.jsx("p",{className:dt,children:"Loading..."})]}),pt=e=>{let t;const n=new Set,r=(e,r)=>{const i="function"==typeof e?e(t):e;if(!Object.is(i,t)){const e=t;t=(null!=r?r:"object"!=typeof i||null===i)?i:Object.assign({},t,i),n.forEach((n=>n(t,e)))}},i=()=>t,a={setState:r,getState:i,getInitialState:()=>o,subscribe:e=>(n.add(e),()=>n.delete(e))},o=t=e(r,i,a);return a},ht=e=>e;const mt=e=>{const t=(e=>e?pt(e):pt)(e),n=e=>function(e,t=ht){const n=s.useSyncExternalStore(e.subscribe,(()=>t(e.getState())),(()=>t(e.getInitialState())));return s.useDebugValue(n),n}(t,e);return Object.assign(n,t),n};function gt(e,t){let n;try{n=e()}catch(r){return}return{getItem:e=>{var t;const r=e=>null===e?null:JSON.parse(e,void 0),i=null!=(t=n.getItem(e))?t:null;return i instanceof Promise?i.then(r):r(i)},setItem:(e,t)=>n.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>n.removeItem(e)}}const vt=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then:e=>vt(e)(n),catch(e){return this}}}catch(n){return{then(e){return this},catch:e=>vt(e)(n)}}},bt=(yt=((e,t)=>(n,r,i)=>{let a={storage:gt((()=>localStorage)),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},o=!1;const l=new Set,s=new Set;let u=a.storage;if(!u)return e(((...e)=>{n(...e)}),r,i);const c=()=>{const e=a.partialize({...r()});return u.setItem(a.name,{state:e,version:a.version})},d=i.setState;i.setState=(e,t)=>{d(e,t),c()};const f=e(((...e)=>{n(...e),c()}),r,i);let p;i.getInitialState=()=>f;const h=()=>{var e,t;if(!u)return;o=!1,l.forEach((e=>{var t;return e(null!=(t=r())?t:f)}));const i=(null==(t=a.onRehydrateStorage)?void 0:t.call(a,null!=(e=r())?e:f))||void 0;return vt(u.getItem.bind(u))(a.name).then((e=>{if(e){if("number"!=typeof e.version||e.version===a.version)return[!1,e.state];if(a.migrate){const t=a.migrate(e.state,e.version);return t instanceof Promise?t.then((e=>[!0,e])):[!0,t]}}return[!1,void 0]})).then((e=>{var t;const[i,o]=e;if(p=a.merge(o,null!=(t=r())?t:f),n(p,!0),i)return c()})).then((()=>{null==i||i(p,void 0),p=r(),o=!0,s.forEach((e=>e(p)))})).catch((e=>{null==i||i(void 0,e)}))};return i.persist={setOptions:e=>{a={...a,...e},e.storage&&(u=e.storage)},clearStorage:()=>{null==u||u.removeItem(a.name)},getOptions:()=>a,rehydrate:()=>h(),hasHydrated:()=>o,onHydrate:e=>(l.add(e),()=>{l.delete(e)}),onFinishHydration:e=>(s.add(e),()=>{s.delete(e)})},a.skipHydration||h(),p||f})(((e,t)=>({isLoading:!1,setIsLoading:t=>e({isLoading:t}),activeModal:null,setActiveModal:t=>e({activeModal:t}),closeModal:()=>e({activeModal:null}),notifications:[],addNotification:t=>e((e=>({notifications:[...e.notifications,{id:Date.now(),type:"info",duration:3e3,...t}]}))),removeNotification:t=>e((e=>({notifications:e.notifications.filter((e=>e.id!==t))}))),clearNotifications:()=>e({notifications:[]}),formSubmissions:{},trackFormSubmission:t=>e((e=>{var n;return{formSubmissions:{...e.formSubmissions,[t]:{lastSubmitted:(new Date).toISOString(),count:((null==(n=e.formSubmissions[t])?void 0:n.count)||0)+1}}}})),pageViews:{},trackPageView:t=>e((e=>({pageViews:{...e.pageViews,[t]:(e.pageViews[t]||0)+1}}))),featureFlags:{enableNewHeader:!1,enableNewFooter:!1,enableNewContactForm:!1},setFeatureFlag:(t,n)=>e((e=>({featureFlags:{...e.featureFlags,[t]:n}})))})),{name:"researchsat-app-store",partialize:e=>({featureFlags:e.featureFlags,pageViews:e.pageViews,formSubmissions:e.formSubmissions})}))?mt(yt):mt;var yt;const _t={notificationContainer:"_notificationContainer_yx8c7_1",notification:"_notification_yx8c7_1",slideIn:"_slideIn_yx8c7_1",success:"_success_yx8c7_25",error:"_error_yx8c7_29",warning:"_warning_yx8c7_33",info:"_info_yx8c7_37",icon:"_icon_yx8c7_41",content:"_content_yx8c7_62",closeButton:"_closeButton_yx8c7_68",slideOut:"_slideOut_yx8c7_1"},wt=({id:e,type:t,message:n,duration:r})=>{const i=bt((e=>e.removeNotification));l.useEffect((()=>{const t=setTimeout((()=>{i(e)}),r);return()=>clearTimeout(t)}),[e,r,i]);return S.jsxs("div",{className:`${_t.notification} ${_t[t]}`,children:[S.jsx("div",{className:_t.icon,children:(()=>{switch(t){case"success":return S.jsx("i",{className:"fas fa-check-circle"});case"error":return S.jsx("i",{className:"fas fa-exclamation-circle"});case"warning":return S.jsx("i",{className:"fas fa-exclamation-triangle"});default:return S.jsx("i",{className:"fas fa-info-circle"})}})()}),S.jsx("div",{className:_t.content,children:n}),S.jsx("button",{className:_t.closeButton,onClick:()=>i(e),"aria-label":"Close notification",children:S.jsx("i",{className:"fas fa-times"})})]})},kt=()=>{const e=bt((e=>e.notifications));return 0===e.length?null:S.jsx("div",{className:_t.notificationContainer,children:e.map((e=>S.jsx(wt,{...e},e.id)))})},xt="_backToTopButton_kvczo_1",St="_buttonInner_kvczo_26",Et="_arrowIcon_kvczo_38",Ct="_ringAnimation_kvczo_48",Tt=()=>{const[e,t]=l.useState(!1);l.useEffect((()=>{const e=()=>{window.pageYOffset>300?t(!0):t(!1)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)}),[]);return S.jsx(S.Fragment,{children:e&&S.jsxs("button",{className:xt,onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},"aria-label":"Back to top",children:[S.jsx("div",{className:St,children:S.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:Et,children:S.jsx("path",{d:"M12 5L5 12M12 5L19 12M12 5V19",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),S.jsx("div",{className:Ct})]})})},Lt=l.createContext(),At={notifications:!0,fontSize:"medium",animationsEnabled:!0,lastVisitedPage:"/",consentGiven:!1},Nt=({children:e})=>{const[t,n]=l.useState((()=>{const e=localStorage.getItem("userPreferences");return e?JSON.parse(e):At}));l.useEffect((()=>{localStorage.setItem("userPreferences",JSON.stringify(t))}),[t]);const r=(e,t)=>{e in At&&n((n=>({...n,[e]:t})))},i={preferences:t,updatePreference:r,resetPreferences:()=>{n(At)},updateLastVisitedPage:e=>{r("lastVisitedPage",e)},toggleNotifications:()=>{r("notifications",!t.notifications)},setFontSize:e=>{["small","medium","large"].includes(e)&&r("fontSize",e)},toggleAnimations:()=>{r("animationsEnabled",!t.animationsEnabled)},setConsent:e=>{r("consentGiven",e)}};return S.jsx(Lt.Provider,{value:i,children:e})},Pt=({children:e})=>S.jsx(Nt,{children:e}),Ot=l.lazy((()=>j((()=>import("./HomePage-DGkHi5g0.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12])))),jt=l.lazy((()=>j((()=>import("./AboutPage-DiqxacV1.js")),__vite__mapDeps([13,1,8,14,15,16,11,17])))),zt=l.lazy((()=>j((()=>import("./FeaturesPage-DK2Hvggj.js")),__vite__mapDeps([18,1,10,14,15,8,2,11,19])))),Mt=l.lazy((()=>j((()=>import("./PayloadsPage-3yeaJz2w.js")),__vite__mapDeps([20,1,10,8,7,9,21,11,22])))),Dt=l.lazy((()=>j((()=>import("./MissionsPage-CsndvgYN.js")),__vite__mapDeps([23,1,10,24,14,15,25,8,26,7,9,21,11,27])))),It=l.lazy((()=>j((()=>import("./PastMissionsPage-CHjHD8aV.js")),__vite__mapDeps([28,1,10,14,15,25,8,26,7,9,11,29])))),Ft=l.lazy((()=>j((()=>import("./CareersPage-BBvVkwf0.js")),__vite__mapDeps([30,1,10,11,31])))),$t=l.lazy((()=>j((()=>import("./ContactPage-CaEFMZJD.js")),__vite__mapDeps([32,1,8,11,33])))),Ht=l.lazy((()=>j((()=>import("./PartnershipsPage-C7HcO-4m.js")),__vite__mapDeps([34,1,10,14,15,3,4,11,35])))),Rt=l.lazy((()=>j((()=>import("./NewsPage-BzFB5KYq.js")),__vite__mapDeps([36,1,10,14,15,5,6,11,37])))),Vt=l.lazy((()=>j((()=>import("./NewsArticlePage-CN9FHy0C.js")),__vite__mapDeps([38,1,10,14,15,5,11,39])))),Bt=l.lazy((()=>j((()=>import("./PrivacyPolicyPage-nNqoRAAZ.js")),__vite__mapDeps([40,1,11])))),Ut=l.lazy((()=>j((()=>import("./TermsConditionsPage-L3ng8iYH.js")),__vite__mapDeps([41,1,11])))),Wt=l.lazy((()=>j((()=>import("./EmailSignPage-DIeKiie_.js")),__vite__mapDeps([42,1,10,11])))),qt=l.lazy((()=>j((()=>import("./TestPage-C7FTlaxU.js")),__vite__mapDeps([43,1,10,11,44])))),Qt=l.lazy((()=>j((()=>import("./BookMissionPage-Ba9VIrgu.js")),__vite__mapDeps([45,1,46,24,11,47])))),Kt=l.lazy((()=>j((()=>import("./SpaceXperimentPage-CxkWPAk9.js")),__vite__mapDeps([48,1,46,11,49])))),Yt=l.lazy((()=>j((()=>import("./OfferingsPage-BfPJHwp-.js")),__vite__mapDeps([50,1,16,11,51])))),Zt=l.lazy((()=>j((()=>import("./NotFoundPage-CjZzi6sk.js")),__vite__mapDeps([52,1,11])))),Xt=()=>{const e=d(),t=bt((e=>e.trackPageView));return l.useEffect((()=>{t(e.pathname)}),[e.pathname,t]),S.jsxs(S.Fragment,{children:[S.jsx(ze,{}),S.jsx(l.Suspense,{fallback:S.jsx(ft,{}),children:S.jsxs(f,{children:[S.jsx(p,{path:"/",element:S.jsx(Ot,{})}),S.jsx(p,{path:"/about",element:S.jsx(jt,{})}),S.jsx(p,{path:"/features",element:S.jsx(zt,{})}),S.jsx(p,{path:"/payloads",element:S.jsx(Mt,{})}),S.jsx(p,{path:"/missions",element:S.jsx(Dt,{})}),S.jsx(p,{path:"/past-missions",element:S.jsx(It,{})}),S.jsx(p,{path:"/careers",element:S.jsx(Ft,{})}),S.jsx(p,{path:"/contact",element:S.jsx($t,{})}),S.jsx(p,{path:"/partnerships",element:S.jsx(Ht,{})}),S.jsx(p,{path:"/news",element:S.jsx(Rt,{})}),S.jsx(p,{path:"/news/article/:id",element:S.jsx(Vt,{})}),S.jsx(p,{path:"/privacy-policy",element:S.jsx(Bt,{})}),S.jsx(p,{path:"/terms-conditions",element:S.jsx(Ut,{})}),S.jsx(p,{path:"/emailSign",element:S.jsx(Wt,{})}),S.jsx(p,{path:"/test",element:S.jsx(qt,{})}),S.jsx(p,{path:"/book-mission",element:S.jsx(Qt,{})}),S.jsx(p,{path:"/spacexperiment",element:S.jsx(Kt,{})}),S.jsx(p,{path:"/offerings",element:S.jsx(Yt,{})}),S.jsx(p,{path:"*",element:S.jsx(Zt,{})})]})}),S.jsx(ot,{}),S.jsx(Tt,{})]})},Gt=()=>S.jsx(Se,{children:S.jsx(Pt,{children:S.jsxs(c,{children:[S.jsx(Xt,{}),S.jsx(kt,{})]})})});var Jt,en={exports:{}};
/*!
  * Bootstrap v5.3.6 (https://getbootstrap.com/)
  * Copyright 2011-2025 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */Jt||(Jt=1,en.exports=function(){const e=new Map,t={set(t,n,r){e.has(t)||e.set(t,new Map);const i=e.get(t);(i.has(n)||0===i.size)&&i.set(n,r)},get:(t,n)=>e.has(t)&&e.get(t).get(n)||null,remove(t,n){if(!e.has(t))return;const r=e.get(t);r.delete(n),0===r.size&&e.delete(t)}},n="transitionend",r=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,((e,t)=>`#${CSS.escape(t)}`))),e),i=e=>{e.dispatchEvent(new Event(n))},a=e=>!(!e||"object"!=typeof e)&&(void 0!==e.jquery&&(e=e[0]),void 0!==e.nodeType),o=e=>a(e)?e.jquery?e[0]:e:"string"==typeof e&&e.length>0?document.querySelector(r(e)):null,l=e=>{if(!a(e)||0===e.getClientRects().length)return!1;const t="visible"===getComputedStyle(e).getPropertyValue("visibility"),n=e.closest("details:not([open])");if(!n)return t;if(n!==e){const t=e.closest("summary");if(t&&t.parentNode!==n)return!1;if(null===t)return!1}return t},s=e=>!e||e.nodeType!==Node.ELEMENT_NODE||!!e.classList.contains("disabled")||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled")),u=e=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){const t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?u(e.parentNode):null},c=()=>{},d=e=>{e.offsetHeight},f=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,p=[],h=()=>"rtl"===document.documentElement.dir,m=e=>{var t;t=()=>{const t=f();if(t){const n=e.NAME,r=t.fn[n];t.fn[n]=e.jQueryInterface,t.fn[n].Constructor=e,t.fn[n].noConflict=()=>(t.fn[n]=r,e.jQueryInterface)}},"loading"===document.readyState?(p.length||document.addEventListener("DOMContentLoaded",(()=>{for(const e of p)e()})),p.push(t)):t()},g=(e,t=[],n=e)=>"function"==typeof e?e.call(...t):n,v=(e,t,r=!0)=>{if(!r)return void g(e);const a=(e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:n}=window.getComputedStyle(e);const r=Number.parseFloat(t),i=Number.parseFloat(n);return r||i?(t=t.split(",")[0],n=n.split(",")[0],1e3*(Number.parseFloat(t)+Number.parseFloat(n))):0})(t)+5;let o=!1;const l=({target:r})=>{r===t&&(o=!0,t.removeEventListener(n,l),g(e))};t.addEventListener(n,l),setTimeout((()=>{o||i(t)}),a)},b=(e,t,n,r)=>{const i=e.length;let a=e.indexOf(t);return-1===a?!n&&r?e[i-1]:e[0]:(a+=n?1:-1,r&&(a=(a+i)%i),e[Math.max(0,Math.min(a,i-1))])},y=/[^.]*(?=\..*)\.|.*/,_=/\..*/,w=/::\d+$/,k={};let x=1;const S={mouseenter:"mouseover",mouseleave:"mouseout"},E=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function C(e,t){return t&&`${t}::${x++}`||e.uidEvent||x++}function T(e){const t=C(e);return e.uidEvent=t,k[t]=k[t]||{},k[t]}function L(e,t,n=null){return Object.values(e).find((e=>e.callable===t&&e.delegationSelector===n))}function A(e,t,n){const r="string"==typeof t,i=r?n:t||n;let a=j(e);return E.has(a)||(a=e),[r,i,a]}function N(e,t,n,r,i){if("string"!=typeof t||!e)return;let[a,o,l]=A(t,n,r);var s;t in S&&(s=o,o=function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return s.call(this,e)});const u=T(e),c=u[l]||(u[l]={}),d=L(c,o,a?n:null);if(d)return void(d.oneOff=d.oneOff&&i);const f=C(o,t.replace(y,"")),p=a?function(e,t,n){return function r(i){const a=e.querySelectorAll(t);for(let{target:o}=i;o&&o!==this;o=o.parentNode)for(const l of a)if(l===o)return M(i,{delegateTarget:o}),r.oneOff&&z.off(e,i.type,t,n),n.apply(o,[i])}}(e,n,o):function(e,t){return function n(r){return M(r,{delegateTarget:e}),n.oneOff&&z.off(e,r.type,t),t.apply(e,[r])}}(e,o);p.delegationSelector=a?n:null,p.callable=o,p.oneOff=i,p.uidEvent=f,c[f]=p,e.addEventListener(l,p,a)}function P(e,t,n,r,i){const a=L(t[n],r,i);a&&(e.removeEventListener(n,a,Boolean(i)),delete t[n][a.uidEvent])}function O(e,t,n,r){const i=t[n]||{};for(const[a,o]of Object.entries(i))a.includes(r)&&P(e,t,n,o.callable,o.delegationSelector)}function j(e){return e=e.replace(_,""),S[e]||e}const z={on(e,t,n,r){N(e,t,n,r,!1)},one(e,t,n,r){N(e,t,n,r,!0)},off(e,t,n,r){if("string"!=typeof t||!e)return;const[i,a,o]=A(t,n,r),l=o!==t,s=T(e),u=s[o]||{},c=t.startsWith(".");if(void 0===a){if(c)for(const n of Object.keys(s))O(e,s,n,t.slice(1));for(const[n,r]of Object.entries(u)){const i=n.replace(w,"");l&&!t.includes(i)||P(e,s,o,r.callable,r.delegationSelector)}}else{if(!Object.keys(u).length)return;P(e,s,o,a,i?n:null)}},trigger(e,t,n){if("string"!=typeof t||!e)return null;const r=f();let i=null,a=!0,o=!0,l=!1;t!==j(t)&&r&&(i=r.Event(t,n),r(e).trigger(i),a=!i.isPropagationStopped(),o=!i.isImmediatePropagationStopped(),l=i.isDefaultPrevented());const s=M(new Event(t,{bubbles:a,cancelable:!0}),n);return l&&s.preventDefault(),o&&e.dispatchEvent(s),s.defaultPrevented&&i&&i.preventDefault(),s}};function M(e,t={}){for(const[r,i]of Object.entries(t))try{e[r]=i}catch(n){Object.defineProperty(e,r,{configurable:!0,get:()=>i})}return e}function D(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function I(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}const F={setDataAttribute(e,t,n){e.setAttribute(`data-bs-${I(t)}`,n)},removeDataAttribute(e,t){e.removeAttribute(`data-bs-${I(t)}`)},getDataAttributes(e){if(!e)return{};const t={},n=Object.keys(e.dataset).filter((e=>e.startsWith("bs")&&!e.startsWith("bsConfig")));for(const r of n){let n=r.replace(/^bs/,"");n=n.charAt(0).toLowerCase()+n.slice(1),t[n]=D(e.dataset[r])}return t},getDataAttribute:(e,t)=>D(e.getAttribute(`data-bs-${I(t)}`))};class ${static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,t){const n=a(t)?F.getDataAttribute(t,"config"):{};return{...this.constructor.Default,..."object"==typeof n?n:{},...a(t)?F.getDataAttributes(t):{},..."object"==typeof e?e:{}}}_typeCheckConfig(e,t=this.constructor.DefaultType){for(const[r,i]of Object.entries(t)){const t=e[r],o=a(t)?"element":null==(n=t)?`${n}`:Object.prototype.toString.call(n).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(i).test(o))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${r}" provided type "${o}" but expected type "${i}".`)}var n}}class H extends ${constructor(e,n){super(),(e=o(e))&&(this._element=e,this._config=this._getConfig(n),t.set(this._element,this.constructor.DATA_KEY,this))}dispose(){t.remove(this._element,this.constructor.DATA_KEY),z.off(this._element,this.constructor.EVENT_KEY);for(const e of Object.getOwnPropertyNames(this))this[e]=null}_queueCallback(e,t,n=!0){v(e,t,n)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return t.get(o(e),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,"object"==typeof t?t:null)}static get VERSION(){return"5.3.6"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}const R=e=>{let t=e.getAttribute("data-bs-target");if(!t||"#"===t){let n=e.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n=`#${n.split("#")[1]}`),t=n&&"#"!==n?n.trim():null}return t?t.split(",").map((e=>r(e))).join(","):null},V={find:(e,t=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(t,e)),findOne:(e,t=document.documentElement)=>Element.prototype.querySelector.call(t,e),children:(e,t)=>[].concat(...e.children).filter((e=>e.matches(t))),parents(e,t){const n=[];let r=e.parentNode.closest(t);for(;r;)n.push(r),r=r.parentNode.closest(t);return n},prev(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return[n];n=n.nextElementSibling}return[]},focusableChildren(e){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((e=>`${e}:not([tabindex^="-"])`)).join(",");return this.find(t,e).filter((e=>!s(e)&&l(e)))},getSelectorFromElement(e){const t=R(e);return t&&V.findOne(t)?t:null},getElementFromSelector(e){const t=R(e);return t?V.findOne(t):null},getMultipleElementsFromSelector(e){const t=R(e);return t?V.find(t):[]}},B=(e,t="hide")=>{const n=`click.dismiss${e.EVENT_KEY}`,r=e.NAME;z.on(document,n,`[data-bs-dismiss="${r}"]`,(function(n){if(["A","AREA"].includes(this.tagName)&&n.preventDefault(),s(this))return;const i=V.getElementFromSelector(this)||this.closest(`.${r}`);e.getOrCreateInstance(i)[t]()}))},U=".bs.alert",W=`close${U}`,q=`closed${U}`;class Q extends H{static get NAME(){return"alert"}close(){if(z.trigger(this._element,W).defaultPrevented)return;this._element.classList.remove("show");const e=this._element.classList.contains("fade");this._queueCallback((()=>this._destroyElement()),this._element,e)}_destroyElement(){this._element.remove(),z.trigger(this._element,q),this.dispose()}static jQueryInterface(e){return this.each((function(){const t=Q.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}}))}}B(Q,"close"),m(Q);const K='[data-bs-toggle="button"]';class Y extends H{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(e){return this.each((function(){const t=Y.getOrCreateInstance(this);"toggle"===e&&t[e]()}))}}z.on(document,"click.bs.button.data-api",K,(e=>{e.preventDefault();const t=e.target.closest(K);Y.getOrCreateInstance(t).toggle()})),m(Y);const Z=".bs.swipe",X=`touchstart${Z}`,G=`touchmove${Z}`,J=`touchend${Z}`,ee=`pointerdown${Z}`,te=`pointerup${Z}`,ne={endCallback:null,leftCallback:null,rightCallback:null},re={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class ie extends ${constructor(e,t){super(),this._element=e,e&&ie.isSupported()&&(this._config=this._getConfig(t),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return ne}static get DefaultType(){return re}static get NAME(){return"swipe"}dispose(){z.off(this._element,Z)}_start(e){this._supportPointerEvents?this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX):this._deltaX=e.touches[0].clientX}_end(e){this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX-this._deltaX),this._handleSwipe(),g(this._config.endCallback)}_move(e){this._deltaX=e.touches&&e.touches.length>1?0:e.touches[0].clientX-this._deltaX}_handleSwipe(){const e=Math.abs(this._deltaX);if(e<=40)return;const t=e/this._deltaX;this._deltaX=0,t&&g(t>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(z.on(this._element,ee,(e=>this._start(e))),z.on(this._element,te,(e=>this._end(e))),this._element.classList.add("pointer-event")):(z.on(this._element,X,(e=>this._start(e))),z.on(this._element,G,(e=>this._move(e))),z.on(this._element,J,(e=>this._end(e))))}_eventIsPointerPenTouch(e){return this._supportPointerEvents&&("pen"===e.pointerType||"touch"===e.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const ae=".bs.carousel",oe=".data-api",le="ArrowLeft",se="ArrowRight",ue="next",ce="prev",de="left",fe="right",pe=`slide${ae}`,he=`slid${ae}`,me=`keydown${ae}`,ge=`mouseenter${ae}`,ve=`mouseleave${ae}`,be=`dragstart${ae}`,ye=`load${ae}${oe}`,_e=`click${ae}${oe}`,we="carousel",ke="active",xe=".active",Se=".carousel-item",Ee=xe+Se,Ce={[le]:fe,[se]:de},Te={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Le={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class Ae extends H{constructor(e,t){super(e,t),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=V.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===we&&this.cycle()}static get Default(){return Te}static get DefaultType(){return Le}static get NAME(){return"carousel"}next(){this._slide(ue)}nextWhenVisible(){!document.hidden&&l(this._element)&&this.next()}prev(){this._slide(ce)}pause(){this._isSliding&&i(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval((()=>this.nextWhenVisible()),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?z.one(this._element,he,(()=>this.cycle())):this.cycle())}to(e){const t=this._getItems();if(e>t.length-1||e<0)return;if(this._isSliding)return void z.one(this._element,he,(()=>this.to(e)));const n=this._getItemIndex(this._getActive());if(n===e)return;const r=e>n?ue:ce;this._slide(r,t[e])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(e){return e.defaultInterval=e.interval,e}_addEventListeners(){this._config.keyboard&&z.on(this._element,me,(e=>this._keydown(e))),"hover"===this._config.pause&&(z.on(this._element,ge,(()=>this.pause())),z.on(this._element,ve,(()=>this._maybeEnableCycle()))),this._config.touch&&ie.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of V.find(".carousel-item img",this._element))z.on(t,be,(e=>e.preventDefault()));const e={leftCallback:()=>this._slide(this._directionToOrder(de)),rightCallback:()=>this._slide(this._directionToOrder(fe)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout((()=>this._maybeEnableCycle()),500+this._config.interval))}};this._swipeHelper=new ie(this._element,e)}_keydown(e){if(/input|textarea/i.test(e.target.tagName))return;const t=Ce[e.key];t&&(e.preventDefault(),this._slide(this._directionToOrder(t)))}_getItemIndex(e){return this._getItems().indexOf(e)}_setActiveIndicatorElement(e){if(!this._indicatorsElement)return;const t=V.findOne(xe,this._indicatorsElement);t.classList.remove(ke),t.removeAttribute("aria-current");const n=V.findOne(`[data-bs-slide-to="${e}"]`,this._indicatorsElement);n&&(n.classList.add(ke),n.setAttribute("aria-current","true"))}_updateInterval(){const e=this._activeElement||this._getActive();if(!e)return;const t=Number.parseInt(e.getAttribute("data-bs-interval"),10);this._config.interval=t||this._config.defaultInterval}_slide(e,t=null){if(this._isSliding)return;const n=this._getActive(),r=e===ue,i=t||b(this._getItems(),n,r,this._config.wrap);if(i===n)return;const a=this._getItemIndex(i),o=t=>z.trigger(this._element,t,{relatedTarget:i,direction:this._orderToDirection(e),from:this._getItemIndex(n),to:a});if(o(pe).defaultPrevented)return;if(!n||!i)return;const l=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(a),this._activeElement=i;const s=r?"carousel-item-start":"carousel-item-end",u=r?"carousel-item-next":"carousel-item-prev";i.classList.add(u),d(i),n.classList.add(s),i.classList.add(s),this._queueCallback((()=>{i.classList.remove(s,u),i.classList.add(ke),n.classList.remove(ke,u,s),this._isSliding=!1,o(he)}),n,this._isAnimated()),l&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return V.findOne(Ee,this._element)}_getItems(){return V.find(Se,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(e){return h()?e===de?ce:ue:e===de?ue:ce}_orderToDirection(e){return h()?e===ce?de:fe:e===ce?fe:de}static jQueryInterface(e){return this.each((function(){const t=Ae.getOrCreateInstance(this,e);if("number"!=typeof e){if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}}else t.to(e)}))}}z.on(document,_e,"[data-bs-slide], [data-bs-slide-to]",(function(e){const t=V.getElementFromSelector(this);if(!t||!t.classList.contains(we))return;e.preventDefault();const n=Ae.getOrCreateInstance(t),r=this.getAttribute("data-bs-slide-to");return r?(n.to(r),void n._maybeEnableCycle()):"next"===F.getDataAttribute(this,"slide")?(n.next(),void n._maybeEnableCycle()):(n.prev(),void n._maybeEnableCycle())})),z.on(window,ye,(()=>{const e=V.find('[data-bs-ride="carousel"]');for(const t of e)Ae.getOrCreateInstance(t)})),m(Ae);const Ne=".bs.collapse",Pe=`show${Ne}`,Oe=`shown${Ne}`,je=`hide${Ne}`,ze=`hidden${Ne}`,Me=`click${Ne}.data-api`,De="show",Ie="collapse",Fe="collapsing",$e=`:scope .${Ie} .${Ie}`,He='[data-bs-toggle="collapse"]',Re={parent:null,toggle:!0},Ve={parent:"(null|element)",toggle:"boolean"};class Be extends H{constructor(e,t){super(e,t),this._isTransitioning=!1,this._triggerArray=[];const n=V.find(He);for(const r of n){const e=V.getSelectorFromElement(r),t=V.find(e).filter((e=>e===this._element));null!==e&&t.length&&this._triggerArray.push(r)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Re}static get DefaultType(){return Ve}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter((e=>e!==this._element)).map((e=>Be.getOrCreateInstance(e,{toggle:!1})))),e.length&&e[0]._isTransitioning)return;if(z.trigger(this._element,Pe).defaultPrevented)return;for(const r of e)r.hide();const t=this._getDimension();this._element.classList.remove(Ie),this._element.classList.add(Fe),this._element.style[t]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const n=`scroll${t[0].toUpperCase()+t.slice(1)}`;this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(Fe),this._element.classList.add(Ie,De),this._element.style[t]="",z.trigger(this._element,Oe)}),this._element,!0),this._element.style[t]=`${this._element[n]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(z.trigger(this._element,je).defaultPrevented)return;const e=this._getDimension();this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,d(this._element),this._element.classList.add(Fe),this._element.classList.remove(Ie,De);for(const t of this._triggerArray){const e=V.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0,this._element.style[e]="",this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(Fe),this._element.classList.add(Ie),z.trigger(this._element,ze)}),this._element,!0)}_isShown(e=this._element){return e.classList.contains(De)}_configAfterMerge(e){return e.toggle=Boolean(e.toggle),e.parent=o(e.parent),e}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const e=this._getFirstLevelChildren(He);for(const t of e){const e=V.getElementFromSelector(t);e&&this._addAriaAndCollapsedClass([t],this._isShown(e))}}_getFirstLevelChildren(e){const t=V.find($e,this._config.parent);return V.find(e,this._config.parent).filter((e=>!t.includes(e)))}_addAriaAndCollapsedClass(e,t){if(e.length)for(const n of e)n.classList.toggle("collapsed",!t),n.setAttribute("aria-expanded",t)}static jQueryInterface(e){const t={};return"string"==typeof e&&/show|hide/.test(e)&&(t.toggle=!1),this.each((function(){const n=Be.getOrCreateInstance(this,t);if("string"==typeof e){if(void 0===n[e])throw new TypeError(`No method named "${e}"`);n[e]()}}))}}z.on(document,Me,He,(function(e){("A"===e.target.tagName||e.delegateTarget&&"A"===e.delegateTarget.tagName)&&e.preventDefault();for(const t of V.getMultipleElementsFromSelector(this))Be.getOrCreateInstance(t,{toggle:!1}).toggle()})),m(Be);var Ue="top",We="bottom",qe="right",Qe="left",Ke="auto",Ye=[Ue,We,qe,Qe],Ze="start",Xe="end",Ge="clippingParents",Je="viewport",et="popper",tt="reference",nt=Ye.reduce((function(e,t){return e.concat([t+"-"+Ze,t+"-"+Xe])}),[]),rt=[].concat(Ye,[Ke]).reduce((function(e,t){return e.concat([t,t+"-"+Ze,t+"-"+Xe])}),[]),it="beforeRead",at="read",ot="afterRead",lt="beforeMain",st="main",ut="afterMain",ct="beforeWrite",dt="write",ft="afterWrite",pt=[it,at,ot,lt,st,ut,ct,dt,ft];function ht(e){return e?(e.nodeName||"").toLowerCase():null}function mt(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function gt(e){return e instanceof mt(e).Element||e instanceof Element}function vt(e){return e instanceof mt(e).HTMLElement||e instanceof HTMLElement}function bt(e){return"undefined"!=typeof ShadowRoot&&(e instanceof mt(e).ShadowRoot||e instanceof ShadowRoot)}const yt={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},i=t.elements[e];vt(i)&&ht(i)&&(Object.assign(i.style,n),Object.keys(r).forEach((function(e){var t=r[e];!1===t?i.removeAttribute(e):i.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var r=t.elements[e],i=t.attributes[e]||{},a=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});vt(r)&&ht(r)&&(Object.assign(r.style,a),Object.keys(i).forEach((function(e){r.removeAttribute(e)})))}))}},requires:["computeStyles"]};function _t(e){return e.split("-")[0]}var wt=Math.max,kt=Math.min,xt=Math.round;function St(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function Et(){return!/^((?!chrome|android).)*safari/i.test(St())}function Ct(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),i=1,a=1;t&&vt(e)&&(i=e.offsetWidth>0&&xt(r.width)/e.offsetWidth||1,a=e.offsetHeight>0&&xt(r.height)/e.offsetHeight||1);var o=(gt(e)?mt(e):window).visualViewport,l=!Et()&&n,s=(r.left+(l&&o?o.offsetLeft:0))/i,u=(r.top+(l&&o?o.offsetTop:0))/a,c=r.width/i,d=r.height/a;return{width:c,height:d,top:u,right:s+c,bottom:u+d,left:s,x:s,y:u}}function Tt(e){var t=Ct(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function Lt(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&bt(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function At(e){return mt(e).getComputedStyle(e)}function Nt(e){return["table","td","th"].indexOf(ht(e))>=0}function Pt(e){return((gt(e)?e.ownerDocument:e.document)||window.document).documentElement}function Ot(e){return"html"===ht(e)?e:e.assignedSlot||e.parentNode||(bt(e)?e.host:null)||Pt(e)}function jt(e){return vt(e)&&"fixed"!==At(e).position?e.offsetParent:null}function zt(e){for(var t=mt(e),n=jt(e);n&&Nt(n)&&"static"===At(n).position;)n=jt(n);return n&&("html"===ht(n)||"body"===ht(n)&&"static"===At(n).position)?t:n||function(e){var t=/firefox/i.test(St());if(/Trident/i.test(St())&&vt(e)&&"fixed"===At(e).position)return null;var n=Ot(e);for(bt(n)&&(n=n.host);vt(n)&&["html","body"].indexOf(ht(n))<0;){var r=At(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function Mt(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Dt(e,t,n){return wt(e,kt(t,n))}function It(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Ft(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}const $t={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n,r,i=e.state,a=e.name,o=e.options,l=i.elements.arrow,s=i.modifiersData.popperOffsets,u=_t(i.placement),c=Mt(u),d=[Qe,qe].indexOf(u)>=0?"height":"width";if(l&&s){var f=(n=o.padding,r=i,It("number"!=typeof(n="function"==typeof n?n(Object.assign({},r.rects,{placement:r.placement})):n)?n:Ft(n,Ye))),p=Tt(l),h="y"===c?Ue:Qe,m="y"===c?We:qe,g=i.rects.reference[d]+i.rects.reference[c]-s[c]-i.rects.popper[d],v=s[c]-i.rects.reference[c],b=zt(l),y=b?"y"===c?b.clientHeight||0:b.clientWidth||0:0,_=g/2-v/2,w=f[h],k=y-p[d]-f[m],x=y/2-p[d]/2+_,S=Dt(w,x,k),E=c;i.modifiersData[a]=((t={})[E]=S,t.centerOffset=S-x,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&Lt(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Ht(e){return e.split("-")[1]}var Rt={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Vt(e){var t,n=e.popper,r=e.popperRect,i=e.placement,a=e.variation,o=e.offsets,l=e.position,s=e.gpuAcceleration,u=e.adaptive,c=e.roundOffsets,d=e.isFixed,f=o.x,p=void 0===f?0:f,h=o.y,m=void 0===h?0:h,g="function"==typeof c?c({x:p,y:m}):{x:p,y:m};p=g.x,m=g.y;var v=o.hasOwnProperty("x"),b=o.hasOwnProperty("y"),y=Qe,_=Ue,w=window;if(u){var k=zt(n),x="clientHeight",S="clientWidth";k===mt(n)&&"static"!==At(k=Pt(n)).position&&"absolute"===l&&(x="scrollHeight",S="scrollWidth"),(i===Ue||(i===Qe||i===qe)&&a===Xe)&&(_=We,m-=(d&&k===w&&w.visualViewport?w.visualViewport.height:k[x])-r.height,m*=s?1:-1),i!==Qe&&(i!==Ue&&i!==We||a!==Xe)||(y=qe,p-=(d&&k===w&&w.visualViewport?w.visualViewport.width:k[S])-r.width,p*=s?1:-1)}var E,C,T,L,A,N,P=Object.assign({position:l},u&&Rt),O=!0===c?(C={x:p,y:m},T=mt(n),L=C.x,A=C.y,N=T.devicePixelRatio||1,{x:xt(L*N)/N||0,y:xt(A*N)/N||0}):{x:p,y:m};return p=O.x,m=O.y,s?Object.assign({},P,((E={})[_]=b?"0":"",E[y]=v?"0":"",E.transform=(w.devicePixelRatio||1)<=1?"translate("+p+"px, "+m+"px)":"translate3d("+p+"px, "+m+"px, 0)",E)):Object.assign({},P,((t={})[_]=b?m+"px":"",t[y]=v?p+"px":"",t.transform="",t))}const Bt={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,i=void 0===r||r,a=n.adaptive,o=void 0===a||a,l=n.roundOffsets,s=void 0===l||l,u={placement:_t(t.placement),variation:Ht(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:i,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,Vt(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:o,roundOffsets:s})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,Vt(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:s})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var Ut={passive:!0};const Wt={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,i=r.scroll,a=void 0===i||i,o=r.resize,l=void 0===o||o,s=mt(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&u.forEach((function(e){e.addEventListener("scroll",n.update,Ut)})),l&&s.addEventListener("resize",n.update,Ut),function(){a&&u.forEach((function(e){e.removeEventListener("scroll",n.update,Ut)})),l&&s.removeEventListener("resize",n.update,Ut)}},data:{}};var qt={left:"right",right:"left",bottom:"top",top:"bottom"};function Qt(e){return e.replace(/left|right|bottom|top/g,(function(e){return qt[e]}))}var Kt={start:"end",end:"start"};function Yt(e){return e.replace(/start|end/g,(function(e){return Kt[e]}))}function Zt(e){var t=mt(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function Xt(e){return Ct(Pt(e)).left+Zt(e).scrollLeft}function Gt(e){var t=At(e),n=t.overflow,r=t.overflowX,i=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+i+r)}function Jt(e){return["html","body","#document"].indexOf(ht(e))>=0?e.ownerDocument.body:vt(e)&&Gt(e)?e:Jt(Ot(e))}function en(e,t){var n;void 0===t&&(t=[]);var r=Jt(e),i=r===(null==(n=e.ownerDocument)?void 0:n.body),a=mt(r),o=i?[a].concat(a.visualViewport||[],Gt(r)?r:[]):r,l=t.concat(o);return i?l:l.concat(en(Ot(o)))}function tn(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function nn(e,t,n){return t===Je?tn(function(e,t){var n=mt(e),r=Pt(e),i=n.visualViewport,a=r.clientWidth,o=r.clientHeight,l=0,s=0;if(i){a=i.width,o=i.height;var u=Et();(u||!u&&"fixed"===t)&&(l=i.offsetLeft,s=i.offsetTop)}return{width:a,height:o,x:l+Xt(e),y:s}}(e,n)):gt(t)?((i=Ct(r=t,!1,"fixed"===n)).top=i.top+r.clientTop,i.left=i.left+r.clientLeft,i.bottom=i.top+r.clientHeight,i.right=i.left+r.clientWidth,i.width=r.clientWidth,i.height=r.clientHeight,i.x=i.left,i.y=i.top,i):tn(function(e){var t,n=Pt(e),r=Zt(e),i=null==(t=e.ownerDocument)?void 0:t.body,a=wt(n.scrollWidth,n.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),o=wt(n.scrollHeight,n.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),l=-r.scrollLeft+Xt(e),s=-r.scrollTop;return"rtl"===At(i||n).direction&&(l+=wt(n.clientWidth,i?i.clientWidth:0)-a),{width:a,height:o,x:l,y:s}}(Pt(e)));var r,i}function rn(e){var t,n=e.reference,r=e.element,i=e.placement,a=i?_t(i):null,o=i?Ht(i):null,l=n.x+n.width/2-r.width/2,s=n.y+n.height/2-r.height/2;switch(a){case Ue:t={x:l,y:n.y-r.height};break;case We:t={x:l,y:n.y+n.height};break;case qe:t={x:n.x+n.width,y:s};break;case Qe:t={x:n.x-r.width,y:s};break;default:t={x:n.x,y:n.y}}var u=a?Mt(a):null;if(null!=u){var c="y"===u?"height":"width";switch(o){case Ze:t[u]=t[u]-(n[c]/2-r[c]/2);break;case Xe:t[u]=t[u]+(n[c]/2-r[c]/2)}}return t}function an(e,t){void 0===t&&(t={});var n,r,i,a,o,l,s,u,c,d,f,p=t,h=p.placement,m=void 0===h?e.placement:h,g=p.strategy,v=void 0===g?e.strategy:g,b=p.boundary,y=void 0===b?Ge:b,_=p.rootBoundary,w=void 0===_?Je:_,k=p.elementContext,x=void 0===k?et:k,S=p.altBoundary,E=void 0!==S&&S,C=p.padding,T=void 0===C?0:C,L=It("number"!=typeof T?T:Ft(T,Ye)),A=x===et?tt:et,N=e.rects.popper,P=e.elements[E?A:x],O=(n=gt(P)?P:P.contextElement||Pt(e.elements.popper),i=w,a=v,u="clippingParents"===(r=y)?(l=en(Ot(o=n)),gt(s=["absolute","fixed"].indexOf(At(o).position)>=0&&vt(o)?zt(o):o)?l.filter((function(e){return gt(e)&&Lt(e,s)&&"body"!==ht(e)})):[]):[].concat(r),c=[].concat(u,[i]),d=c[0],f=c.reduce((function(e,t){var r=nn(n,t,a);return e.top=wt(r.top,e.top),e.right=kt(r.right,e.right),e.bottom=kt(r.bottom,e.bottom),e.left=wt(r.left,e.left),e}),nn(n,d,a)),f.width=f.right-f.left,f.height=f.bottom-f.top,f.x=f.left,f.y=f.top,f),j=Ct(e.elements.reference),z=rn({reference:j,element:N,placement:m}),M=tn(Object.assign({},N,z)),D=x===et?M:j,I={top:O.top-D.top+L.top,bottom:D.bottom-O.bottom+L.bottom,left:O.left-D.left+L.left,right:D.right-O.right+L.right},F=e.modifiersData.offset;if(x===et&&F){var $=F[m];Object.keys(I).forEach((function(e){var t=[qe,We].indexOf(e)>=0?1:-1,n=[Ue,We].indexOf(e)>=0?"y":"x";I[e]+=$[n]*t}))}return I}function on(e,t){void 0===t&&(t={});var n=t,r=n.placement,i=n.boundary,a=n.rootBoundary,o=n.padding,l=n.flipVariations,s=n.allowedAutoPlacements,u=void 0===s?rt:s,c=Ht(r),d=c?l?nt:nt.filter((function(e){return Ht(e)===c})):Ye,f=d.filter((function(e){return u.indexOf(e)>=0}));0===f.length&&(f=d);var p=f.reduce((function(t,n){return t[n]=an(e,{placement:n,boundary:i,rootBoundary:a,padding:o})[_t(n)],t}),{});return Object.keys(p).sort((function(e,t){return p[e]-p[t]}))}const ln={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var i=n.mainAxis,a=void 0===i||i,o=n.altAxis,l=void 0===o||o,s=n.fallbackPlacements,u=n.padding,c=n.boundary,d=n.rootBoundary,f=n.altBoundary,p=n.flipVariations,h=void 0===p||p,m=n.allowedAutoPlacements,g=t.options.placement,v=_t(g),b=s||(v!==g&&h?function(e){if(_t(e)===Ke)return[];var t=Qt(e);return[Yt(e),t,Yt(t)]}(g):[Qt(g)]),y=[g].concat(b).reduce((function(e,n){return e.concat(_t(n)===Ke?on(t,{placement:n,boundary:c,rootBoundary:d,padding:u,flipVariations:h,allowedAutoPlacements:m}):n)}),[]),_=t.rects.reference,w=t.rects.popper,k=new Map,x=!0,S=y[0],E=0;E<y.length;E++){var C=y[E],T=_t(C),L=Ht(C)===Ze,A=[Ue,We].indexOf(T)>=0,N=A?"width":"height",P=an(t,{placement:C,boundary:c,rootBoundary:d,altBoundary:f,padding:u}),O=A?L?qe:Qe:L?We:Ue;_[N]>w[N]&&(O=Qt(O));var j=Qt(O),z=[];if(a&&z.push(P[T]<=0),l&&z.push(P[O]<=0,P[j]<=0),z.every((function(e){return e}))){S=C,x=!1;break}k.set(C,z)}if(x)for(var M=function(e){var t=y.find((function(t){var n=k.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return S=t,"break"},D=h?3:1;D>0&&"break"!==M(D);D--);t.placement!==S&&(t.modifiersData[r]._skip=!0,t.placement=S,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function sn(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function un(e){return[Ue,qe,We,Qe].some((function(t){return e[t]>=0}))}const cn={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,i=t.rects.popper,a=t.modifiersData.preventOverflow,o=an(t,{elementContext:"reference"}),l=an(t,{altBoundary:!0}),s=sn(o,r),u=sn(l,i,a),c=un(s),d=un(u);t.modifiersData[n]={referenceClippingOffsets:s,popperEscapeOffsets:u,isReferenceHidden:c,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":d})}},dn={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,i=n.offset,a=void 0===i?[0,0]:i,o=rt.reduce((function(e,n){return e[n]=(r=n,i=t.rects,o=a,l=_t(r),s=[Qe,Ue].indexOf(l)>=0?-1:1,u="function"==typeof o?o(Object.assign({},i,{placement:r})):o,c=u[0],d=u[1],c=c||0,d=(d||0)*s,[Qe,qe].indexOf(l)>=0?{x:d,y:c}:{x:c,y:d}),e;var r,i,o,l,s,u,c,d}),{}),l=o[t.placement],s=l.x,u=l.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=s,t.modifiersData.popperOffsets.y+=u),t.modifiersData[r]=o}},fn={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=rn({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})},data:{}},pn={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t,n,r=e.state,i=e.options,a=e.name,o=i.mainAxis,l=void 0===o||o,s=i.altAxis,u=void 0!==s&&s,c=i.boundary,d=i.rootBoundary,f=i.altBoundary,p=i.padding,h=i.tether,m=void 0===h||h,g=i.tetherOffset,v=void 0===g?0:g,b=an(r,{boundary:c,rootBoundary:d,padding:p,altBoundary:f}),y=_t(r.placement),_=Ht(r.placement),w=!_,k=Mt(y),x="x"===k?"y":"x",S=r.modifiersData.popperOffsets,E=r.rects.reference,C=r.rects.popper,T="function"==typeof v?v(Object.assign({},r.rects,{placement:r.placement})):v,L="number"==typeof T?{mainAxis:T,altAxis:T}:Object.assign({mainAxis:0,altAxis:0},T),A=r.modifiersData.offset?r.modifiersData.offset[r.placement]:null,N={x:0,y:0};if(S){if(l){var P,O="y"===k?Ue:Qe,j="y"===k?We:qe,z="y"===k?"height":"width",M=S[k],D=M+b[O],I=M-b[j],F=m?-C[z]/2:0,$=_===Ze?E[z]:C[z],H=_===Ze?-C[z]:-E[z],R=r.elements.arrow,V=m&&R?Tt(R):{width:0,height:0},B=r.modifiersData["arrow#persistent"]?r.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},U=B[O],W=B[j],q=Dt(0,E[z],V[z]),Q=w?E[z]/2-F-q-U-L.mainAxis:$-q-U-L.mainAxis,K=w?-E[z]/2+F+q+W+L.mainAxis:H+q+W+L.mainAxis,Y=r.elements.arrow&&zt(r.elements.arrow),Z=Y?"y"===k?Y.clientTop||0:Y.clientLeft||0:0,X=null!=(P=null==A?void 0:A[k])?P:0,G=M+K-X,J=Dt(m?kt(D,M+Q-X-Z):D,M,m?wt(I,G):I);S[k]=J,N[k]=J-M}if(u){var ee,te="x"===k?Ue:Qe,ne="x"===k?We:qe,re=S[x],ie="y"===x?"height":"width",ae=re+b[te],oe=re-b[ne],le=-1!==[Ue,Qe].indexOf(y),se=null!=(ee=null==A?void 0:A[x])?ee:0,ue=le?ae:re-E[ie]-C[ie]-se+L.altAxis,ce=le?re+E[ie]+C[ie]-se-L.altAxis:oe,de=m&&le?(n=Dt(ue,re,t=ce))>t?t:n:Dt(m?ue:ae,re,m?ce:oe);S[x]=de,N[x]=de-re}r.modifiersData[a]=N}},requiresIfExists:["offset"]};function hn(e,t,n){void 0===n&&(n=!1);var r,i,a,o,l,s,u=vt(t),c=vt(t)&&(o=(a=t).getBoundingClientRect(),l=xt(o.width)/a.offsetWidth||1,s=xt(o.height)/a.offsetHeight||1,1!==l||1!==s),d=Pt(t),f=Ct(e,c,n),p={scrollLeft:0,scrollTop:0},h={x:0,y:0};return(u||!u&&!n)&&(("body"!==ht(t)||Gt(d))&&(p=(r=t)!==mt(r)&&vt(r)?{scrollLeft:(i=r).scrollLeft,scrollTop:i.scrollTop}:Zt(r)),vt(t)?((h=Ct(t,!0)).x+=t.clientLeft,h.y+=t.clientTop):d&&(h.x=Xt(d))),{x:f.left+p.scrollLeft-h.x,y:f.top+p.scrollTop-h.y,width:f.width,height:f.height}}function mn(e){var t=new Map,n=new Set,r=[];function i(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&i(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||i(e)})),r}var gn={placement:"bottom",modifiers:[],strategy:"absolute"};function vn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function bn(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,i=t.defaultOptions,a=void 0===i?gn:i;return function(e,t,n){void 0===n&&(n=a);var i,o,l={placement:"bottom",orderedModifiers:[],options:Object.assign({},gn,a),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},s=[],u=!1,c={state:l,setOptions:function(n){var i="function"==typeof n?n(l.options):n;d(),l.options=Object.assign({},a,l.options,i),l.scrollParents={reference:gt(e)?en(e):e.contextElement?en(e.contextElement):[],popper:en(t)};var o,u,f,p,h=(o=[].concat(r,l.options.modifiers),u=o.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),f=Object.keys(u).map((function(e){return u[e]})),p=mn(f),pt.reduce((function(e,t){return e.concat(p.filter((function(e){return e.phase===t})))}),[]));return l.orderedModifiers=h.filter((function(e){return e.enabled})),l.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,i=e.effect;if("function"==typeof i){var a=i({state:l,name:t,instance:c,options:r});s.push(a||function(){})}})),c.update()},forceUpdate:function(){if(!u){var e=l.elements,t=e.reference,n=e.popper;if(vn(t,n)){l.rects={reference:hn(t,zt(n),"fixed"===l.options.strategy),popper:Tt(n)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach((function(e){return l.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0;r<l.orderedModifiers.length;r++)if(!0!==l.reset){var i=l.orderedModifiers[r],a=i.fn,o=i.options,s=void 0===o?{}:o,d=i.name;"function"==typeof a&&(l=a({state:l,options:s,name:d,instance:c})||l)}else l.reset=!1,r=-1}}},update:(i=function(){return new Promise((function(e){c.forceUpdate(),e(l)}))},function(){return o||(o=new Promise((function(e){Promise.resolve().then((function(){o=void 0,e(i())}))}))),o}),destroy:function(){d(),u=!0}};if(!vn(e,t))return c;function d(){s.forEach((function(e){return e()})),s=[]}return c.setOptions(n).then((function(e){!u&&n.onFirstUpdate&&n.onFirstUpdate(e)})),c}}var yn=bn(),_n=bn({defaultModifiers:[Wt,fn,Bt,yt]}),wn=bn({defaultModifiers:[Wt,fn,Bt,yt,dn,ln,pn,$t,cn]});const kn=Object.freeze(Object.defineProperty({__proto__:null,afterMain:ut,afterRead:ot,afterWrite:ft,applyStyles:yt,arrow:$t,auto:Ke,basePlacements:Ye,beforeMain:lt,beforeRead:it,beforeWrite:ct,bottom:We,clippingParents:Ge,computeStyles:Bt,createPopper:wn,createPopperBase:yn,createPopperLite:_n,detectOverflow:an,end:Xe,eventListeners:Wt,flip:ln,hide:cn,left:Qe,main:st,modifierPhases:pt,offset:dn,placements:rt,popper:et,popperGenerator:bn,popperOffsets:fn,preventOverflow:pn,read:at,reference:tt,right:qe,start:Ze,top:Ue,variationPlacements:nt,viewport:Je,write:dt},Symbol.toStringTag,{value:"Module"})),xn="dropdown",Sn=".bs.dropdown",En=".data-api",Cn="ArrowUp",Tn="ArrowDown",Ln=`hide${Sn}`,An=`hidden${Sn}`,Nn=`show${Sn}`,Pn=`shown${Sn}`,On=`click${Sn}${En}`,jn=`keydown${Sn}${En}`,zn=`keyup${Sn}${En}`,Mn="show",Dn='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',In=`${Dn}.${Mn}`,Fn=".dropdown-menu",$n=h()?"top-end":"top-start",Hn=h()?"top-start":"top-end",Rn=h()?"bottom-end":"bottom-start",Vn=h()?"bottom-start":"bottom-end",Bn=h()?"left-start":"right-start",Un=h()?"right-start":"left-start",Wn={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},qn={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class Qn extends H{constructor(e,t){super(e,t),this._popper=null,this._parent=this._element.parentNode,this._menu=V.next(this._element,Fn)[0]||V.prev(this._element,Fn)[0]||V.findOne(Fn,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Wn}static get DefaultType(){return qn}static get NAME(){return xn}toggle(){return this._isShown()?this.hide():this.show()}show(){if(s(this._element)||this._isShown())return;const e={relatedTarget:this._element};if(!z.trigger(this._element,Nn,e).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const e of[].concat(...document.body.children))z.on(e,"mouseover",c);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Mn),this._element.classList.add(Mn),z.trigger(this._element,Pn,e)}}hide(){if(s(this._element)||!this._isShown())return;const e={relatedTarget:this._element};this._completeHide(e)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(e){if(!z.trigger(this._element,Ln,e).defaultPrevented){if("ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))z.off(e,"mouseover",c);this._popper&&this._popper.destroy(),this._menu.classList.remove(Mn),this._element.classList.remove(Mn),this._element.setAttribute("aria-expanded","false"),F.removeDataAttribute(this._menu,"popper"),z.trigger(this._element,An,e),this._element.focus()}}_getConfig(e){if("object"==typeof(e=super._getConfig(e)).reference&&!a(e.reference)&&"function"!=typeof e.reference.getBoundingClientRect)throw new TypeError(`${xn.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return e}_createPopper(){if(void 0===kn)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org/docs/v2/)");let e=this._element;"parent"===this._config.reference?e=this._parent:a(this._config.reference)?e=o(this._config.reference):"object"==typeof this._config.reference&&(e=this._config.reference);const t=this._getPopperConfig();this._popper=wn(e,this._menu,t)}_isShown(){return this._menu.classList.contains(Mn)}_getPlacement(){const e=this._parent;if(e.classList.contains("dropend"))return Bn;if(e.classList.contains("dropstart"))return Un;if(e.classList.contains("dropup-center"))return"top";if(e.classList.contains("dropdown-center"))return"bottom";const t="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return e.classList.contains("dropup")?t?Hn:$n:t?Vn:Rn}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:e}=this._config;return"string"==typeof e?e.split(",").map((e=>Number.parseInt(e,10))):"function"==typeof e?t=>e(t,this._element):e}_getPopperConfig(){const e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(F.setDataAttribute(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),{...e,...g(this._config.popperConfig,[void 0,e])}}_selectMenuItem({key:e,target:t}){const n=V.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter((e=>l(e)));n.length&&b(n,t,e===Tn,!n.includes(t)).focus()}static jQueryInterface(e){return this.each((function(){const t=Qn.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}}))}static clearMenus(e){if(2===e.button||"keyup"===e.type&&"Tab"!==e.key)return;const t=V.find(In);for(const n of t){const t=Qn.getInstance(n);if(!t||!1===t._config.autoClose)continue;const r=e.composedPath(),i=r.includes(t._menu);if(r.includes(t._element)||"inside"===t._config.autoClose&&!i||"outside"===t._config.autoClose&&i)continue;if(t._menu.contains(e.target)&&("keyup"===e.type&&"Tab"===e.key||/input|select|option|textarea|form/i.test(e.target.tagName)))continue;const a={relatedTarget:t._element};"click"===e.type&&(a.clickEvent=e),t._completeHide(a)}}static dataApiKeydownHandler(e){const t=/input|textarea/i.test(e.target.tagName),n="Escape"===e.key,r=[Cn,Tn].includes(e.key);if(!r&&!n)return;if(t&&!n)return;e.preventDefault();const i=this.matches(Dn)?this:V.prev(this,Dn)[0]||V.next(this,Dn)[0]||V.findOne(Dn,e.delegateTarget.parentNode),a=Qn.getOrCreateInstance(i);if(r)return e.stopPropagation(),a.show(),void a._selectMenuItem(e);a._isShown()&&(e.stopPropagation(),a.hide(),i.focus())}}z.on(document,jn,Dn,Qn.dataApiKeydownHandler),z.on(document,jn,Fn,Qn.dataApiKeydownHandler),z.on(document,On,Qn.clearMenus),z.on(document,zn,Qn.clearMenus),z.on(document,On,Dn,(function(e){e.preventDefault(),Qn.getOrCreateInstance(this).toggle()})),m(Qn);const Kn="backdrop",Yn="show",Zn=`mousedown.bs.${Kn}`,Xn={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},Gn={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class Jn extends ${constructor(e){super(),this._config=this._getConfig(e),this._isAppended=!1,this._element=null}static get Default(){return Xn}static get DefaultType(){return Gn}static get NAME(){return Kn}show(e){if(!this._config.isVisible)return void g(e);this._append();const t=this._getElement();this._config.isAnimated&&d(t),t.classList.add(Yn),this._emulateAnimation((()=>{g(e)}))}hide(e){this._config.isVisible?(this._getElement().classList.remove(Yn),this._emulateAnimation((()=>{this.dispose(),g(e)}))):g(e)}dispose(){this._isAppended&&(z.off(this._element,Zn),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e}return this._element}_configAfterMerge(e){return e.rootElement=o(e.rootElement),e}_append(){if(this._isAppended)return;const e=this._getElement();this._config.rootElement.append(e),z.on(e,Zn,(()=>{g(this._config.clickCallback)})),this._isAppended=!0}_emulateAnimation(e){v(e,this._getElement(),this._config.isAnimated)}}const er=".bs.focustrap",tr=`focusin${er}`,nr=`keydown.tab${er}`,rr="backward",ir={autofocus:!0,trapElement:null},ar={autofocus:"boolean",trapElement:"element"};class or extends ${constructor(e){super(),this._config=this._getConfig(e),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return ir}static get DefaultType(){return ar}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),z.off(document,er),z.on(document,tr,(e=>this._handleFocusin(e))),z.on(document,nr,(e=>this._handleKeydown(e))),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,z.off(document,er))}_handleFocusin(e){const{trapElement:t}=this._config;if(e.target===document||e.target===t||t.contains(e.target))return;const n=V.focusableChildren(t);0===n.length?t.focus():this._lastTabNavDirection===rr?n[n.length-1].focus():n[0].focus()}_handleKeydown(e){"Tab"===e.key&&(this._lastTabNavDirection=e.shiftKey?rr:"forward")}}const lr=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",sr=".sticky-top",ur="padding-right",cr="margin-right";class dr{constructor(){this._element=document.body}getWidth(){const e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){const e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,ur,(t=>t+e)),this._setElementAttributes(lr,ur,(t=>t+e)),this._setElementAttributes(sr,cr,(t=>t-e))}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,ur),this._resetElementAttributes(lr,ur),this._resetElementAttributes(sr,cr)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,t,n){const r=this.getWidth();this._applyManipulationCallback(e,(e=>{if(e!==this._element&&window.innerWidth>e.clientWidth+r)return;this._saveInitialAttribute(e,t);const i=window.getComputedStyle(e).getPropertyValue(t);e.style.setProperty(t,`${n(Number.parseFloat(i))}px`)}))}_saveInitialAttribute(e,t){const n=e.style.getPropertyValue(t);n&&F.setDataAttribute(e,t,n)}_resetElementAttributes(e,t){this._applyManipulationCallback(e,(e=>{const n=F.getDataAttribute(e,t);null!==n?(F.removeDataAttribute(e,t),e.style.setProperty(t,n)):e.style.removeProperty(t)}))}_applyManipulationCallback(e,t){if(a(e))t(e);else for(const n of V.find(e,this._element))t(n)}}const fr=".bs.modal",pr=`hide${fr}`,hr=`hidePrevented${fr}`,mr=`hidden${fr}`,gr=`show${fr}`,vr=`shown${fr}`,br=`resize${fr}`,yr=`click.dismiss${fr}`,_r=`mousedown.dismiss${fr}`,wr=`keydown.dismiss${fr}`,kr=`click${fr}.data-api`,xr="modal-open",Sr="show",Er="modal-static",Cr={backdrop:!0,focus:!0,keyboard:!0},Tr={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Lr extends H{constructor(e,t){super(e,t),this._dialog=V.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new dr,this._addEventListeners()}static get Default(){return Cr}static get DefaultType(){return Tr}static get NAME(){return"modal"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||this._isTransitioning||z.trigger(this._element,gr,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(xr),this._adjustDialog(),this._backdrop.show((()=>this._showElement(e))))}hide(){this._isShown&&!this._isTransitioning&&(z.trigger(this._element,pr).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Sr),this._queueCallback((()=>this._hideModal()),this._element,this._isAnimated())))}dispose(){z.off(window,fr),z.off(this._dialog,fr),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Jn({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new or({trapElement:this._element})}_showElement(e){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const t=V.findOne(".modal-body",this._dialog);t&&(t.scrollTop=0),d(this._element),this._element.classList.add(Sr),this._queueCallback((()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,z.trigger(this._element,vr,{relatedTarget:e})}),this._dialog,this._isAnimated())}_addEventListeners(){z.on(this._element,wr,(e=>{"Escape"===e.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())})),z.on(window,br,(()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()})),z.on(this._element,_r,(e=>{z.one(this._element,yr,(t=>{this._element===e.target&&this._element===t.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())}))}))}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide((()=>{document.body.classList.remove(xr),this._resetAdjustments(),this._scrollBar.reset(),z.trigger(this._element,mr)}))}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(z.trigger(this._element,hr).defaultPrevented)return;const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._element.style.overflowY;"hidden"===t||this._element.classList.contains(Er)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(Er),this._queueCallback((()=>{this._element.classList.remove(Er),this._queueCallback((()=>{this._element.style.overflowY=t}),this._dialog)}),this._dialog),this._element.focus())}_adjustDialog(){const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),n=t>0;if(n&&!e){const e=h()?"paddingLeft":"paddingRight";this._element.style[e]=`${t}px`}if(!n&&e){const e=h()?"paddingRight":"paddingLeft";this._element.style[e]=`${t}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,t){return this.each((function(){const n=Lr.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===n[e])throw new TypeError(`No method named "${e}"`);n[e](t)}}))}}z.on(document,kr,'[data-bs-toggle="modal"]',(function(e){const t=V.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),z.one(t,gr,(e=>{e.defaultPrevented||z.one(t,mr,(()=>{l(this)&&this.focus()}))}));const n=V.findOne(".modal.show");n&&Lr.getInstance(n).hide(),Lr.getOrCreateInstance(t).toggle(this)})),B(Lr),m(Lr);const Ar=".bs.offcanvas",Nr=".data-api",Pr=`load${Ar}${Nr}`,Or="show",jr="showing",zr="hiding",Mr=".offcanvas.show",Dr=`show${Ar}`,Ir=`shown${Ar}`,Fr=`hide${Ar}`,$r=`hidePrevented${Ar}`,Hr=`hidden${Ar}`,Rr=`resize${Ar}`,Vr=`click${Ar}${Nr}`,Br=`keydown.dismiss${Ar}`,Ur={backdrop:!0,keyboard:!0,scroll:!1},Wr={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class qr extends H{constructor(e,t){super(e,t),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Ur}static get DefaultType(){return Wr}static get NAME(){return"offcanvas"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||z.trigger(this._element,Dr,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||(new dr).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(jr),this._queueCallback((()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(Or),this._element.classList.remove(jr),z.trigger(this._element,Ir,{relatedTarget:e})}),this._element,!0))}hide(){this._isShown&&(z.trigger(this._element,Fr).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(zr),this._backdrop.hide(),this._queueCallback((()=>{this._element.classList.remove(Or,zr),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new dr).reset(),z.trigger(this._element,Hr)}),this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const e=Boolean(this._config.backdrop);return new Jn({className:"offcanvas-backdrop",isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?()=>{"static"!==this._config.backdrop?this.hide():z.trigger(this._element,$r)}:null})}_initializeFocusTrap(){return new or({trapElement:this._element})}_addEventListeners(){z.on(this._element,Br,(e=>{"Escape"===e.key&&(this._config.keyboard?this.hide():z.trigger(this._element,$r))}))}static jQueryInterface(e){return this.each((function(){const t=qr.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}}))}}z.on(document,Vr,'[data-bs-toggle="offcanvas"]',(function(e){const t=V.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&e.preventDefault(),s(this))return;z.one(t,Hr,(()=>{l(this)&&this.focus()}));const n=V.findOne(Mr);n&&n!==t&&qr.getInstance(n).hide(),qr.getOrCreateInstance(t).toggle(this)})),z.on(window,Pr,(()=>{for(const e of V.find(Mr))qr.getOrCreateInstance(e).show()})),z.on(window,Rr,(()=>{for(const e of V.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(e).position&&qr.getOrCreateInstance(e).hide()})),B(qr),m(qr);const Qr={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Kr=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Yr=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,Zr=(e,t)=>{const n=e.nodeName.toLowerCase();return t.includes(n)?!Kr.has(n)||Boolean(Yr.test(e.nodeValue)):t.filter((e=>e instanceof RegExp)).some((e=>e.test(n)))},Xr={allowList:Qr,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},Gr={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Jr={entry:"(string|element|function|null)",selector:"(string|element)"};class ei extends ${constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return Xr}static get DefaultType(){return Gr}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map((e=>this._resolvePossibleFunction(e))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){const e=document.createElement("div");e.innerHTML=this._maybeSanitize(this._config.template);for(const[r,i]of Object.entries(this._config.content))this._setContent(e,i,r);const t=e.children[0],n=this._resolvePossibleFunction(this._config.extraClass);return n&&t.classList.add(...n.split(" ")),t}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(const[t,n]of Object.entries(e))super._typeCheckConfig({selector:t,entry:n},Jr)}_setContent(e,t,n){const r=V.findOne(n,e);r&&((t=this._resolvePossibleFunction(t))?a(t)?this._putElementInTemplate(o(t),r):this._config.html?r.innerHTML=this._maybeSanitize(t):r.textContent=t:r.remove())}_maybeSanitize(e){return this._config.sanitize?function(e,t,n){if(!e.length)return e;if(n&&"function"==typeof n)return n(e);const r=(new window.DOMParser).parseFromString(e,"text/html"),i=[].concat(...r.body.querySelectorAll("*"));for(const a of i){const e=a.nodeName.toLowerCase();if(!Object.keys(t).includes(e)){a.remove();continue}const n=[].concat(...a.attributes),r=[].concat(t["*"]||[],t[e]||[]);for(const t of n)Zr(t,r)||a.removeAttribute(t.nodeName)}return r.body.innerHTML}(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return g(e,[void 0,this])}_putElementInTemplate(e,t){if(this._config.html)return t.innerHTML="",void t.append(e);t.textContent=e.textContent}}const ti=new Set(["sanitize","allowList","sanitizeFn"]),ni="fade",ri="show",ii=".tooltip-inner",ai=".modal",oi="hide.bs.modal",li="hover",si="focus",ui={AUTO:"auto",TOP:"top",RIGHT:h()?"left":"right",BOTTOM:"bottom",LEFT:h()?"right":"left"},ci={allowList:Qr,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},di={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class fi extends H{constructor(e,t){if(void 0===kn)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(e,t),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return ci}static get DefaultType(){return di}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),z.off(this._element.closest(ai),oi,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const e=z.trigger(this._element,this.constructor.eventName("show")),t=(u(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!t)return;this._disposePopper();const n=this._getTipElement();this._element.setAttribute("aria-describedby",n.getAttribute("id"));const{container:r}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(r.append(n),z.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(n),n.classList.add(ri),"ontouchstart"in document.documentElement)for(const i of[].concat(...document.body.children))z.on(i,"mouseover",c);this._queueCallback((()=>{z.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1}),this.tip,this._isAnimated())}hide(){if(this._isShown()&&!z.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(ri),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))z.off(e,"mouseover",c);this._activeTrigger.click=!1,this._activeTrigger[si]=!1,this._activeTrigger[li]=!1,this._isHovered=null,this._queueCallback((()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),z.trigger(this._element,this.constructor.eventName("hidden")))}),this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){const t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove(ni,ri),t.classList.add(`bs-${this.constructor.NAME}-auto`);const n=(e=>{do{e+=Math.floor(1e6*Math.random())}while(document.getElementById(e));return e})(this.constructor.NAME).toString();return t.setAttribute("id",n),this._isAnimated()&&t.classList.add(ni),t}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new ei({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[ii]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(ni)}_isShown(){return this.tip&&this.tip.classList.contains(ri)}_createPopper(e){const t=g(this._config.placement,[this,e,this._element]),n=ui[t.toUpperCase()];return wn(this._element,e,this._getPopperConfig(n))}_getOffset(){const{offset:e}=this._config;return"string"==typeof e?e.split(",").map((e=>Number.parseInt(e,10))):"function"==typeof e?t=>e(t,this._element):e}_resolvePossibleFunction(e){return g(e,[this._element,this._element])}_getPopperConfig(e){const t={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:e=>{this._getTipElement().setAttribute("data-popper-placement",e.state.placement)}}]};return{...t,...g(this._config.popperConfig,[void 0,t])}}_setListeners(){const e=this._config.trigger.split(" ");for(const t of e)if("click"===t)z.on(this._element,this.constructor.eventName("click"),this._config.selector,(e=>{this._initializeOnDelegatedTarget(e).toggle()}));else if("manual"!==t){const e=t===li?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),n=t===li?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");z.on(this._element,e,this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusin"===e.type?si:li]=!0,t._enter()})),z.on(this._element,n,this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusout"===e.type?si:li]=t._element.contains(e.relatedTarget),t._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},z.on(this._element.closest(ai),oi,this._hideModalHandler)}_fixTitle(){const e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){const t=F.getDataAttributes(this._element);for(const n of Object.keys(t))ti.has(n)&&delete t[n];return e={...t,..."object"==typeof e&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=!1===e.container?document.body:o(e.container),"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),e}_getDelegateConfig(){const e={};for(const[t,n]of Object.entries(this._config))this.constructor.Default[t]!==n&&(e[t]=n);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each((function(){const t=fi.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}}))}}m(fi);const pi=".popover-header",hi=".popover-body",mi={...fi.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},gi={...fi.DefaultType,content:"(null|string|element|function)"};class vi extends fi{static get Default(){return mi}static get DefaultType(){return gi}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[pi]:this._getTitle(),[hi]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(e){return this.each((function(){const t=vi.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}}))}}m(vi);const bi=".bs.scrollspy",yi=`activate${bi}`,_i=`click${bi}`,wi=`load${bi}.data-api`,ki="active",xi="[href]",Si=".nav-link",Ei=`${Si}, .nav-item > ${Si}, .list-group-item`,Ci={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Ti={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Li extends H{constructor(e,t){super(e,t),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Ci}static get DefaultType(){return Ti}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const e of this._observableSections.values())this._observer.observe(e)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(e){return e.target=o(e.target)||document.body,e.rootMargin=e.offset?`${e.offset}px 0px -30%`:e.rootMargin,"string"==typeof e.threshold&&(e.threshold=e.threshold.split(",").map((e=>Number.parseFloat(e)))),e}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(z.off(this._config.target,_i),z.on(this._config.target,_i,xi,(e=>{const t=this._observableSections.get(e.target.hash);if(t){e.preventDefault();const n=this._rootElement||window,r=t.offsetTop-this._element.offsetTop;if(n.scrollTo)return void n.scrollTo({top:r,behavior:"smooth"});n.scrollTop=r}})))}_getNewObserver(){const e={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver((e=>this._observerCallback(e)),e)}_observerCallback(e){const t=e=>this._targetLinks.get(`#${e.target.id}`),n=e=>{this._previousScrollData.visibleEntryTop=e.target.offsetTop,this._process(t(e))},r=(this._rootElement||document.documentElement).scrollTop,i=r>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=r;for(const a of e){if(!a.isIntersecting){this._activeTarget=null,this._clearActiveClass(t(a));continue}const e=a.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(i&&e){if(n(a),!r)return}else i||e||n(a)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const e=V.find(xi,this._config.target);for(const t of e){if(!t.hash||s(t))continue;const e=V.findOne(decodeURI(t.hash),this._element);l(e)&&(this._targetLinks.set(decodeURI(t.hash),t),this._observableSections.set(t.hash,e))}}_process(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),this._activeTarget=e,e.classList.add(ki),this._activateParents(e),z.trigger(this._element,yi,{relatedTarget:e}))}_activateParents(e){if(e.classList.contains("dropdown-item"))V.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add(ki);else for(const t of V.parents(e,".nav, .list-group"))for(const e of V.prev(t,Ei))e.classList.add(ki)}_clearActiveClass(e){e.classList.remove(ki);const t=V.find(`${xi}.${ki}`,e);for(const n of t)n.classList.remove(ki)}static jQueryInterface(e){return this.each((function(){const t=Li.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}}))}}z.on(window,wi,(()=>{for(const e of V.find('[data-bs-spy="scroll"]'))Li.getOrCreateInstance(e)})),m(Li);const Ai=".bs.tab",Ni=`hide${Ai}`,Pi=`hidden${Ai}`,Oi=`show${Ai}`,ji=`shown${Ai}`,zi=`click${Ai}`,Mi=`keydown${Ai}`,Di=`load${Ai}`,Ii="ArrowLeft",Fi="ArrowRight",$i="ArrowUp",Hi="ArrowDown",Ri="Home",Vi="End",Bi="active",Ui="fade",Wi="show",qi=".dropdown-toggle",Qi=`:not(${qi})`,Ki='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',Yi=`.nav-link${Qi}, .list-group-item${Qi}, [role="tab"]${Qi}, ${Ki}`,Zi=`.${Bi}[data-bs-toggle="tab"], .${Bi}[data-bs-toggle="pill"], .${Bi}[data-bs-toggle="list"]`;class Xi extends H{constructor(e){super(e),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),z.on(this._element,Mi,(e=>this._keydown(e))))}static get NAME(){return"tab"}show(){const e=this._element;if(this._elemIsActive(e))return;const t=this._getActiveElem(),n=t?z.trigger(t,Ni,{relatedTarget:e}):null;z.trigger(e,Oi,{relatedTarget:t}).defaultPrevented||n&&n.defaultPrevented||(this._deactivate(t,e),this._activate(e,t))}_activate(e,t){e&&(e.classList.add(Bi),this._activate(V.getElementFromSelector(e)),this._queueCallback((()=>{"tab"===e.getAttribute("role")?(e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),this._toggleDropDown(e,!0),z.trigger(e,ji,{relatedTarget:t})):e.classList.add(Wi)}),e,e.classList.contains(Ui)))}_deactivate(e,t){e&&(e.classList.remove(Bi),e.blur(),this._deactivate(V.getElementFromSelector(e)),this._queueCallback((()=>{"tab"===e.getAttribute("role")?(e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),this._toggleDropDown(e,!1),z.trigger(e,Pi,{relatedTarget:t})):e.classList.remove(Wi)}),e,e.classList.contains(Ui)))}_keydown(e){if(![Ii,Fi,$i,Hi,Ri,Vi].includes(e.key))return;e.stopPropagation(),e.preventDefault();const t=this._getChildren().filter((e=>!s(e)));let n;if([Ri,Vi].includes(e.key))n=t[e.key===Ri?0:t.length-1];else{const r=[Fi,Hi].includes(e.key);n=b(t,e.target,r,!0)}n&&(n.focus({preventScroll:!0}),Xi.getOrCreateInstance(n).show())}_getChildren(){return V.find(Yi,this._parent)}_getActiveElem(){return this._getChildren().find((e=>this._elemIsActive(e)))||null}_setInitialAttributes(e,t){this._setAttributeIfNotExists(e,"role","tablist");for(const n of t)this._setInitialAttributesOnChild(n)}_setInitialAttributesOnChild(e){e=this._getInnerElement(e);const t=this._elemIsActive(e),n=this._getOuterElement(e);e.setAttribute("aria-selected",t),n!==e&&this._setAttributeIfNotExists(n,"role","presentation"),t||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}_setInitialAttributesOnTargetPanel(e){const t=V.getElementFromSelector(e);t&&(this._setAttributeIfNotExists(t,"role","tabpanel"),e.id&&this._setAttributeIfNotExists(t,"aria-labelledby",`${e.id}`))}_toggleDropDown(e,t){const n=this._getOuterElement(e);if(!n.classList.contains("dropdown"))return;const r=(e,r)=>{const i=V.findOne(e,n);i&&i.classList.toggle(r,t)};r(qi,Bi),r(".dropdown-menu",Wi),n.setAttribute("aria-expanded",t)}_setAttributeIfNotExists(e,t,n){e.hasAttribute(t)||e.setAttribute(t,n)}_elemIsActive(e){return e.classList.contains(Bi)}_getInnerElement(e){return e.matches(Yi)?e:V.findOne(Yi,e)}_getOuterElement(e){return e.closest(".nav-item, .list-group-item")||e}static jQueryInterface(e){return this.each((function(){const t=Xi.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}}))}}z.on(document,zi,Ki,(function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),s(this)||Xi.getOrCreateInstance(this).show()})),z.on(window,Di,(()=>{for(const e of V.find(Zi))Xi.getOrCreateInstance(e)})),m(Xi);const Gi=".bs.toast",Ji=`mouseover${Gi}`,ea=`mouseout${Gi}`,ta=`focusin${Gi}`,na=`focusout${Gi}`,ra=`hide${Gi}`,ia=`hidden${Gi}`,aa=`show${Gi}`,oa=`shown${Gi}`,la="hide",sa="show",ua="showing",ca={animation:"boolean",autohide:"boolean",delay:"number"},da={animation:!0,autohide:!0,delay:5e3};class fa extends H{constructor(e,t){super(e,t),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return da}static get DefaultType(){return ca}static get NAME(){return"toast"}show(){z.trigger(this._element,aa).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(la),d(this._element),this._element.classList.add(sa,ua),this._queueCallback((()=>{this._element.classList.remove(ua),z.trigger(this._element,oa),this._maybeScheduleHide()}),this._element,this._config.animation))}hide(){this.isShown()&&(z.trigger(this._element,ra).defaultPrevented||(this._element.classList.add(ua),this._queueCallback((()=>{this._element.classList.add(la),this._element.classList.remove(ua,sa),z.trigger(this._element,ia)}),this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(sa),super.dispose()}isShown(){return this._element.classList.contains(sa)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout((()=>{this.hide()}),this._config.delay)))}_onInteraction(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}if(t)return void this._clearTimeout();const n=e.relatedTarget;this._element===n||this._element.contains(n)||this._maybeScheduleHide()}_setListeners(){z.on(this._element,Ji,(e=>this._onInteraction(e,!0))),z.on(this._element,ea,(e=>this._onInteraction(e,!1))),z.on(this._element,ta,(e=>this._onInteraction(e,!0))),z.on(this._element,na,(e=>this._onInteraction(e,!1)))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each((function(){const t=fa.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e](this)}}))}}return B(fa),m(fa),{Alert:Q,Button:Y,Carousel:Ae,Collapse:Be,Dropdown:Qn,Modal:Lr,Offcanvas:qr,Popover:vi,ScrollSpy:Li,Tab:Xi,Toast:fa,Tooltip:fi}}());const tn=o(h());m(),window.$=window.jQuery=tn,document.addEventListener("DOMContentLoaded",(()=>{j((()=>import("./navbar-DKWY-dJv.js")),[]).then((e=>{const t=e.default;"function"==typeof t&&t()})),j((()=>import("./sliders-FN4r_CE7.js")),__vite__mapDeps([53,11])).then((e=>{const t=e.default;"function"==typeof t&&t()})),j((()=>import("./lightbox-SG57LFHg.js")),[]).then((e=>{const t=e.default;"function"==typeof t&&t()})),j((()=>import("./scroll-29t_LZvt.js")),[]).then((e=>{const t=e.default;"function"==typeof t&&t()})),j((()=>import("./cal-integration-UFwr938F.js")),[]).then((e=>{const t=e.default;"function"==typeof t&&(t(),window.addEventListener("load",t),document.addEventListener("visibilitychange",(()=>{"visible"===document.visibilityState&&t()})))}))}));const nn=document.getElementById("app"),rn=()=>{nn&&P.createRoot(nn).render(S.jsx(s.StrictMode,{children:S.jsx(Gt,{})}))};"complete"===document.readyState?rn():window.addEventListener("load",rn);export{Pe as H,S as j};
