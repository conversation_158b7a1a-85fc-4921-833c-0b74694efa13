import{j as e}from"./main-iJYbDIKL.js";import{b as n}from"./vendor-react-DkG9wxl6.js";const a=({children:a,calLink:t="researchsat-2023/30min",namespace:c="30min",config:i={layout:"month_view"},className:s,onClick:r,...l})=>{const o=n.useRef(null);return n.useEffect((()=>{const e=o.current;if(!e)return;const n=e=>{e.preventDefault(),r&&r(e),window.Cal&&window.Cal.ns&&window.Cal.ns[c]&&window.Cal.ns[c]("floatingButton",{calLink:t,config:{...i,cssVarsPerTheme:{light:{"cal-brand":"#cf1414"}},hideEventTypeDetails:!1}})};return e.addEventListener("click",n),()=>{e.removeEventListener("click",n)}}),[t,c,i,r]),e.jsx("a",{ref:o,href:"#","data-cal-link":t,"data-cal-namespace":c,"data-cal-config":JSON.stringify(i),className:s,...l,children:a})};export{a as C};
