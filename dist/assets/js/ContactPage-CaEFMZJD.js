import{j as s}from"./main-iJYbDIKL.js";import{b as e,L as a}from"./vendor-react-DkG9wxl6.js";import{C as c}from"./CalButton-BDenXGt5.js";import"./vendor-utils-DOb1KAbh.js";const l="_contactPage_vqqlm_1",i="_footerMargin_vqqlm_6",r="_heroSection_utd3t_1",n="_heroImageContainer_utd3t_8",t="_heroImage_utd3t_8",_="_blurOverlay_utd3t_27",m="_gradientOverlay_utd3t_36",o="_container_utd3t_46",d="_contentWrapper_utd3t_62",h="_label_utd3t_71",x="_title_utd3t_81",j=()=>s.jsxs("section",{className:r,children:[s.jsxs("div",{className:n,children:[s.jsx("img",{src:"/assets/png/hero-background-Bka6rV4P.png",alt:"Contact Hero",className:t}),s.jsx("div",{className:_}),s.jsx("div",{className:m})]}),s.jsx("div",{className:o,children:s.jsxs("div",{className:d,children:[s.jsx("div",{className:h,children:"_Contact"}),s.jsxs("h1",{className:x,children:["Turning your great ideas into reality is",s.jsx("br",{}),"our mission"]})]})})]}),N="_secondSection_1wpmu_1",u="_container_1wpmu_17",p="_sectionTitle_1wpmu_30",v="_contentContainer_1wpmu_41",g="_descriptionText_1wpmu_52",f="_grayText_1wpmu_63",y="_lightText_1wpmu_67",b="_scheduleButton_1wpmu_71",w="_buttonText_1wpmu_96",C=()=>s.jsx("section",{className:N,children:s.jsxs("div",{className:u,children:[s.jsxs("h2",{className:p,children:["Collaborate.",s.jsx("br",{}),"Create.",s.jsx("br",{}),"Conquer."]}),s.jsxs("div",{className:v,children:[s.jsxs("p",{className:g,children:[s.jsxs("span",{className:f,children:["Nothing beats a good old-fashioned face-to-face chat.",s.jsx("br",{}),s.jsx("br",{})]}),s.jsx("span",{className:y,children:"Schedule a call now—we know you've got a packed schedule!"})]}),s.jsx(c,{className:b,calLink:"researchsat-2023/30min",namespace:"30min",config:{layout:"month_view"},children:s.jsx("span",{className:w,children:"Schedule Meeting"})})]})]})}),q="_contactSection_1rnsm_1",S="_container_1rnsm_7",T="_formCard_1rnsm_13",M="_formContent_1rnsm_26",k="_formHeader_1rnsm_34",I="_formTitle_1rnsm_40",W="_formSubtitle_1rnsm_48",A="_contactForm_1rnsm_56",D="_formGroup_1rnsm_64",B="_formLabel_1rnsm_71",F="_inputWrapper_1rnsm_79",L="_formInput_1rnsm_89",O="_textareaWrapper_1rnsm_106",P="_formTextarea_1rnsm_115",$="_submitButton_1rnsm_134",E="_buttonText_1rnsm_154",H="_imageContainer_1rnsm_169",R="_contactImage_1rnsm_177",U=()=>{const[a,c]=e.useState({name:"",contact:"",message:""}),l=s=>{const{name:e,value:a}=s.target;c((s=>({...s,[e]:a})))};return s.jsx("section",{className:q,children:s.jsx("div",{className:S,children:s.jsxs("div",{className:T,children:[s.jsxs("div",{className:M,children:[s.jsxs("div",{className:k,children:[s.jsx("h3",{className:I,children:"Not quite ready to pencil in a meeting?"}),s.jsx("p",{className:W,children:"No worries—we've got your back! Simply fill out our form, and we'll take it from there."})]}),s.jsxs("form",{className:A,onSubmit:s=>{s.preventDefault(),alert("Thank you for your message! We will get back to you soon."),c({name:"",contact:"",message:""})},children:[s.jsxs("div",{className:D,children:[s.jsx("label",{htmlFor:"name",className:B,children:"Name"}),s.jsx("div",{className:F,children:s.jsx("input",{type:"text",id:"name",name:"name",className:L,placeholder:"Name",value:a.name,onChange:l,required:!0})})]}),s.jsxs("div",{className:D,children:[s.jsx("label",{htmlFor:"contact",className:B,children:"Contact"}),s.jsx("div",{className:F,children:s.jsx("input",{type:"text",id:"contact",name:"contact",className:L,placeholder:"00-0000-0000",value:a.contact,onChange:l,required:!0})})]}),s.jsxs("div",{className:D,children:[s.jsx("label",{htmlFor:"message",className:B,children:"Message"}),s.jsx("div",{className:O,children:s.jsx("textarea",{id:"message",name:"message",className:P,placeholder:"Enter your message here",rows:"4",value:a.message,onChange:l,required:!0})})]}),s.jsx("button",{type:"submit",className:$,children:s.jsx("span",{className:E,children:"Submit"})})]})]}),s.jsx("div",{className:H,children:s.jsx("img",{src:"/assets/jpg/contact-image-CyzY3W-A.jpg",alt:"Contact",className:R})})]})})})},z="_partnershipsSection_7fl5x_2",G="_container_7fl5x_14",V="_trustWallLabel_7fl5x_23",X="_partnershipsTitle_7fl5x_35",Y="_viewAllButton_7fl5x_48",J="_cardsGrid_7fl5x_76",K="_leftColumn_7fl5x_91",Q="_topRow_7fl5x_104",Z="_card_7fl5x_76",ss="_card1_7fl5x_155",es="_card2_7fl5x_161",as="_cardIcon_7fl5x_167",cs="_cardDetails_7fl5x_180",ls="_cardHeading_7fl5x_192",is="_cardCategory_7fl5x_202",rs="_cardTitle_7fl5x_213",ns="_bottomCard_7fl5x_229",ts="_rightColumn_7fl5x_249",_s="_largeCard_7fl5x_262",ms="_cardImage_7fl5x_281",os="_cardOverlay_7fl5x_294",ds=()=>s.jsx("section",{className:z,children:s.jsxs("div",{className:G,children:[s.jsx("div",{className:V,children:"_Trust Wall"}),s.jsx("h2",{className:X,children:"Our Partnerships"}),s.jsx(a,{to:"/partnerships",className:Y,children:"View All"}),s.jsxs("div",{className:J,children:[s.jsxs("div",{className:K,children:[s.jsxs("div",{className:Q,children:[s.jsxs("div",{className:`${Z} ${ss}`,children:[s.jsx("img",{src:"/src/assets/images/partnerships/diagonal-arrow1.svg",alt:"",className:as}),s.jsx("div",{className:cs,children:s.jsxs("div",{className:ls,children:[s.jsx("div",{className:is,children:"Missions"}),s.jsx("div",{className:rs,children:"Atmospheric Missions - Duration: 9 seconds"})]})})]}),s.jsxs("div",{className:`${Z} ${es}`,children:[s.jsx("img",{src:"/src/assets/images/partnerships/diagonal-arrow2.svg",alt:"",className:as}),s.jsx("div",{className:cs,children:s.jsxs("div",{className:ls,children:[s.jsx("div",{className:is,children:"Upcoming"}),s.jsx("div",{className:rs,children:"ISS Missions - Duration: 9 months"})]})})]})]}),s.jsxs("div",{className:ns,children:[s.jsx("img",{src:"/src/assets/images/partnerships/unsplash-s-3-h-qu-5-yjg0.png",alt:"Mission",className:ms}),s.jsxs("div",{className:os,children:[s.jsx("div",{className:is,children:"Missions"}),s.jsx("div",{className:rs,children:"Mission S1X-3/M15"})]})]})]}),s.jsx("div",{className:ts,children:s.jsxs("div",{className:_s,children:[s.jsx("img",{src:"/src/assets/images/partnerships/unsplash-s-3-h-qu-5-yjg1.png",alt:"Atmospheric Mission",className:ms}),s.jsxs("div",{className:os,children:[s.jsx("div",{className:is,children:"Missions"}),s.jsx("div",{className:rs,children:"Atmospheric Missions - Duration: 9 seconds"})]})]})})]})]})}),hs="_contactSection_qtoyi_1",xs="_contactTitle_qtoyi_15",js="_contactInfo_qtoyi_26",Ns="_reachUs_qtoyi_37",us="_contactDetails_qtoyi_49",ps="_contactEmail_qtoyi_58",vs="_contactPhone_qtoyi_58",gs=()=>s.jsxs("section",{className:hs,children:[s.jsx("h2",{className:xs,children:"Feel free to contact us"}),s.jsxs("div",{className:js,children:[s.jsx("div",{className:Ns,children:"Reach us @"}),s.jsxs("div",{className:us,children:[s.jsx("a",{href:"mailto:<EMAIL>",className:ps,children:"<EMAIL>"}),s.jsx("a",{href:"tel:+61452594883",className:vs,children:"+61 4525 94883"})]})]})]}),fs=()=>(e.useEffect((()=>{document.title="Contact Us | ResearchSat",window.scrollTo(0,0)}),[]),s.jsxs("div",{className:l,children:[s.jsx(j,{}),s.jsx(C,{}),s.jsx(U,{}),s.jsx(ds,{}),s.jsx(gs,{}),s.jsx("div",{className:i})]}));export{fs as default};
