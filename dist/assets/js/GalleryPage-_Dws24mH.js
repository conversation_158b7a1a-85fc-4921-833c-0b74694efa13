import{j as e}from"./main-CgIzQBkX.js";import{b as s}from"./vendor-react-DkG9wxl6.js";import{S as t}from"./SEO-DKPeNMo0.js";import"./vendor-utils-DOb1KAbh.js";const i=({src:t,alt:i,className:a,loading:r="lazy",sizes:o="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:n=!1,onClick:l,style:c,placeholder:p=!0})=>{const[h,m]=s.useState(!1),[g,d]=s.useState(!1),[u,y]=s.useState(!1),j=s.useRef(null);s.useEffect((()=>{if(n)return void d(!0);const e=new IntersectionObserver((([s])=>{s.isIntersecting&&(d(!0),e.disconnect())}),{rootMargin:"50px 0px",threshold:.1});return j.current&&e.observe(j.current),()=>e.disconnect()}),[n]);const{webpSrcSet:_,fallbackSrcSet:x}=(e=>{if(!e)return"";const s=e.split(".").pop().toLowerCase(),t=e.replace(`.${s}`,"");return{webpSrcSet:[`${t}-320.webp 320w`,`${t}-640.webp 640w`,`${t}-960.webp 960w`,`${t}-1280.webp 1280w`,`${t}-1920.webp 1920w`].join(", "),fallbackSrcSet:[`${t}-320.${s} 320w`,`${t}-640.${s} 640w`,`${t}-960.${s} 960w`,`${t}-1280.${s} 1280w`,`${t}-1920.${s} 1920w`].join(", ")}})(t),w=()=>e.jsxs("div",{className:a,style:{...c,backgroundColor:"#1a1a1a",display:"flex",alignItems:"center",justifyContent:"center",minHeight:"200px",position:"relative",overflow:"hidden"},children:[e.jsx("div",{style:{width:"40px",height:"40px",border:"3px solid rgba(255, 50, 65, 0.3)",borderTop:"3px solid #ff3241",borderRadius:"50%",animation:"spin 1s linear infinite"}}),e.jsx("style",{jsx:!0,children:"\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      "})]});return u?e.jsx("div",{className:a,style:{...c,backgroundColor:"#1a1a1a",display:"flex",alignItems:"center",justifyContent:"center",color:"#666",fontSize:"14px"},children:"Image unavailable"}):!g&&p?e.jsx("div",{ref:j,children:e.jsx(w,{})}):e.jsxs("div",{ref:j,style:{position:"relative"},children:[!h&&p&&e.jsx(w,{}),e.jsxs("picture",{style:{display:h?"block":"none"},children:[e.jsx("source",{srcSet:_,sizes:o,type:"image/webp"}),e.jsx("source",{srcSet:x,sizes:o}),e.jsx("img",{src:t,alt:i,className:a,style:c,loading:r,onLoad:()=>{m(!0)},onError:()=>{y(!0),m(!0)},onClick:l,decoding:"async",fetchpriority:n?"high":"auto"})]})]})};const a=new class{constructor(){this.cache=new Map,this.loadingPromises=new Map,this.preloadQueue=[],this.isProcessing=!1,this.maxConcurrent=3,this.currentLoading=0}async preloadImage(e,s=!1){if(this.cache.has(e))return this.cache.get(e);if(this.loadingPromises.has(e))return this.loadingPromises.get(e);const t=this.loadImageWithFallback(e);this.loadingPromises.set(e,t);try{const s=await t;return this.cache.set(e,s),this.loadingPromises.delete(e),s}catch(i){throw this.loadingPromises.delete(e),i}}async loadImageWithFallback(e){const s=e.split(".").pop().toLowerCase(),t=`${e.replace(`.${s}`,"")}.webp`;try{if(this.supportsWebP()){return{src:t,image:await this.loadSingleImage(t),format:"webp"}}}catch(i){}return{src:e,image:await this.loadSingleImage(e),format:s}}loadSingleImage(e){return new Promise(((s,t)=>{const i=new Image;i.onload=()=>s(i),i.onerror=()=>t(new Error(`Failed to load image: ${e}`)),e.startsWith("http")&&(i.crossOrigin="anonymous"),i.src=e}))}supportsWebP(){if(void 0!==this._webpSupport)return this._webpSupport;const e=document.createElement("canvas");return e.width=1,e.height=1,this._webpSupport=0===e.toDataURL("image/webp").indexOf("data:image/webp"),this._webpSupport}async preloadImages(e,s={}){const{priority:t=!1,maxConcurrent:i=this.maxConcurrent,onProgress:a,onError:r}=s,o=[],n=[];let l=0;for(let c=0;c<e.length;c+=i){const s=e.slice(c,c+i).map((async s=>{try{const i=await this.preloadImage(s,t);return l++,a&&a(l,e.length),i}catch(i){return l++,n.push({src:s,error:i}),r&&r(s,i),a&&a(l,e.length),null}})),p=await Promise.all(s);o.push(...p)}return{results:o.filter(Boolean),errors:n,totalLoaded:o.filter(Boolean).length,totalErrors:n.length}}async preloadCritical(e){return this.preloadImages(e,{priority:!0,maxConcurrent:6,onProgress:(e,s)=>{}})}async preloadInViewport(e){return this.preloadImages(e,{priority:!1,maxConcurrent:2,onProgress:(e,s)=>{}})}getResponsiveSources(e){const s=e.split(".").pop().toLowerCase(),t=e.replace(`.${s}`,"");return{webp:[`${t}-320.webp`,`${t}-640.webp`,`${t}-960.webp`,`${t}-1280.webp`,`${t}-1920.webp`],original:[`${t}-320.${s}`,`${t}-640.${s}`,`${t}-960.${s}`,`${t}-1280.${s}`,`${t}-1920.${s}`]}}async preloadResponsive(e,s=window.innerWidth){let t;this.getResponsiveSources(e),t=s<=320?"320":s<=640?"640":s<=960?"960":s<=1280?"1280":"1920";const i=[t],a=["320","640","960","1280","1920"].indexOf(t);a<4&&i.push(["320","640","960","1280","1920"][a+1]);const r=[];return i.forEach((s=>{const t=e.split(".").pop().toLowerCase(),i=e.replace(`.${t}`,"");r.push(`${i}-${s}.webp`),r.push(`${i}-${s}.${t}`)})),this.preloadImages(r,{priority:!0})}clearCache(){this.cache.clear(),this.loadingPromises.clear()}getCacheStats(){return{cached:this.cache.size,loading:this.loadingPromises.size,memoryUsage:this.estimateMemoryUsage()}}estimateMemoryUsage(){let e=0;return this.cache.forEach((s=>{s.image&&(e+=(s.image.width||0)*(s.image.height||0)*4)})),e}},r={galleryPage:"_galleryPage_ch44e_2",heroSection:"_heroSection_ch44e_7",heroBackground:"_heroBackground_ch44e_18",gradientOverlay:"_gradientOverlay_ch44e_29",contentContainer:"_contentContainer_ch44e_40",heroContent:"_heroContent_ch44e_48",galleryLabel:"_galleryLabel_ch44e_59",heroTitle:"_heroTitle_ch44e_70",bottomContainer:"_bottomContainer_ch44e_82",descriptionContainer:"_descriptionContainer_ch44e_96",descriptionText:"_descriptionText_ch44e_105",gallerySection:"_gallerySection_ch44e_118",container:"_container_ch44e_127",sectionTitle:"_sectionTitle_ch44e_133",bentoGrid:"_bentoGrid_ch44e_155",bentoItem:"_bentoItem_ch44e_163",hero:"_hero_ch44e_7",large:"_large_ch44e_184",medium:"_medium_ch44e_189",small:"_small_ch44e_194",bentoImage:"_bentoImage_ch44e_199",imageOverlay:"_imageOverlay_ch44e_213",imageCategory:"_imageCategory_ch44e_236",ctaSection:"_ctaSection_ch44e_254",ctaContent:"_ctaContent_ch44e_260",ctaTitle:"_ctaTitle_ch44e_265",ctaDescription:"_ctaDescription_ch44e_273",ctaButtons:"_ctaButtons_ch44e_280",primaryButton:"_primaryButton_ch44e_287",secondaryButton:"_secondaryButton_ch44e_288",footerMargin:"_footerMargin_ch44e_322",slideshowOverlay:"_slideshowOverlay_ch44e_327",slideshowContainer:"_slideshowContainer_ch44e_341",closeButton:"_closeButton_ch44e_352",navButton:"_navButton_ch44e_376",slideshowImageContainer:"_slideshowImageContainer_ch44e_400",slideshowImage:"_slideshowImage_ch44e_400",slideshowInfo:"_slideshowInfo_ch44e_419",slideshowTitle:"_slideshowTitle_ch44e_425",slideshowCounter:"_slideshowCounter_ch44e_432",thumbnailContainer:"_thumbnailContainer_ch44e_438",thumbnail:"_thumbnail_ch44e_438",activeThumbnail:"_activeThumbnail_ch44e_483",imageLoading:"_imageLoading_ch44e_493",shimmer:"_shimmer_ch44e_1",imagePlaceholder:"_imagePlaceholder_ch44e_508",loading:"_loading_ch44e_1"},o="/assets/jpeg/galleryhr-jobtMjA1.jpeg",n=()=>{var n,l,c;const[p,h]=s.useState(!1),[m,g]=s.useState(0),[d,u]=s.useState([]),[y,j]=s.useState(!1),[_,x]=s.useState(new Set(["mission"]));s.useEffect((()=>{window.scrollTo(0,0)}),[]);const w=s.useMemo((()=>[{src:"/assets/webp/SubOrbital-Express-3-launch-DvfSFvp1.webp",alt:"SubOrbital Express 3 Launch",position:"hero",priority:!0},{src:"/assets/png/Figure001-CJa69uqs.png",alt:"Scientific Figure and Data Analysis",position:"large",priority:!0},{src:"/assets/png/CubeSat_galaxyfixed-cbSQ9347.png",alt:"CubeSat Galaxy Mission",position:"medium",priority:!1},{src:"/assets/jpg/Mission_Dashboard_Screenshot-qIReIC_j.jpg",alt:"Mission Dashboard Screenshot",position:"small",priority:!1},{src:"/assets/jpg/20220920_001428-BC8zhB7p.jpg",alt:"Late Night Mission Operations",position:"medium",priority:!1},{src:"/assets/png/Figure020-CMUWfh9L.png",alt:"Mission Data and Results",position:"small",priority:!1},{src:"/assets/jpg/20220919_190558-Bz6ulgFL.jpg",alt:"Mission Preparation - September 2022",position:"medium",priority:!1},{src:"/assets/jpg/20220920_001901-BsKV2bgO.jpg",alt:"Mission Monitoring",position:"small",priority:!1},{src:"/assets/jpg/Image_%2011022022_19_19_16(1)-Dk5vtvM-.jpg",alt:"Mission Event - November 2022",position:"large",priority:!1},{src:"/assets/jpg/WhatsApp%20Image%202024-02-27%20at%2011.24.18_2185ba9e-Df-tbQPN.jpg",alt:"Mission Planning - February 2024",position:"small",priority:!1}]),[]),v=s.useMemo((()=>[{src:"/assets/jpg/Engineers-Cw9XdDt2.jpg",alt:"Engineering Team",position:"hero",priority:!1},{src:"/assets/JPG/_DSC7330-D3rDjGKc.JPG",alt:"Professional Photography Session",position:"large",priority:!1},{src:"/assets/JPG/galimg008-S6pBCvjm.JPG",alt:"Team Collaboration Session",position:"medium",priority:!1},{src:"/assets/JPG/_DSC7380-DtJIT4pO.JPG",alt:"Professional Team Photography",position:"medium",priority:!1},{src:"/assets/jpg/20220919_194504-BpZ6nj1H.jpg",alt:"Team Working Session",position:"small",priority:!1},{src:"/assets/JPG/_DSC7331-DdGH7tiG.JPG",alt:"Team Portrait Session",position:"small",priority:!1},{src:"/assets/jpg/WhatsApp%20Image%202023-12-08%20at%2023.55.09_6e58b7b8-DjmDhtcD.jpg",alt:"Late Night Work Session",position:"large",priority:!1},{src:"/assets/jpeg/Image_20221102_232544_334-wBMvgH_3.jpeg",alt:"Team Collaboration",position:"small",priority:!1},{src:"/assets/jpg/WhatsApp%20Image%202023-12-08%20at%2016.52.08_e4be6a91-Dj_UyL6J.jpg",alt:"Team Communication - December 2023",position:"medium",priority:!1},{src:"/assets/jpg/IMG_0225-DAPjRRtF.jpg",alt:"Team Member Portrait",position:"small",priority:!1},{src:"/assets/jpg/IMG_0762-BPLZvABk.jpg",alt:"Team Working",position:"small",priority:!1},{src:"/assets/jpg/IMG_1517-CT-ff1K_.jpg",alt:"Team Discussion",position:"medium",priority:!1}]),[]),b=s.useMemo((()=>[{src:"/assets/jpg/AIC%20_%20RST%20Payloads-DHxf8dwZ.jpg",alt:"AIC & RST Payloads",position:"hero",priority:!1},{src:"/assets/png/Titanium_Box_croped-blChzu-u.png",alt:"Titanium Payload Container",position:"large",priority:!1},{src:"/assets/jpg/Microfluidic%20chip%20flowrate%20experiment-CpZlq5pN.jpg",alt:"Microfluidic Chip Flowrate Experiment",position:"large",priority:!1},{src:"/assets/png/abhrMob-Cq_Nuc05.png",alt:"Mobile Research Platform",position:"medium",priority:!1},{src:"/assets/jpg/V%202-CQbq74vL.jpg",alt:"Version 2 Development",position:"medium",priority:!1},{src:"/assets/jpg/20220920_001809-B0BjG7KN.jpg",alt:"Technical Equipment Check",position:"small",priority:!1},{src:"/assets/jpg/WhatsApp%20Image%202024-02-25%20at%2015.08.34_531215bc-2-_CaYvS.jpg",alt:"Equipment Testing - February 2024",position:"small",priority:!1},{src:"/assets/jpg/IMG_0787-CD5KJkAc.jpg",alt:"Payload Development",position:"medium",priority:!1},{src:"/assets/jpg/IMG_1556-CuHBRDYD.jpg",alt:"Payload Testing",position:"small",priority:!1},{src:"/assets/jpg/IMG_2571-V3CiLVXr.jpg",alt:"Payload Assembly",position:"small",priority:!1}]),[]),C=s.useMemo((()=>[{src:"/assets/jpeg/galimg001-zcfT285k.jpeg",alt:"Research Laboratory Setup",position:"hero",priority:!1},{src:"/assets/png/galimg007-BfAhXeVB.png",alt:"Laboratory Research Process",position:"large",priority:!1},{src:"/assets/jpeg/Image_20221118_145359_870%20(4)-D1mH3SpO.jpeg",alt:"Research Progress - November 2022",position:"large",priority:!1},{src:"/assets/png/galimg006-8EOO9NJ4.png",alt:"Space Research Equipment",position:"medium",priority:!1},{src:"/assets/png/galimg009-BHQpMWmS.png",alt:"Advanced Research Facility",position:"medium",priority:!1},{src:"/assets/jpg/20220920_001539-Dy4dUyvO.jpg",alt:"Mission Control Center",position:"medium",priority:!1},{src:"/assets/jpg/P6-SR6v9nNi.jpg",alt:"Project Documentation P6",position:"large",priority:!1},{src:"/assets/png/IMG_4837-DCcgJr-n.png",alt:"Technical Documentation",position:"medium",priority:!1},{src:"/assets/jpg/serv1-PscBiVwp.jpg",alt:"Space Services and Operations",position:"medium",priority:!1},{src:"/assets/jpg/11-CzLR2NDV.jpg",alt:"Research Equipment Setup",position:"small",priority:!1},{src:"/assets/JPG/DSC00382-Y-8-CXji.JPG",alt:"Professional Mission Photography",position:"small",priority:!1},{src:"/assets/jpg/IMG_6984-D1SuAj5k.jpg",alt:"Research Facility",position:"small",priority:!1},{src:"/assets/jpg/P1-Cf7ehPs9.jpg",alt:"Project Documentation P1",position:"small",priority:!1},{src:"/assets/jpg/P23-CHVF68Jy.jpg",alt:"Project Documentation P23",position:"small",priority:!1},{src:"/assets/jpg/WhatsApp%20Image%202023-12-08%20at%2016.53.24_6b5e22fa-Xfi9ZJZv.jpg",alt:"Project Updates - December 2023",position:"small",priority:!1},{src:"/assets/jpg/WhatsApp%20Image%202024-02-25%20at%2015.08.07_a9e86f05-BFx3KucJ.jpg",alt:"Research Progress - February 2024",position:"medium",priority:!1},{src:"/assets/jpg/WhatsApp%20Image%202024-02-25%20at%2015.08.44_27d716e0-B2hDLcJU.jpg",alt:"Technical Setup - February 2024",position:"small",priority:!1},{src:"/assets/jpg/WhatsApp%20Image%202024-08-02%20at%2017.05.54_60d4f3d5-CCtqmC0K.jpg",alt:"Recent Developments - August 2024",position:"large",priority:!1},{src:"/assets/png/image009-DfPmozn3.png",alt:"Technical Documentation Image",position:"small",priority:!1},{src:"/assets/jpg/image010-XyBbzwPD.jpg",alt:"Research Documentation",position:"medium",priority:!1},{src:"/assets/jpg/galimg002-vmd_Z2Dr.jpg",alt:"Laboratory Equipment",position:"medium",priority:!1},{src:"/assets/jpg/galimg003-BGxLfzfE.jpg",alt:"Research Setup",position:"small",priority:!1},{src:"/assets/jpg/galimg004-CiZScLwa.jpg",alt:"Technical Work",position:"small",priority:!1},{src:"/assets/jpg/galimg005-DcGT6IRS.jpg",alt:"Research Process",position:"medium",priority:!1},{src:"/assets/jpg/IMG_1536--Nu1if_Z.jpg",alt:"Research Activity",position:"small",priority:!1},{src:"/assets/jpg/IMG_3282-BPAIgG-F.jpg",alt:"Laboratory Work",position:"small",priority:!1},{src:"/assets/jpg/IMG_3356%20(1)-CMhLnUnI.jpg",alt:"Technical Setup",position:"medium",priority:!1},{src:"/assets/jpg/IMG_4752-D-ZRT1-Z.jpg",alt:"Research Documentation",position:"small",priority:!1},{src:"/assets/jpg/IMG_4950-CfMSTs-9.jpg",alt:"Laboratory Process",position:"medium",priority:!1},{src:"/assets/jpg/IMG_6948%20(1)-GDspsSxw.jpg",alt:"Research Equipment",position:"small",priority:!1},{src:"/assets/jpg/IMG_6949-BjT2sxT1.jpg",alt:"Technical Work",position:"small",priority:!1}]),[]),f=s.useMemo((()=>[...w,...v,...b,...C]),[w,v,b,C]);s.useEffect((()=>{u(f)}),[f]),s.useEffect((()=>{const e=[o,...w.filter((e=>e.priority)).map((e=>e.src))];var s;(s=e,a.preloadCritical(s)).then((()=>{j(!0)}))}),[w]),s.useEffect((()=>{const e=new IntersectionObserver((e=>{e.forEach((e=>{if(e.isIntersecting){const i=e.target.dataset.section;if(i&&!_.has(i)){x((e=>new Set([...e,i])));let e=[];switch(i){case"team":e=v.map((e=>e.src));break;case"payload":e=b.map((e=>e.src));break;case"activity":e=C.map((e=>e.src))}e.length>0&&(s=e,t={maxConcurrent:2},a.preloadImages(s,t))}}var s,t}))}),{root:null,rootMargin:"100px 0px",threshold:.1});return document.querySelectorAll("[data-section]").forEach((s=>e.observe(s))),()=>e.disconnect()}),[_,v,b,C]);const S=s.useCallback(((e,s,t=0)=>{g(t+e),h(!0),document.body.style.overflow="hidden"}),[]),I=s.useCallback((()=>{h(!1),document.body.style.overflow="unset"}),[]),N=s.useCallback((()=>{g((e=>(e+1)%d.length))}),[d.length]),P=s.useCallback((()=>{g((e=>(e-1+d.length)%d.length))}),[d.length]);s.useEffect((()=>{const e=e=>{if(p)switch(e.key){case"ArrowRight":N();break;case"ArrowLeft":P();break;case"Escape":I()}};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[p,N,P,I]);const k=e=>{switch(e){case"mission":default:return 0;case"team":return w.length;case"payload":return w.length+v.length;case"activity":return w.length+v.length+b.length}};return e.jsxs(e.Fragment,{children:[e.jsx(t,{title:"Gallery - Space Biology Research Images",description:"Explore ResearchSat's comprehensive gallery showcasing space missions, research team, cutting-edge payloads, and groundbreaking microgravity research activities.",keywords:["space research gallery","microgravity experiments photos","satellite missions images","space biology photos","research team gallery","space technology images","orbital research pictures","space laboratory photos"],canonical:"https://researchsat.space/gallery",structuredData:{"@context":"https://schema.org","@type":"ImageGallery",name:"ResearchSat Gallery - Space Biology Research Images",description:"Explore our comprehensive gallery showcasing space missions, research team, payloads, and scientific activities in microgravity research.",url:"https://researchsat.space/gallery",publisher:{"@type":"Organization",name:"ResearchSat"},image:["https://researchsat.space/src/assets/images/gallery/galleryhr.jpeg"]},breadcrumbs:[{name:"Home",url:"https://researchsat.space"},{name:"Gallery",url:"https://researchsat.space/gallery"}],ogType:"website",ogImage:o}),e.jsxs("div",{className:r.galleryPage,children:[e.jsxs("section",{className:r.heroSection,children:[e.jsx("img",{src:o,alt:"Space background",className:r.heroBackground}),e.jsx("div",{className:r.gradientOverlay}),e.jsx("div",{className:r.contentContainer,children:e.jsxs("div",{className:r.heroContent,children:[e.jsx("div",{className:r.galleryLabel,children:"_Gallery"}),e.jsx("h1",{className:r.heroTitle,children:"Discover our world through images"})]})}),e.jsx("div",{className:r.bottomContainer,children:e.jsxs("div",{className:r.descriptionContainer,children:[e.jsx("p",{className:r.descriptionText,children:"Explore our journey through space research, showcasing past missions, our dedicated team, and groundbreaking activities."}),e.jsx("div",{style:{textAlign:"right"},children:e.jsx("a",{href:"/book-mission",style:{textDecoration:"none"},children:e.jsx("span",{style:{background:"linear-gradient(180deg, #BBDED0 0%, #87C2AA 100%)",WebkitBackgroundClip:"text",backgroundClip:"text",WebkitTextFillColor:"transparent",fontSize:"16px",fontWeight:500,lineHeight:"120%",letterSpacing:"0.25px",fontFamily:"Poppins, sans-serif"},children:"...explore our missions"})})})]})})]}),e.jsx("section",{className:r.gallerySection,"data-section":"mission",children:e.jsxs("div",{className:r.container,children:[e.jsx("h2",{className:r.sectionTitle,children:"Space Missions"}),e.jsx("div",{className:r.bentoGrid,children:w.map(((s,t)=>e.jsxs("div",{className:`${r.bentoItem} ${r[s.position]}`,onClick:()=>S(t,w,k("mission")),children:[e.jsx(i,{src:s.src,alt:s.alt,className:r.bentoImage,loading:s.priority?"eager":"lazy",priority:s.priority,sizes:"hero"===s.position?"(max-width: 768px) 100vw, 50vw":"large"===s.position?"(max-width: 768px) 100vw, 33vw":"medium"===s.position?"(max-width: 768px) 50vw, 25vw":"(max-width: 768px) 50vw, 16vw"}),e.jsx("div",{className:r.imageOverlay,children:e.jsx("span",{className:r.imageCategory,children:"Mission"})})]},`mission-${t}`)))})]})}),e.jsx("section",{className:r.gallerySection,"data-section":"team",children:e.jsxs("div",{className:r.container,children:[e.jsx("h2",{className:r.sectionTitle,children:"Our Team"}),e.jsx("div",{className:r.bentoGrid,children:_.has("team")&&v.map(((s,t)=>e.jsxs("div",{className:`${r.bentoItem} ${r[s.position]}`,onClick:()=>S(t,v,k("team")),children:[e.jsx(i,{src:s.src,alt:s.alt,className:r.bentoImage,loading:"lazy",sizes:"hero"===s.position?"(max-width: 768px) 100vw, 50vw":"large"===s.position?"(max-width: 768px) 100vw, 33vw":"medium"===s.position?"(max-width: 768px) 50vw, 25vw":"(max-width: 768px) 50vw, 16vw"}),e.jsx("div",{className:r.imageOverlay,children:e.jsx("span",{className:r.imageCategory,children:"Team"})})]},`team-${t}`)))})]})}),e.jsx("section",{className:r.gallerySection,"data-section":"payload",children:e.jsxs("div",{className:r.container,children:[e.jsx("h2",{className:r.sectionTitle,children:"Payloads & Equipment"}),e.jsx("div",{className:r.bentoGrid,children:_.has("payload")&&b.map(((s,t)=>e.jsxs("div",{className:`${r.bentoItem} ${r[s.position]}`,onClick:()=>S(t,b,k("payload")),children:[e.jsx(i,{src:s.src,alt:s.alt,className:r.bentoImage,loading:"lazy",sizes:"hero"===s.position?"(max-width: 768px) 100vw, 50vw":"large"===s.position?"(max-width: 768px) 100vw, 33vw":"medium"===s.position?"(max-width: 768px) 50vw, 25vw":"(max-width: 768px) 50vw, 16vw"}),e.jsx("div",{className:r.imageOverlay,children:e.jsx("span",{className:r.imageCategory,children:"Payload"})})]},`payload-${t}`)))})]})}),e.jsx("section",{className:r.gallerySection,"data-section":"activity",children:e.jsxs("div",{className:r.container,children:[e.jsx("h2",{className:r.sectionTitle,children:"Research Activities"}),e.jsx("div",{className:r.bentoGrid,children:_.has("activity")&&C.map(((s,t)=>e.jsxs("div",{className:`${r.bentoItem} ${r[s.position]}`,onClick:()=>S(t,C,k("activity")),children:[e.jsx(i,{src:s.src,alt:s.alt,className:r.bentoImage,loading:"lazy",sizes:"hero"===s.position?"(max-width: 768px) 100vw, 50vw":"large"===s.position?"(max-width: 768px) 100vw, 33vw":"medium"===s.position?"(max-width: 768px) 50vw, 25vw":"(max-width: 768px) 50vw, 16vw"}),e.jsx("div",{className:r.imageOverlay,children:e.jsx("span",{className:r.imageCategory,children:"Activity"})})]},`activity-${t}`)))})]})}),e.jsx("section",{className:r.ctaSection,children:e.jsx("div",{className:r.container,children:e.jsxs("div",{className:r.ctaContent,children:[e.jsx("h2",{className:r.ctaTitle,children:"Ready to be part of our story?"}),e.jsx("p",{className:r.ctaDescription,children:"Join us in advancing space research and be featured in our next gallery showcase."}),e.jsxs("div",{className:r.ctaButtons,children:[e.jsx("a",{href:"/book-mission",className:r.primaryButton,children:"Book Mission"}),e.jsx("a",{href:"/contact",className:r.secondaryButton,children:"Contact Us"})]})]})})}),e.jsx("div",{className:r.footerMargin}),p&&d.length>0&&e.jsx("div",{className:r.slideshowOverlay,onClick:I,children:e.jsxs("div",{className:r.slideshowContainer,onClick:e=>e.stopPropagation(),children:[e.jsx("button",{className:r.closeButton,onClick:I,children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M18 6L6 18M6 6L18 18",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{className:r.navButton,onClick:P,style:{left:"20px"},children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{className:r.navButton,onClick:N,style:{right:"20px"},children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("div",{className:r.slideshowImageContainer,children:e.jsx(i,{src:null==(n=d[m])?void 0:n.src,alt:null==(l=d[m])?void 0:l.alt,className:r.slideshowImage,loading:"eager",priority:!0,sizes:"90vw",placeholder:!1})}),e.jsxs("div",{className:r.slideshowInfo,children:[e.jsx("h3",{className:r.slideshowTitle,children:null==(c=d[m])?void 0:c.alt}),e.jsxs("p",{className:r.slideshowCounter,children:[m+1," of ",d.length]})]}),e.jsx("div",{className:r.thumbnailContainer,children:d.map(((s,t)=>e.jsx("div",{className:`${r.thumbnail} ${t===m?r.activeThumbnail:""}`,onClick:()=>g(t),children:e.jsx("img",{src:s.src,alt:s.alt})},t)))})]})})]})]})};export{n as default};
