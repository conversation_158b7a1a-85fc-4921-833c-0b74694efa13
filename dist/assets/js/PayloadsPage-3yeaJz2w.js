import{j as e}from"./main-iJYbDIKL.js";import{b as s,L as a}from"./vendor-react-DkG9wxl6.js";import{S as i}from"./SEO-B3bIlZbv.js";import{C as t}from"./CalButton-BDenXGt5.js";import{B as n}from"./BookMission-CLg2k6W0.js";import{s as r}from"./serv1-Bb5bIGGW.js";import"./vendor-utils-DOb1KAbh.js";const o="_heroSection_mnlhb_1",l="_heroBackground_mnlhb_8",c="_gradientOverlay_mnlhb_17",d="_topContainer_mnlhb_26",m="_heroContent_mnlhb_41",h="_payloadsLabel_mnlhb_50",_="_heroHeading_mnlhb_59",p="_bottomContainer_mnlhb_69",u="_leftColumn_mnlhb_86",f="_subHeading_mnlhb_91",x="_rightColumn_mnlhb_100",g="_description_mnlhb_109",v="_grayText_mnlhb_118",j="_highlightText_mnlhb_122",y="_contactButton_mnlhb_126",b=()=>e.jsxs("div",{className:o,children:[e.jsx("img",{src:"/assets/png/payloads-hero-bg-4PDjTP5R.png",alt:"Space background",className:l}),e.jsx("div",{className:c}),e.jsx("div",{className:d,children:e.jsxs("div",{className:m,children:[e.jsx("div",{className:h,children:"_Payloads"}),e.jsx("h1",{className:_,children:"Harness the vast potential of space, and let us handle the logistics"})]})}),e.jsxs("div",{className:p,children:[e.jsx("div",{className:u,children:e.jsx("h2",{className:f,children:"Why Choose ResearchSat Payloads?"})}),e.jsxs("div",{className:x,children:[e.jsxs("p",{className:g,children:[e.jsxs("span",{className:v,children:["Our payloads are more than just containers; they're cutting-edge tools designed to optimize your research outcomes. Collaborate with us to leverage the untapped potential of space in your next project.",e.jsx("br",{}),e.jsx("br",{}),"At"]}),e.jsx("span",{className:j,children:" ResearchSat"}),e.jsx("span",{className:v,children:", we believe in the transformative power of space to revolutionize our understanding of life sciences and its potential applications."})]}),e.jsx(t,{className:y,calLink:"researchsat-2023/30min",namespace:"30min",config:{layout:"month_view"},children:"Contact"})]})]})]}),N="_container_109ap_1",C="_container2_109ap_23",w="_container3_109ap_36",S="_container4_109ap_48",T="_container5_109ap_62",z="_description_109ap_73",k=()=>e.jsx("div",{className:N,children:e.jsx("div",{className:C,children:e.jsx("div",{className:w,children:e.jsx("div",{className:S,children:e.jsx("div",{className:T,children:e.jsx("div",{className:z,children:"In microgravity, biological processes unfold differently, revealing insights impossible to observe on Earth. Our custom payload solutions capture these unique phenomena, accelerating discoveries in medicine and biotechnology."})})})})})}),D={secondSection:"_secondSection_7qvrc_1",container:"_container_7qvrc_14",content:"_content_7qvrc_21",textColumn:"_textColumn_7qvrc_29",heading:"_heading_7qvrc_37",description:"_description_7qvrc_46",featuresGrid:"_featuresGrid_7qvrc_55",featureCard:"_featureCard_7qvrc_63",featureIcon:"_featureIcon_7qvrc_80",featureTitle:"_featureTitle_7qvrc_86",featureDescription:"_featureDescription_7qvrc_94"},P=()=>e.jsx("div",{className:D.secondSection,children:e.jsx("div",{className:D.container,children:e.jsxs("div",{className:D.content,children:[e.jsxs("div",{className:D.textColumn,children:[e.jsx("h2",{className:D.heading,children:"Custom Payload Solutions"}),e.jsxs("p",{className:D.description,children:[e.jsx("span",{className:D.highlightText,children:" ResearchSat"}),", delivers specialized microgravity research platforms optimized for your unique experimental requirements. Our payloads maximize research potential while minimizing complexity."]})]}),e.jsxs("div",{className:D.featuresGrid,children:[e.jsxs("div",{className:D.featureCard,children:[e.jsx("div",{className:D.featureIcon,children:e.jsx("i",{className:"fas fa-temperature-high"})}),e.jsx("h3",{className:D.featureTitle,children:"Temperature Control"}),e.jsx("p",{className:D.featureDescription,children:"Precise temperature regulation (±0.5°C) to maintain optimal conditions for biological samples throughout the mission."})]}),e.jsxs("div",{className:D.featureCard,children:[e.jsx("div",{className:D.featureIcon,children:e.jsx("i",{className:"fas fa-robot"})}),e.jsx("h3",{className:D.featureTitle,children:"Automation"}),e.jsx("p",{className:D.featureDescription,children:"Fully automated experimental procedures to ensure consistent execution without human intervention."})]}),e.jsxs("div",{className:D.featureCard,children:[e.jsx("div",{className:D.featureIcon,children:e.jsx("i",{className:"fas fa-chart-line"})}),e.jsx("h3",{className:D.featureTitle,children:"Data Monitoring"}),e.jsx("p",{className:D.featureDescription,children:"Real-time data collection and monitoring capabilities to track experimental progress throughout the mission."})]}),e.jsxs("div",{className:D.featureCard,children:[e.jsx("div",{className:D.featureIcon,children:e.jsx("i",{className:"fas fa-cogs"})}),e.jsx("h3",{className:D.featureTitle,children:"Customization"}),e.jsx("p",{className:D.featureDescription,children:"Tailored solutions designed to meet the specific requirements of your research objectives and experimental protocols."})]})]})]})})}),q="_sectionContainer_1nbff_1",R="_imageContainer_1nbff_14",M="_sectionImage_1nbff_21",I="_contentContainer_1nbff_40",B="_headerContainer_1nbff_50",L="_sectionTitle_1nbff_60",O="_sectionDate_1nbff_71",A="_descriptionContainer_1nbff_81",E="_sectionDescription_1nbff_91",H="_readMoreLink_1nbff_102",G="_lightboxOverlay_2brfh_1",W="_lightboxContainer_2brfh_16",F="_closeButton_2brfh_27",K="_lightboxContent_2brfh_50",Q="_imageSection_2brfh_57",V="_payloadImage_2brfh_62",Y="_contentSection_2brfh_69",Z="_headerContainer_2brfh_77",J="_payloadLabel_2brfh_82",U="_payloadTitle_2brfh_89",X="_payloadSubtitle_2brfh_96",$="_divider_2brfh_103",ee="_featuresContainer_2brfh_110",se="_featuresTitle_2brfh_114",ae="_featuresList_2brfh_121",ie="_featureItem_2brfh_127",te="_checkIcon_2brfh_133",ne="_featureText_2brfh_142",re="_applicationsContainer_2brfh_148",oe="_applicationsTitle_2brfh_152",le="_applicationsText_2brfh_159",ce="_buttonsContainer_2brfh_165",de="_contactButton_2brfh_172",me="_backButton_2brfh_203",he=({isOpen:a,onClose:i,title:t,subtitle:n,image:r,features:o,applications:l})=>{const c=s.useRef(null);return s.useEffect((()=>{const e=e=>{c.current&&!c.current.contains(e.target)&&i()};return a&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}}),[a,i]),a?e.jsx("div",{className:G,onClick:i,children:e.jsxs("div",{className:W,ref:c,onClick:e=>e.stopPropagation(),children:[e.jsx("button",{className:F,onClick:i,children:"×"}),e.jsxs("div",{className:K,children:[e.jsx("div",{className:Q,children:e.jsx("img",{src:r,alt:t,className:V})}),e.jsxs("div",{className:Y,children:[e.jsxs("div",{className:Z,children:[e.jsx("div",{className:J,children:"_Payload"}),e.jsx("h2",{className:U,children:t}),n&&e.jsx("h6",{className:X,children:n})]}),e.jsx("hr",{className:$}),e.jsxs("div",{className:ee,children:[e.jsx("h4",{className:se,children:"Key Features:"}),e.jsx("ul",{className:ae,children:o.map(((s,a)=>e.jsxs("li",{className:ie,children:[e.jsx("i",{className:te}),e.jsx("div",{className:ne,children:s})]},a)))})]}),l&&e.jsxs("div",{className:re,children:[e.jsx("h4",{className:oe,children:"Potential Applications:"}),e.jsx("p",{className:le,children:l})]}),e.jsxs("div",{className:ce,children:[e.jsx("a",{href:"javascript:void(0)",className:de,onClick:e=>{e.preventDefault(),i(),setTimeout((()=>{const e=document.getElementById("bookmission");e&&e.scrollIntoView({behavior:"smooth"})}),300)},children:"Schedule a Consultation"}),e.jsx("button",{className:me,onClick:i,children:"Back"})]})]})]})]})}):null},_e=({image:a,title:i,date:t,description:n,readMoreLink:r,features:o=[],applications:l="",subtitle:c=""})=>{const[d,m]=s.useState(!1);return e.jsxs("section",{className:q,children:[e.jsx("div",{className:R,children:e.jsx("img",{src:a,alt:i,className:M})}),e.jsxs("div",{className:I,children:[e.jsxs("div",{className:B,children:[e.jsx("h3",{className:L,children:i}),t&&e.jsx("div",{className:O,children:t})]}),e.jsxs("div",{className:A,children:[e.jsx("p",{className:E,children:n}),e.jsx("a",{href:"#",onClick:e=>{e.preventDefault(),m(!0),document.body.style.overflow="hidden"},className:H,children:"Read More"})]})]}),e.jsx(he,{isOpen:d,onClose:()=>{m(!1),document.body.style.overflow="auto"},title:i,subtitle:c,image:a,features:o,applications:l})]})},pe="_sectionContainer_wp2xd_1",ue="_imageContainer_wp2xd_14",fe="_sectionImage_wp2xd_21",xe="_contentContainer_wp2xd_40",ge="_headerContainer_wp2xd_50",ve="_sectionTitle_wp2xd_60",je="_sectionDate_wp2xd_71",ye="_descriptionContainer_wp2xd_81",be="_sectionDescription_wp2xd_91",Ne="_readMoreLink_wp2xd_102",Ce=({image:a,title:i,date:t,description:n,readMoreLink:r,features:o=[],applications:l="",subtitle:c=""})=>{const[d,m]=s.useState(!1);return e.jsxs("section",{className:pe,children:[e.jsxs("div",{className:xe,children:[e.jsxs("div",{className:ge,children:[e.jsx("h3",{className:ve,children:i}),t&&e.jsx("div",{className:je,children:t})]}),e.jsxs("div",{className:ye,children:[e.jsx("p",{className:be,children:n}),e.jsx("a",{href:"#",onClick:e=>{e.preventDefault(),m(!0),document.body.style.overflow="hidden"},className:Ne,children:"Read More"})]})]}),e.jsx("div",{className:ue,children:e.jsx("img",{src:a,alt:i,className:fe})}),e.jsx(he,{isOpen:d,onClose:()=>{m(!1),document.body.style.overflow="auto"},title:i,subtitle:c,image:a,features:o,applications:l})]})},we="_container_d5zon_1",Se="_titleContainer_d5zon_15",Te="_title_d5zon_15",ze="_titleText_d5zon_28",ke="_titleHighlight_d5zon_32",De="_contentContainer_d5zon_36",Pe="_description_d5zon_46",qe="_descriptionText_d5zon_56",Re="_descriptionHighlight_d5zon_60",Me="_descriptionCallout_d5zon_64",Ie="_button_d5zon_68",Be=()=>e.jsxs("div",{className:we,children:[e.jsx("div",{className:Se,children:e.jsxs("h2",{className:Te,children:[e.jsx("span",{className:ze,children:"Looking to elevate your research with "}),e.jsx("span",{className:ke,children:"ResearchSat"}),e.jsx("span",{className:ze,children:" Missions?"})]})}),e.jsxs("div",{className:De,children:[e.jsxs("div",{className:Pe,children:[e.jsx("span",{className:qe,children:"At "}),e.jsx("span",{className:Re,children:"ResearchSat"}),e.jsxs("span",{className:qe,children:[", we provide a variety of mission types tailored to your research needs, ensuring the right balance of time and environment to glean the insights your experiments require.",e.jsx("br",{}),e.jsx("br",{})]}),e.jsx("span",{className:Me,children:"Schedule a call now—we know you've got a packed schedule!"})]}),e.jsx(a,{to:"/missions",className:Ie,children:"Discover our Missions"})]})]}),Le="_payloadsPage_w3fhv_1",Oe="_payloadTypesWrapper_w3fhv_6",Ae="_payloadTypesHeader_w3fhv_12",Ee="_trustWallLabel_w3fhv_17",He="_sectionTitle_w3fhv_23",Ge=()=>{s.useEffect((()=>{window.scrollTo(0,0)}),[]);return e.jsxs("div",{className:Le,children:[e.jsx(i,{title:"Space Biology Research Payloads",description:"Explore ResearchSat's custom payload solutions for space biology research, designed to meet the specific requirements of your experiments in microgravity.",keywords:["space payloads","microgravity research","biology payloads","custom payloads","space biology","research payloads"],ogImage:r,structuredData:{"@context":"https://schema.org","@type":"Service",name:"Space Biology Research Payloads",description:"ResearchSat empowers your space biology research with custom payload solutions designed for optimal performance in microgravity environments.",provider:{"@type":"Organization",name:"ResearchSat",url:"https://researchsat.space"},serviceType:"Space Research Payloads",areaServed:"Worldwide",offers:{"@type":"Offer",description:"Custom payload solutions for space biology research"}}}),e.jsx(b,{}),e.jsx(k,{}),e.jsxs("div",{className:Oe,children:[e.jsxs("div",{className:Ae,children:[e.jsx("div",{className:Ee,children:"_Payloads"}),e.jsx("h2",{className:He,children:"Our Payload Solutions"})]}),e.jsx(_e,{image:"/assets/png/unsplash-s-3-h-qu-5-yjg0-Cm-1-Byn.png",title:"Microbe Culture Payload",subtitle:"Advanced Solutions for Microbial Research in Space",date:"",description:"Designed for studying microbial growth and behavior in microgravity, with applications in antimicrobial resistance research and pharmaceutical development.",features:["Multiple culture chambers for parallel experiments","Automated nutrient delivery system for consistent growth conditions","Real-time growth monitoring and data collection"],applications:"Antimicrobial resistance research, pharmaceutical development, microbial adaptation studies, biofilm formation analysis"}),e.jsx(Ce,{image:"/assets/png/unsplash-s-3-h-qu-5-yjg1-D3Ynn7Ai.png",title:"Protein Crystallization Payload",subtitle:"High-Quality Crystal Growth in Microgravity",date:"",description:"Optimized for growing high-quality protein crystals in microgravity, enabling more detailed structural analysis for drug discovery and development.",features:["Precise temperature control (±0.5°C) for optimal crystallization conditions","Multiple crystallization methods supported in a single payload","Advanced vibration isolation system to prevent crystal disruption"],applications:"Drug discovery, structural biology research, enzyme engineering, pharmaceutical development"}),e.jsx(_e,{image:"/assets/png/unsplash-s-3-h-qu-5-yjg0-Cm-1-Byn.png",title:"Cell Culture Payload",subtitle:"Mammalian Cell Research in Space",date:"",description:"Specialized for mammalian cell culture experiments in space, supporting research in tissue engineering, cancer biology, and regenerative medicine.",features:["Controlled gas exchange system for maintaining optimal cell environment","Automated media exchange to support long-duration experiments","Live cell imaging capability for real-time observation"],applications:"Tissue engineering, cancer research, stem cell studies, regenerative medicine, drug testing"}),e.jsx(Ce,{image:"/assets/png/unsplash-s-3-h-qu-5-yjg0-WZ6zslGP.png",title:"Double Emulsion Payload",subtitle:"Advanced Drug Delivery Research Platform",date:"",description:"Custom-designed for studying double emulsion formation in microgravity, with applications in drug delivery system development and material science.",features:["Precision fluid handling system for controlled emulsion formation","High-resolution imaging for detailed structural analysis","Temperature-controlled environment for stability studies"],applications:"Drug delivery system development, encapsulation technology, controlled release formulations, material science research"})]}),e.jsx(P,{}),e.jsx(Be,{}),e.jsx(n,{})]})};export{Ge as default};
