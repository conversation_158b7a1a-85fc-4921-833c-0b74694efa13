import{j as s}from"./main-iJYbDIKL.js";import{b as e}from"./vendor-react-DkG9wxl6.js";import{S as i}from"./SEO-B3bIlZbv.js";import{S as a}from"./SectionDivider-B14-s4wf.js";import{m as n,a as t,b as l,S as c}from"./mission_3-DRuGaD7I.js";import{B as o}from"./BookMission-CLg2k6W0.js";import{C as r}from"./CalButton-BDenXGt5.js";import"./vendor-utils-DOb1KAbh.js";const d={lightboxOverlay:"_lightboxOverlay_akojd_2",lightboxContainer:"_lightboxContainer_akojd_27",closeButton:"_closeButton_akojd_50",lightboxContent:"_lightboxContent_akojd_75",imageSection:"_imageSection_akojd_83",missionImage:"_missionImage_akojd_89",missionBadge:"_missionBadge_akojd_96",missionNumber:"_missionNumber_akojd_109",missionIcon:"_missionIcon_akojd_117",contentSection:"_contentSection_akojd_122",headerContainer:"_headerContainer_akojd_132",missionTitle:"_missionTitle_akojd_136",missionSubtitle:"_missionSubtitle_akojd_149",missionDate:"_missionDate_akojd_158",divider:"_divider_akojd_168",detailsContainer:"_detailsContainer_akojd_175",sectionTitle:"_sectionTitle_akojd_181",overviewText:"_overviewText_akojd_190",significanceText:"_significanceText_akojd_191",detailsList:"_detailsList_akojd_200",detailItem:"_detailItem_akojd_209",detailIcon:"_detailIcon_akojd_220",buttonsContainer:"_buttonsContainer_akojd_227",consultationButton:"_consultationButton_akojd_235",backButton:"_backButton_akojd_265"},m=({isOpen:i,onClose:a,missionData:n})=>{const t=e.useRef(null);return e.useEffect((()=>{const s=s=>{t.current&&!t.current.contains(s.target)&&a()},e=s=>{"Escape"===s.key&&a()};return i&&(document.addEventListener("mousedown",s),document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("mousedown",s),document.removeEventListener("keydown",e),document.body.style.overflow="auto"}}),[i,a]),i&&n?s.jsx("div",{className:d.lightboxOverlay,onClick:a,children:s.jsxs("div",{className:d.lightboxContainer,ref:t,onClick:s=>s.stopPropagation(),children:[s.jsx("button",{className:d.closeButton,onClick:a,children:"×"}),s.jsxs("div",{className:d.lightboxContent,children:[s.jsxs("div",{className:d.imageSection,children:[s.jsx("img",{src:n.image,alt:n.title,className:d.missionImage}),s.jsxs("div",{className:d.missionBadge,children:[s.jsx("span",{className:d.missionNumber,children:String(n.id).padStart(2,"0")}),s.jsx("span",{className:d.missionIcon,children:"🚀"})]})]}),s.jsxs("div",{className:d.contentSection,children:[s.jsxs("div",{className:d.headerContainer,children:[s.jsx("h2",{className:d.missionTitle,children:n.title}),s.jsx("p",{className:d.missionSubtitle,children:n.subtitle}),s.jsx("p",{className:d.missionDate,children:n.date})]}),s.jsx("hr",{className:d.divider}),s.jsxs("div",{className:d.detailsContainer,children:[s.jsxs("div",{className:d.overviewSection,children:[s.jsx("h3",{className:d.sectionTitle,children:"Mission Overview"}),s.jsx("p",{className:d.overviewText,children:n.overview})]}),s.jsxs("div",{className:d.launchDetailsSection,children:[s.jsx("h3",{className:d.sectionTitle,children:"Launch Details"}),s.jsx("ul",{className:d.detailsList,children:n.launchDetails.map(((e,i)=>s.jsxs("li",{className:d.detailItem,children:[s.jsx("span",{className:d.detailIcon,children:"✓"}),e]},i)))})]}),s.jsxs("div",{className:d.specsSection,children:[s.jsx("h3",{className:d.sectionTitle,children:"Mission Specifications"}),s.jsx("ul",{className:d.detailsList,children:n.missionSpecs.map(((e,i)=>s.jsxs("li",{className:d.detailItem,children:[s.jsx("span",{className:d.detailIcon,children:"📊"}),e]},i)))})]}),s.jsxs("div",{className:d.resultsSection,children:[s.jsx("h3",{className:d.sectionTitle,children:"Mission Results"}),s.jsx("ul",{className:d.detailsList,children:n.results.map(((e,i)=>s.jsxs("li",{className:d.detailItem,children:[s.jsx("span",{className:d.detailIcon,children:"🎯"}),e]},i)))})]}),s.jsxs("div",{className:d.significanceSection,children:[s.jsx("h3",{className:d.sectionTitle,children:"Mission Significance"}),s.jsx("p",{className:d.significanceText,children:n.significance})]})]}),s.jsxs("div",{className:d.buttonsContainer,children:[s.jsx(r,{className:d.consultationButton,calLink:"researchsat-2023/30min",namespace:"30min",config:{layout:"month_view"},onCalOpen:a,children:"Schedule Consultation"}),s.jsx("button",{className:d.backButton,onClick:a,children:"Back to Missions"})]})]})]})]})}):null},h="_pastMissionsPage_mbsu3_3",u="_heroSection_mbsu3_10",p="_heroBackground_mbsu3_20",x="_gradientOverlay_mbsu3_31",_="_contentContainer_mbsu3_41",v="_heroContent_mbsu3_53",j="_pastMissionsLabel_mbsu3_57",b="_heroTitle_mbsu3_68",g="_bottomContainer_mbsu3_77",N="_descriptionContainer_mbsu3_86",y="_descriptionText_mbsu3_90",S="_secondSection_mbsu3_100",f="_sectionContainer_mbsu3_105",k="_sectionTitle_mbsu3_111",C="_grayText_mbsu3_121",w="_redText_mbsu3_125",A="_lightText_mbsu3_129",M="_missionTypesWrapper_mbsu3_134",L="_missionTypesHeader_mbsu3_139",D="_trustWallLabel_mbsu3_148",T="_missionSection_mbsu3_160",E="_missionContent_mbsu3_166",I="_missionImageContainer_mbsu3_174",B="_missionImage_mbsu3_174",O="_missionTextContent_mbsu3_193",F="_missionTitle_mbsu3_201",R="_missionSubtitle_mbsu3_210",U="_missionDescription_mbsu3_220",P="_missionHighlights_mbsu3_229",H="_highlight_mbsu3_236",Y="_highlightLabel_mbsu3_242",z="_highlightValue_mbsu3_250",q="_learnMoreButton_mbsu3_260",G="_footerMargin_mbsu3_296",W=()=>{const[r,d]=e.useState(!1),[W,X]=e.useState(null);e.useEffect((()=>{document.title="Past Missions | ResearchSat",window.scrollTo(0,0)}),[]);const V={1:{id:1,title:"Atmospheric Space Mission Launch",subtitle:"UDAAN Yeast Experiment",date:"February 2022 | Adelaide",image:n,overview:"Our first atmospheric mission successfully demonstrated ResearchSat's capability to deliver functional experimental systems in space environments. The UDAAN payload carried yeast experiments to an altitude of 25km, providing valuable insights into microgravity effects on biological systems.",launchDetails:["Launched in February 2022","ResearchSat payload: UDAAN [Yeast Experiment]","Launch provider: UP&UP - High Altitude Balloon","Launch site: Mount Barker, SA"],missionSpecs:["Flight Duration: Approx 60 secs of microgravity","Flight Altitude: max altitude is 25 km","Microgravity level: Free fall","Environment: -20°C & 0.103 atm"],results:["SUCCESSFUL LAUNCH","Flow Rate Experiment: Observed decreased flow rate","Yeast Experiment: yeast undergoes random budding rather than bipolar budding","Increased cell clumping observed"],significance:"This mission validated our payload design approaches and established our capability to execute space-based biological research."},2:{id:2,title:"Suborbital Space Mission Launch 01",subtitle:"ADI-Alpha Advanced Research",date:"November 2022 | Esrange Space Center, Sweden",image:t,overview:"Our breakthrough suborbital mission achieved 6 minutes of high-quality microgravity at 300km altitude. The ADI-Alpha payload demonstrated advanced experimental capabilities including double emulsion formation and significant biological discoveries.",launchDetails:["Mission S1X-3/M15","ResearchSat payload: ADI-Alpha","Launch provider: Swedish Space Corporation (SSC)","Launch site: Esrange Space Center, Sweden"],missionSpecs:["Flight Duration: 6 minutes of microgravity","Flight Altitude: 300 km","Microgravity level: 10^-6 g","Environment: -20°C & 0.103 atm"],results:["SUCCESSFUL LAUNCH","Observed double emulsion formation","Yeast grown 4x times with decreased cell size","100% cell viability maintained","Flow rate experiments showed decreased flow rate"],significance:"This mission provided crucial experience that directly informs our current development programs and demonstrated our ability to achieve complex experimental objectives in space."},3:{id:3,title:"Suborbital Space Mission Launch 02",subtitle:"ADI-Beta Electronics & Cell Bank",date:"February 2023 | Esrange Space Center, Sweden",image:l,overview:"Our most recent suborbital mission showcased advanced electronics payload capabilities and demonstrated our cell bank technology. Multiple microorganisms were successfully transported to space, validating our biological preservation systems.",launchDetails:["Mission S1X-4/M16","ResearchSat payload: ADI-electronica (Beta)","Launch provider: Swedish Space Corporation (SSC)","Launch site: Esrange Space Center, Sweden"],missionSpecs:["Flight Duration: 6 minutes of microgravity","Flight Altitude: 300 km","Microgravity level: 10^-6 g","Environment: -20°C & 0.103 atm"],results:["SUCCESSFUL LAUNCH","Custom Electronics Payload performed flawlessly","Multiple microbes (bacteria, yeast, fungi) carried to space","Cell viability maintained throughout mission","Demonstrated cellbank capability for future missions"],significance:"This mission established our electronics capabilities and validated our cell bank technology, paving the way for more complex biological research missions."}},J=s=>{X(V[s]),d(!0),document.body.style.overflow="hidden"};return s.jsxs(s.Fragment,{children:[s.jsx(i,{title:"Past Missions - Mission Gallery | ResearchSat",description:"Explore ResearchSat's successful space missions including atmospheric balloon launches and suborbital research missions. Detailed mission reports and results from our proven track record.",keywords:["past space missions","mission gallery","space research history","suborbital missions","atmospheric missions","space experiments"],structuredData:{"@context":"https://schema.org","@type":"Organization",name:"ResearchSat Past Missions",description:"Explore ResearchSat's proven track record with detailed mission histories, from atmospheric balloon launches to advanced suborbital research missions.",url:"https://researchsat.space/past-missions"}}),s.jsxs("div",{className:h,children:[s.jsxs("section",{className:u,children:[s.jsx("img",{src:"/assets/png/spacemissionResearch-DpF5_Lrp.png",alt:"Space missions background",className:p}),s.jsx("div",{className:x}),s.jsx("div",{className:_,children:s.jsxs("div",{className:v,children:[s.jsx("div",{className:j,children:"_Past Missions"}),s.jsx("h1",{className:b,children:"Our Proven Track Record in Space"})]})}),s.jsx("div",{className:g,children:s.jsx("div",{className:N,children:s.jsx("p",{className:y,children:"Explore our successful space missions that have validated our technology and demonstrated our capability to deliver groundbreaking research in space environments."})})})]}),s.jsx("section",{className:S,children:s.jsxs("div",{className:f,children:[s.jsx("h2",{className:k,children:"Proven Success in Space Environments"}),s.jsx("div",{className:_,children:s.jsxs("div",{className:y,children:[s.jsxs("span",{className:C,children:["At ",s.jsx("span",{className:w,children:"ResearchSat"}),", we have successfully executed multiple space missions, demonstrating our ability to deliver functional experimental systems in the demanding conditions of space flight.",s.jsx("br",{}),s.jsx("br",{})]}),s.jsx("span",{className:A,children:"Discover the details of our groundbreaking missions and their scientific achievements."})]})})]})}),s.jsx(a,{}),s.jsxs("div",{className:M,children:[s.jsxs("div",{className:L,children:[s.jsx("div",{className:D,children:"_Mission History"}),s.jsx("h2",{className:k,children:"Our Past Missions"})]}),s.jsx("div",{className:T,children:s.jsx("div",{className:f,children:s.jsxs("div",{className:E,children:[s.jsx("div",{className:I,children:s.jsx("img",{src:n,alt:"Atmospheric Mission",className:B})}),s.jsxs("div",{className:O,children:[s.jsx("h2",{className:F,children:"Atmospheric Space Mission Launch"}),s.jsx("h6",{className:R,children:"UDAAN Yeast Experiment - February 2022"}),s.jsx("p",{className:U,children:"Our inaugural atmospheric mission successfully demonstrated ResearchSat's capability to deliver functional experimental systems in space environments. The UDAAN payload carried yeast experiments to 25km altitude, providing valuable insights into microgravity effects on biological systems and validating our payload design approaches."}),s.jsxs("div",{className:P,children:[s.jsxs("div",{className:H,children:[s.jsx("span",{className:Y,children:"Duration:"}),s.jsx("span",{className:z,children:"60 seconds microgravity"})]}),s.jsxs("div",{className:H,children:[s.jsx("span",{className:Y,children:"Altitude:"}),s.jsx("span",{className:z,children:"25 km"})]}),s.jsxs("div",{className:H,children:[s.jsx("span",{className:Y,children:"Status:"}),s.jsx("span",{className:z,children:"Successful Launch"})]})]}),s.jsx("button",{className:q,onClick:()=>J(1),children:"Learn More"})]})]})})}),s.jsx("div",{className:T,children:s.jsx("div",{className:f,children:s.jsxs("div",{className:E,children:[s.jsxs("div",{className:O,children:[s.jsx("h2",{className:F,children:"Suborbital Space Mission Launch 01"}),s.jsx("h6",{className:R,children:"ADI-Alpha Advanced Research - November 2022"}),s.jsx("p",{className:U,children:"Our breakthrough suborbital mission achieved 6 minutes of high-quality microgravity at 300km altitude. The ADI-Alpha payload demonstrated advanced experimental capabilities including double emulsion formation and significant biological discoveries, providing crucial experience for our current development programs."}),s.jsxs("div",{className:P,children:[s.jsxs("div",{className:H,children:[s.jsx("span",{className:Y,children:"Duration:"}),s.jsx("span",{className:z,children:"6 minutes microgravity"})]}),s.jsxs("div",{className:H,children:[s.jsx("span",{className:Y,children:"Altitude:"}),s.jsx("span",{className:z,children:"300 km"})]}),s.jsxs("div",{className:H,children:[s.jsx("span",{className:Y,children:"Achievement:"}),s.jsx("span",{className:z,children:"4x Yeast Growth"})]})]}),s.jsx("button",{className:q,onClick:()=>J(2),children:"Learn More"})]}),s.jsx("div",{className:I,children:s.jsx("img",{src:t,alt:"Suborbital Mission 01",className:B})})]})})}),s.jsx("div",{className:T,children:s.jsx("div",{className:f,children:s.jsxs("div",{className:E,children:[s.jsx("div",{className:I,children:s.jsx("img",{src:l,alt:"Suborbital Mission 02",className:B})}),s.jsxs("div",{className:O,children:[s.jsx("h2",{className:F,children:"Suborbital Space Mission Launch 02"}),s.jsx("h6",{className:R,children:"ADI-Beta Electronics & Cell Bank - February 2023"}),s.jsx("p",{className:U,children:"Our most recent suborbital mission showcased advanced electronics payload capabilities and demonstrated our cell bank technology. Multiple microorganisms were successfully transported to space, validating our biological preservation systems and establishing our electronics capabilities for future missions."}),s.jsxs("div",{className:P,children:[s.jsxs("div",{className:H,children:[s.jsx("span",{className:Y,children:"Duration:"}),s.jsx("span",{className:z,children:"6 minutes microgravity"})]}),s.jsxs("div",{className:H,children:[s.jsx("span",{className:Y,children:"Payload:"}),s.jsx("span",{className:z,children:"Custom Electronics"})]}),s.jsxs("div",{className:H,children:[s.jsx("span",{className:Y,children:"Biology:"}),s.jsx("span",{className:z,children:"Multiple Microbes"})]})]}),s.jsx("button",{className:q,onClick:()=>J(3),children:"Learn More"})]})]})})}),s.jsx("div",{className:G})]}),s.jsx(c,{}),s.jsx(o,{})]}),s.jsx(m,{isOpen:r,onClose:()=>{d(!1),X(null),document.body.style.overflow="auto"},missionData:W})]})};export{W as default};
