import{j as s}from"./main-yODui1Lp.js";import{b as e}from"./vendor-react-DkG9wxl6.js";import{S as a}from"./SEO-CnfzhZqe.js";import"./vendor-utils-DOb1KAbh.js";const i={galleryPage:"_galleryPage_lw04i_2",heroSection:"_heroSection_lw04i_7",heroBackground:"_heroBackground_lw04i_18",gradientOverlay:"_gradientOverlay_lw04i_29",contentContainer:"_contentContainer_lw04i_40",heroContent:"_heroContent_lw04i_48",galleryLabel:"_galleryLabel_lw04i_59",heroTitle:"_heroTitle_lw04i_70",bottomContainer:"_bottomContainer_lw04i_82",descriptionContainer:"_descriptionContainer_lw04i_96",descriptionText:"_descriptionText_lw04i_105",gallerySection:"_gallerySection_lw04i_118",container:"_container_lw04i_127",sectionTitle:"_sectionTitle_lw04i_133",bentoGrid:"_bentoGrid_lw04i_155",bentoItem:"_bentoItem_lw04i_163",hero:"_hero_lw04i_7",large:"_large_lw04i_184",medium:"_medium_lw04i_189",small:"_small_lw04i_194",bentoImage:"_bentoImage_lw04i_199",imageOverlay:"_imageOverlay_lw04i_210",imageCategory:"_imageCategory_lw04i_233",ctaSection:"_ctaSection_lw04i_251",ctaContent:"_ctaContent_lw04i_257",ctaTitle:"_ctaTitle_lw04i_262",ctaDescription:"_ctaDescription_lw04i_270",ctaButtons:"_ctaButtons_lw04i_277",primaryButton:"_primaryButton_lw04i_284",secondaryButton:"_secondaryButton_lw04i_285",footerMargin:"_footerMargin_lw04i_319",slideshowOverlay:"_slideshowOverlay_lw04i_324",slideshowContainer:"_slideshowContainer_lw04i_338",closeButton:"_closeButton_lw04i_349",navButton:"_navButton_lw04i_373",slideshowImageContainer:"_slideshowImageContainer_lw04i_397",slideshowImage:"_slideshowImage_lw04i_397",slideshowInfo:"_slideshowInfo_lw04i_416",slideshowTitle:"_slideshowTitle_lw04i_422",slideshowCounter:"_slideshowCounter_lw04i_429",thumbnailContainer:"_thumbnailContainer_lw04i_435",thumbnail:"_thumbnail_lw04i_435",activeThumbnail:"_activeThumbnail_lw04i_480"},t="/assets/jpeg/galleryhr-jobtMjA1.jpeg",o=()=>{var o,l,n;const[r,c]=e.useState(!1),[p,g]=e.useState(0),[m,h]=e.useState([]);e.useEffect((()=>{window.scrollTo(0,0)}),[]);const d=[{src:"/assets/webp/SubOrbital-Express-3-launch-DvfSFvp1.webp",alt:"SubOrbital Express 3 Launch",position:"hero"},{src:"/assets/png/Figure001-CJa69uqs.png",alt:"Scientific Figure and Data Analysis",position:"large"},{src:"/assets/png/CubeSat_galaxyfixed-cbSQ9347.png",alt:"CubeSat Galaxy Mission",position:"medium"},{src:"/assets/jpg/Mission_Dashboard_Screenshot-qIReIC_j.jpg",alt:"Mission Dashboard Screenshot",position:"small"},{src:"/assets/jpg/20220920_001428-BC8zhB7p.jpg",alt:"Late Night Mission Operations",position:"medium"},{src:"/assets/png/Figure020-CMUWfh9L.png",alt:"Mission Data and Results",position:"small"},{src:"/assets/jpg/20220919_190558-Bz6ulgFL.jpg",alt:"Mission Preparation - September 2022",position:"medium"},{src:"/assets/jpg/20220920_001901-BsKV2bgO.jpg",alt:"Mission Monitoring",position:"small"},{src:"/assets/jpg/Image_%2011022022_19_19_16(1)-Dk5vtvM-.jpg",alt:"Mission Event - November 2022",position:"large"},{src:"/assets/jpg/WhatsApp%20Image%202024-02-27%20at%2011.24.18_2185ba9e-Df-tbQPN.jpg",alt:"Mission Planning - February 2024",position:"small"}],j=[{src:"/assets/jpg/Engineers-Cw9XdDt2.jpg",alt:"Engineering Team",position:"hero"},{src:"/assets/JPG/_DSC7330-D3rDjGKc.JPG",alt:"Professional Photography Session",position:"large"},{src:"/assets/JPG/galimg008-S6pBCvjm.JPG",alt:"Team Collaboration Session",position:"medium"},{src:"/assets/JPG/_DSC7380-DtJIT4pO.JPG",alt:"Professional Team Photography",position:"medium"},{src:"/assets/jpg/20220919_194504-BpZ6nj1H.jpg",alt:"Team Working Session",position:"small"},{src:"/assets/JPG/_DSC7331-DdGH7tiG.JPG",alt:"Team Portrait Session",position:"small"},{src:"/assets/jpg/WhatsApp%20Image%202023-12-08%20at%2023.55.09_6e58b7b8-DjmDhtcD.jpg",alt:"Late Night Work Session",position:"large"},{src:"/assets/jpeg/Image_20221102_232544_334-wBMvgH_3.jpeg",alt:"Team Collaboration",position:"small"},{src:"/assets/jpg/WhatsApp%20Image%202023-12-08%20at%2016.52.08_e4be6a91-Dj_UyL6J.jpg",alt:"Team Communication - December 2023",position:"medium"},{src:"/assets/jpg/IMG_0225-DAPjRRtF.jpg",alt:"Team Member Portrait",position:"small"},{src:"/assets/jpg/IMG_0762-BPLZvABk.jpg",alt:"Team Working",position:"small"},{src:"/assets/jpg/IMG_1517-CT-ff1K_.jpg",alt:"Team Discussion",position:"medium"}],_=[{src:"/assets/jpg/AIC%20_%20RST%20Payloads-DHxf8dwZ.jpg",alt:"AIC & RST Payloads",position:"hero"},{src:"/assets/png/Titanium_Box_croped-blChzu-u.png",alt:"Titanium Payload Container",position:"large"},{src:"/assets/jpg/Microfluidic%20chip%20flowrate%20experiment-CpZlq5pN.jpg",alt:"Microfluidic Chip Flowrate Experiment",position:"large"},{src:"/assets/png/abhrMob-Cq_Nuc05.png",alt:"Mobile Research Platform",position:"medium"},{src:"/assets/jpg/V%202-CQbq74vL.jpg",alt:"Version 2 Development",position:"medium"},{src:"/assets/jpg/20220920_001809-B0BjG7KN.jpg",alt:"Technical Equipment Check",position:"small"},{src:"/assets/jpg/WhatsApp%20Image%202024-02-25%20at%2015.08.34_531215bc-2-_CaYvS.jpg",alt:"Equipment Testing - February 2024",position:"small"},{src:"/assets/jpg/IMG_0787-CD5KJkAc.jpg",alt:"Payload Development",position:"medium"},{src:"/assets/jpg/IMG_1556-CuHBRDYD.jpg",alt:"Payload Testing",position:"small"},{src:"/assets/jpg/IMG_2571-V3CiLVXr.jpg",alt:"Payload Assembly",position:"small"}],u=[{src:"/assets/jpeg/galimg001-zcfT285k.jpeg",alt:"Research Laboratory Setup",position:"hero"},{src:"/assets/png/galimg007-BfAhXeVB.png",alt:"Laboratory Research Process",position:"large"},{src:"/assets/jpeg/Image_20221118_145359_870%20(4)-D1mH3SpO.jpeg",alt:"Research Progress - November 2022",position:"large"},{src:"/assets/png/galimg006-8EOO9NJ4.png",alt:"Space Research Equipment",position:"medium"},{src:"/assets/png/galimg009-BHQpMWmS.png",alt:"Advanced Research Facility",position:"medium"},{src:"/assets/jpg/20220920_001539-Dy4dUyvO.jpg",alt:"Mission Control Center",position:"medium"},{src:"/assets/jpg/P6-SR6v9nNi.jpg",alt:"Project Documentation P6",position:"large"},{src:"/assets/png/IMG_4837-DCcgJr-n.png",alt:"Technical Documentation",position:"medium"},{src:"/assets/jpg/serv1-PscBiVwp.jpg",alt:"Space Services and Operations",position:"medium"},{src:"/assets/jpg/11-CzLR2NDV.jpg",alt:"Research Equipment Setup",position:"small"},{src:"/assets/JPG/DSC00382-Y-8-CXji.JPG",alt:"Professional Mission Photography",position:"small"},{src:"/assets/jpg/IMG_6984-D1SuAj5k.jpg",alt:"Research Facility",position:"small"},{src:"/assets/jpg/P1-Cf7ehPs9.jpg",alt:"Project Documentation P1",position:"small"},{src:"/assets/jpg/P23-CHVF68Jy.jpg",alt:"Project Documentation P23",position:"small"},{src:"/assets/jpg/WhatsApp%20Image%202023-12-08%20at%2016.53.24_6b5e22fa-Xfi9ZJZv.jpg",alt:"Project Updates - December 2023",position:"small"},{src:"/assets/jpg/WhatsApp%20Image%202024-02-25%20at%2015.08.07_a9e86f05-BFx3KucJ.jpg",alt:"Research Progress - February 2024",position:"medium"},{src:"/assets/jpg/WhatsApp%20Image%202024-02-25%20at%2015.08.44_27d716e0-B2hDLcJU.jpg",alt:"Technical Setup - February 2024",position:"small"},{src:"/assets/jpg/WhatsApp%20Image%202024-08-02%20at%2017.05.54_60d4f3d5-CCtqmC0K.jpg",alt:"Recent Developments - August 2024",position:"large"},{src:"/assets/png/image009-DfPmozn3.png",alt:"Technical Documentation Image",position:"small"},{src:"/assets/jpg/image010-XyBbzwPD.jpg",alt:"Research Documentation",position:"medium"},{src:"/assets/jpg/galimg002-vmd_Z2Dr.jpg",alt:"Laboratory Equipment",position:"medium"},{src:"/assets/jpg/galimg003-BGxLfzfE.jpg",alt:"Research Setup",position:"small"},{src:"/assets/jpg/galimg004-CiZScLwa.jpg",alt:"Technical Work",position:"small"},{src:"/assets/jpg/galimg005-DcGT6IRS.jpg",alt:"Research Process",position:"medium"},{src:"/assets/jpg/IMG_1536--Nu1if_Z.jpg",alt:"Research Activity",position:"small"},{src:"/assets/jpg/IMG_3282-BPAIgG-F.jpg",alt:"Laboratory Work",position:"small"},{src:"/assets/jpg/IMG_3356%20(1)-CMhLnUnI.jpg",alt:"Technical Setup",position:"medium"},{src:"/assets/jpg/IMG_4752-D-ZRT1-Z.jpg",alt:"Research Documentation",position:"small"},{src:"/assets/jpg/IMG_4950-CfMSTs-9.jpg",alt:"Laboratory Process",position:"medium"},{src:"/assets/jpg/IMG_6948%20(1)-GDspsSxw.jpg",alt:"Research Equipment",position:"small"},{src:"/assets/jpg/IMG_6949-BjT2sxT1.jpg",alt:"Technical Work",position:"small"}];e.useEffect((()=>{const s=[...d,...j,..._,...u];h(s)}),[]);const y=e.useCallback(((s,e,a=0)=>{g(a+s),c(!0),document.body.style.overflow="hidden"}),[]),x=e.useCallback((()=>{c(!1),document.body.style.overflow="unset"}),[]),v=e.useCallback((()=>{g((s=>(s+1)%m.length))}),[m.length]),w=e.useCallback((()=>{g((s=>(s-1+m.length)%m.length))}),[m.length]);e.useEffect((()=>{const s=s=>{if(r)switch(s.key){case"ArrowRight":v();break;case"ArrowLeft":w();break;case"Escape":x()}};return document.addEventListener("keydown",s),()=>document.removeEventListener("keydown",s)}),[r,v,w,x]);const b=s=>{switch(s){case"mission":default:return 0;case"team":return d.length;case"payload":return d.length+j.length;case"activity":return d.length+j.length+_.length}};return s.jsxs(s.Fragment,{children:[s.jsx(a,{title:"Gallery - Space Biology Research Images",description:"Explore ResearchSat's comprehensive gallery showcasing space missions, research team, cutting-edge payloads, and groundbreaking microgravity research activities.",keywords:["space research gallery","microgravity experiments photos","satellite missions images","space biology photos","research team gallery","space technology images","orbital research pictures","space laboratory photos"],canonical:"https://researchsat.space/gallery",structuredData:{"@context":"https://schema.org","@type":"ImageGallery",name:"ResearchSat Gallery - Space Biology Research Images",description:"Explore our comprehensive gallery showcasing space missions, research team, payloads, and scientific activities in microgravity research.",url:"https://researchsat.space/gallery",publisher:{"@type":"Organization",name:"ResearchSat"},image:["https://researchsat.space/src/assets/images/gallery/galleryhr.jpeg"]},breadcrumbs:[{name:"Home",url:"https://researchsat.space"},{name:"Gallery",url:"https://researchsat.space/gallery"}],ogType:"website",ogImage:t}),s.jsxs("div",{className:i.galleryPage,children:[s.jsxs("section",{className:i.heroSection,children:[s.jsx("img",{src:t,alt:"Space background",className:i.heroBackground}),s.jsx("div",{className:i.gradientOverlay}),s.jsx("div",{className:i.contentContainer,children:s.jsxs("div",{className:i.heroContent,children:[s.jsx("div",{className:i.galleryLabel,children:"_Gallery"}),s.jsx("h1",{className:i.heroTitle,children:"Discover our world through images"})]})}),s.jsx("div",{className:i.bottomContainer,children:s.jsxs("div",{className:i.descriptionContainer,children:[s.jsx("p",{className:i.descriptionText,children:"Explore our journey through space research, showcasing past missions, our dedicated team, and groundbreaking activities."}),s.jsx("div",{style:{textAlign:"right"},children:s.jsx("a",{href:"/book-mission",style:{textDecoration:"none"},children:s.jsx("span",{style:{background:"linear-gradient(180deg, #BBDED0 0%, #87C2AA 100%)",WebkitBackgroundClip:"text",backgroundClip:"text",WebkitTextFillColor:"transparent",fontSize:"16px",fontWeight:500,lineHeight:"120%",letterSpacing:"0.25px",fontFamily:"Poppins, sans-serif"},children:"...explore our missions"})})})]})})]}),s.jsx("section",{className:i.gallerySection,children:s.jsxs("div",{className:i.container,children:[s.jsx("h2",{className:i.sectionTitle,children:"Space Missions"}),s.jsx("div",{className:i.bentoGrid,children:d.map(((e,a)=>s.jsxs("div",{className:`${i.bentoItem} ${i[e.position]}`,onClick:()=>y(a,d,b("mission")),children:[s.jsx("img",{src:e.src,alt:e.alt,className:i.bentoImage,loading:"lazy"}),s.jsx("div",{className:i.imageOverlay,children:s.jsx("span",{className:i.imageCategory,children:"Mission"})})]},`mission-${a}`)))})]})}),s.jsx("section",{className:i.gallerySection,children:s.jsxs("div",{className:i.container,children:[s.jsx("h2",{className:i.sectionTitle,children:"Our Team"}),s.jsx("div",{className:i.bentoGrid,children:j.map(((e,a)=>s.jsxs("div",{className:`${i.bentoItem} ${i[e.position]}`,onClick:()=>y(a,j,b("team")),children:[s.jsx("img",{src:e.src,alt:e.alt,className:i.bentoImage,loading:"lazy"}),s.jsx("div",{className:i.imageOverlay,children:s.jsx("span",{className:i.imageCategory,children:"Team"})})]},`team-${a}`)))})]})}),s.jsx("section",{className:i.gallerySection,children:s.jsxs("div",{className:i.container,children:[s.jsx("h2",{className:i.sectionTitle,children:"Payloads & Equipment"}),s.jsx("div",{className:i.bentoGrid,children:_.map(((e,a)=>s.jsxs("div",{className:`${i.bentoItem} ${i[e.position]}`,onClick:()=>y(a,_,b("payload")),children:[s.jsx("img",{src:e.src,alt:e.alt,className:i.bentoImage,loading:"lazy"}),s.jsx("div",{className:i.imageOverlay,children:s.jsx("span",{className:i.imageCategory,children:"Payload"})})]},`payload-${a}`)))})]})}),s.jsx("section",{className:i.gallerySection,children:s.jsxs("div",{className:i.container,children:[s.jsx("h2",{className:i.sectionTitle,children:"Research Activities"}),s.jsx("div",{className:i.bentoGrid,children:u.map(((e,a)=>s.jsxs("div",{className:`${i.bentoItem} ${i[e.position]}`,onClick:()=>y(a,u,b("activity")),children:[s.jsx("img",{src:e.src,alt:e.alt,className:i.bentoImage,loading:"lazy"}),s.jsx("div",{className:i.imageOverlay,children:s.jsx("span",{className:i.imageCategory,children:"Activity"})})]},`activity-${a}`)))})]})}),s.jsx("section",{className:i.ctaSection,children:s.jsx("div",{className:i.container,children:s.jsxs("div",{className:i.ctaContent,children:[s.jsx("h2",{className:i.ctaTitle,children:"Ready to be part of our story?"}),s.jsx("p",{className:i.ctaDescription,children:"Join us in advancing space research and be featured in our next gallery showcase."}),s.jsxs("div",{className:i.ctaButtons,children:[s.jsx("a",{href:"/book-mission",className:i.primaryButton,children:"Book Mission"}),s.jsx("a",{href:"/contact",className:i.secondaryButton,children:"Contact Us"})]})]})})}),s.jsx("div",{className:i.footerMargin}),r&&m.length>0&&s.jsx("div",{className:i.slideshowOverlay,onClick:x,children:s.jsxs("div",{className:i.slideshowContainer,onClick:s=>s.stopPropagation(),children:[s.jsx("button",{className:i.closeButton,onClick:x,children:s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M18 6L6 18M6 6L18 18",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),s.jsx("button",{className:i.navButton,onClick:w,style:{left:"20px"},children:s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),s.jsx("button",{className:i.navButton,onClick:v,style:{right:"20px"},children:s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),s.jsx("div",{className:i.slideshowImageContainer,children:s.jsx("img",{src:null==(o=m[p])?void 0:o.src,alt:null==(l=m[p])?void 0:l.alt,className:i.slideshowImage})}),s.jsxs("div",{className:i.slideshowInfo,children:[s.jsx("h3",{className:i.slideshowTitle,children:null==(n=m[p])?void 0:n.alt}),s.jsxs("p",{className:i.slideshowCounter,children:[p+1," of ",m.length]})]}),s.jsx("div",{className:i.thumbnailContainer,children:m.map(((e,a)=>s.jsx("div",{className:`${i.thumbnail} ${a===p?i.activeThumbnail:""}`,onClick:()=>g(a),children:s.jsx("img",{src:e.src,alt:e.alt})},a)))})]})})]})]})};export{o as default};
