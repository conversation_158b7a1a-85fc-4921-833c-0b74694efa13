import{j as e,H as t}from"./main-iJYbDIKL.js";import"./vendor-react-DkG9wxl6.js";const o=({title:o,description:a,keywords:n=[],ogImage:s="/src/assets/images/og-image.jpg",ogType:i="website",canonical:r,structuredData:c,author:m="ResearchSat Team",publishedAt:p,modifiedAt:l,noindex:d=!1})=>{const j="ResearchSat",x=o?`${o} | ${j}`:j,g=a||"ResearchSat provides microgravity research solutions for space biology research, offering custom satellite payloads and mission services.",y=["space biology","microgravity research","satellite payloads","space missions",...n].join(", "),h=r||("undefined"!=typeof window?window.location.href:"");return e.jsxs(t,{children:[e.jsx("title",{children:x}),e.jsx("meta",{name:"description",content:g}),e.jsx("meta",{name:"keywords",content:y}),e.jsx("link",{rel:"canonical",href:h}),e.jsx("meta",{property:"og:type",content:i}),e.jsx("meta",{property:"og:url",content:h}),e.jsx("meta",{property:"og:title",content:x}),e.jsx("meta",{property:"og:description",content:g}),e.jsx("meta",{property:"og:image",content:s}),e.jsx("meta",{property:"og:site_name",content:j}),e.jsx("meta",{name:"twitter:card",content:"summary_large_image"}),e.jsx("meta",{name:"twitter:title",content:x}),e.jsx("meta",{name:"twitter:description",content:g}),e.jsx("meta",{name:"twitter:image",content:s}),e.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1, shrink-to-fit=no"}),e.jsx("meta",{name:"author",content:m}),e.jsx("meta",{name:"robots",content:d?"noindex, nofollow":"index, follow"}),e.jsx("meta",{name:"theme-color",content:"#17242D"}),e.jsx("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),e.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"black"}),p&&e.jsx("meta",{property:"article:published_time",content:p}),l&&e.jsx("meta",{property:"article:modified_time",content:l}),c&&e.jsx("script",{type:"application/ld+json",children:JSON.stringify(c)}),e.jsx("html",{lang:"en"})]})};export{o as S};
