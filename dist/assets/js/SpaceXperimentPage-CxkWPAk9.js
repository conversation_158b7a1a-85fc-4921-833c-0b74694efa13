import{j as e,H as t}from"./main-iJYbDIKL.js";import{b as s}from"./vendor-react-DkG9wxl6.js";import{s as a}from"./spacexperiment-email-DxDf5037.js";import"./vendor-utils-DOb1KAbh.js";const i={spaceXperimentPage:"_spaceXperimentPage_1tmto_3",notification:"_notification_1tmto_10",slideIn:"_slideIn_1tmto_1",success:"_success_1tmto_25",error:"_error_1tmto_31",info:"_info_1tmto_37",heroSection:"_heroSection_1tmto_55",heroContent:"_heroContent_1tmto_75",heroTitle:"_heroTitle_1tmto_83",heroSubtitle:"_heroSubtitle_1tmto_96",heroStats:"_heroStats_1tmto_106",stat:"_stat_1tmto_113",statNumber:"_statNumber_1tmto_117",statLabel:"_statLabel_1tmto_127",progressSection:"_progressSection_1tmto_137",container:"_container_1tmto_143",progressBar:"_progressBar_1tmto_149",progressStep:"_progressStep_1tmto_169",stepNumber:"_stepNumber_1tmto_177",active:"_active_1tmto_194",completed:"_completed_1tmto_200",stepLabel:"_stepLabel_1tmto_206",mainSection:"_mainSection_1tmto_221",formCard:"_formCard_1tmto_226",stepTitle:"_stepTitle_1tmto_236",stepContent:"_stepContent_1tmto_246",stepDescription:"_stepDescription_1tmto_250",experimentGrid:"_experimentGrid_1tmto_261",experimentCard:"_experimentCard_1tmto_267",selected:"_selected_1tmto_283",experimentIcon:"_experimentIcon_1tmto_289",experimentName:"_experimentName_1tmto_295",experimentDescription:"_experimentDescription_1tmto_304",experimentMeta:"_experimentMeta_1tmto_314",duration:"_duration_1tmto_323",complexity:"_complexity_1tmto_330",beginner:"_beginner_1tmto_340",intermediate:"_intermediate_1tmto_345",advanced:"_advanced_1tmto_350",missionGrid:"_missionGrid_1tmto_356",missionCard:"_missionCard_1tmto_362",missionName:"_missionName_1tmto_383",missionDetails:"_missionDetails_1tmto_393",missionDetail:"_missionDetail_1tmto_393",detailLabel:"_detailLabel_1tmto_411",detailValue:"_detailValue_1tmto_418",detailsForm:"_detailsForm_1tmto_426",contactForm:"_contactForm_1tmto_427",formRow:"_formRow_1tmto_432",inputGroup:"_inputGroup_1tmto_439",label:"_label_1tmto_445",input:"_input_1tmto_439",select:"_select_1tmto_283",textarea:"_textarea_1tmto_455",proposalSection:"_proposalSection_1tmto_494",proposalHeader:"_proposalHeader_1tmto_503",proposalTitle:"_proposalTitle_1tmto_512",loadingSpinner:"_loadingSpinner_1tmto_523",pulse:"_pulse_1tmto_1",regenerateButton:"_regenerateButton_1tmto_532",proposalContent:"_proposalContent_1tmto_559",proposalText:"_proposalText_1tmto_568",emailOptionsSection:"_emailOptionsSection_1tmto_580",emailOptionsTitle:"_emailOptionsTitle_1tmto_589",emailOptionsDescription:"_emailOptionsDescription_1tmto_598",emailOptionsButtons:"_emailOptionsButtons_1tmto_609",emailButton:"_emailButton_1tmto_616",copyButton:"_copyButton_1tmto_617",emailInstructions:"_emailInstructions_1tmto_642",summarySection:"_summarySection_1tmto_670",summaryTitle:"_summaryTitle_1tmto_678",summaryContent:"_summaryContent_1tmto_686",summaryItem:"_summaryItem_1tmto_692",summaryLabel:"_summaryLabel_1tmto_704",summaryValue:"_summaryValue_1tmto_711",navigationButtons:"_navigationButtons_1tmto_719",previousButton:"_previousButton_1tmto_728",nextButton:"_nextButton_1tmto_729",submitButton:"_submitButton_1tmto_730"},n=()=>{var n,o;const[r,l]=s.useState(""),[c,m]=s.useState(""),[d,p]=s.useState(""),[u,h]=s.useState(""),[_,x]=s.useState({name:"",email:"",organization:"",phone:""}),[j,y]=s.useState(1),[v,N]=s.useState({message:"",type:"",show:!1}),[b,g]=s.useState(""),[S,f]=s.useState(!1),[C,w]=s.useState(!1),[P,D]=s.useState(!1),[$,T]=s.useState(!1),[k,B]=s.useState(""),[L,I]=s.useState(null);s.useEffect((()=>{document.title="SpaceXperiment - Book Your Space Research | ResearchSat",window.scrollTo(0,0)}),[]);const E=[{id:"protein-crystallization",name:"Protein Crystallization",icon:"🧬",description:"Grow high-quality protein crystals in microgravity for pharmaceutical research",duration:"2-14 days",complexity:"Advanced"},{id:"cell-culture",name:"Cell Culture Studies",icon:"🔬",description:"Study cellular behavior and tissue engineering in space environment",duration:"1-7 days",complexity:"Intermediate"},{id:"material-science",name:"Material Science",icon:"⚛️",description:"Investigate material properties and crystal formation in microgravity",duration:"3-21 days",complexity:"Advanced"},{id:"plant-biology",name:"Plant Biology",icon:"🌱",description:"Research plant growth and adaptation in space conditions",duration:"7-30 days",complexity:"Beginner"},{id:"fluid-physics",name:"Fluid Physics",icon:"💧",description:"Study fluid dynamics and mixing processes in zero gravity",duration:"1-5 days",complexity:"Intermediate"},{id:"combustion",name:"Combustion Research",icon:"🔥",description:"Analyze combustion processes and flame behavior in microgravity",duration:"1-3 days",complexity:"Advanced"}],G=[{id:"suborbital",name:"Sub-orbital Flight",duration:"3-5 minutes",altitude:"100+ km",microgravity:"3-5 minutes",cost:"$$$"},{id:"orbital",name:"Low Earth Orbit",duration:"90 minutes - 30 days",altitude:"400-500 km",microgravity:"Continuous",cost:"$$$$"},{id:"iss",name:"International Space Station",duration:"1-6 months",altitude:"408 km",microgravity:"Long-term",cost:"$$$$$"}],O=(e,t="success")=>{N({message:e,type:t,show:!0}),setTimeout((()=>{N({message:"",type:"",show:!1})}),5e3)},R=async()=>{f(!0),w(!0),g("Generating your project proposal..."),setTimeout((()=>{var e,t;try{const s=E.find((e=>e.id===r)),a=G.find((e=>e.id===c)),i=`# SpaceXperiment Project Proposal\n\n## Executive Summary\nThis proposal outlines a ${(null==s?void 0:s.name)||"space research"} experiment using the ${(null==a?void 0:a.name)||"selected mission"} platform. The research will leverage microgravity conditions to advance scientific understanding.\n\n## Research Objectives\n- Conduct ${(null==s?void 0:s.name)||"space research"} in microgravity environment\n- Collect valuable scientific data for analysis\n- Compare results with Earth-based control experiments\n- Contribute to space research knowledge base\n\n## Mission Details\n- Platform: ${(null==a?void 0:a.name)||"Selected mission platform"}\n- Duration: ${d||(null==a?void 0:a.duration)||"To be determined"}\n- Environment: ${(null==a?void 0:a.microgravity)||"Microgravity conditions"}\n\n## Research Goals\n${u||"Research goals to be defined in collaboration with the research team."}\n\n## Technical Requirements\n- Specialized equipment for ${(null==(e=null==s?void 0:s.name)?void 0:e.toLowerCase())||"space research"}\n- Data recording and transmission systems\n- Safety protocols for mission operations\n- Ground support team coordination\n\n## Timeline\n- Phase 1: Preparation and equipment setup (4-6 weeks)\n- Phase 2: Mission execution (${(null==a?void 0:a.duration)||"Mission duration"})\n- Phase 3: Data analysis and reporting (8-12 weeks)\n- Phase 4: Publication and dissemination (4-6 weeks)\n\n## Expected Outcomes\n- Enhanced understanding of ${(null==(t=null==s?void 0:s.description)?void 0:t.toLowerCase())||"space phenomena"}\n- Potential breakthroughs in space-based research methodologies\n- Data that could lead to practical applications in various industries\n- Contribution to the growing body of microgravity research\n\n## Next Steps\nUpon approval, our team will work with you to refine the experimental parameters and prepare for mission execution.\n\nThis proposal will be further developed based on your specific requirements and mission constraints.`;g(i),O("Project proposal generated successfully!","success")}catch(s){g("Error generating proposal. Please try again."),O("Error generating proposal. Please try again.","error")}finally{f(!1)}}),2e3)};return e.jsxs(e.Fragment,{children:[e.jsxs(t,{children:[e.jsx("title",{children:"SpaceXperiment - Book Your Space Research | ResearchSat"}),e.jsx("meta",{name:"description",content:"Design and book your custom space experiment with SpaceXperiment. From protein crystallization to material science - launch your research to new heights."}),e.jsx("meta",{name:"keywords",content:"space experiment, microgravity research, protein crystallization, space biology, material science, ISS research"})]}),e.jsxs("div",{className:i.spaceXperimentPage,children:[v.show&&e.jsx("div",{className:`${i.notification} ${i[v.type]}`,children:v.message}),e.jsx("section",{className:i.heroSection,children:e.jsxs("div",{className:i.heroContent,children:[e.jsx("h1",{className:i.heroTitle,children:"SpaceXperiment"}),e.jsx("p",{className:i.heroSubtitle,children:"Design Your Custom Space Research Mission"}),e.jsxs("div",{className:i.heroStats,children:[e.jsxs("div",{className:i.stat,children:[e.jsx("span",{className:i.statNumber,children:"50+"}),e.jsx("span",{className:i.statLabel,children:"Experiments Launched"})]}),e.jsxs("div",{className:i.stat,children:[e.jsx("span",{className:i.statNumber,children:"15+"}),e.jsx("span",{className:i.statLabel,children:"Research Partners"})]}),e.jsxs("div",{className:i.stat,children:[e.jsx("span",{className:i.statNumber,children:"99%"}),e.jsx("span",{className:i.statLabel,children:"Success Rate"})]})]})]})}),e.jsx("section",{className:i.progressSection,children:e.jsx("div",{className:i.container,children:e.jsx("div",{className:i.progressBar,children:[1,2,3,4].map((t=>e.jsxs("div",{className:`${i.progressStep} ${j>=t?i.active:""} ${j>t?i.completed:""}`,children:[e.jsx("div",{className:i.stepNumber,children:t}),e.jsxs("div",{className:i.stepLabel,children:[1===t&&"Experiment",2===t&&"Mission",3===t&&"Details",4===t&&"Contact"]})]},t)))})})}),e.jsx("section",{className:i.mainSection,children:e.jsx("div",{className:i.container,children:e.jsxs("div",{className:i.formCard,children:[e.jsx("h2",{className:i.stepTitle,children:(()=>{switch(j){case 1:return"Choose Your Experiment";case 2:return"Select Mission Type";case 3:return"Research Details";case 4:return"Contact Information";default:return"SpaceXperiment"}})()}),1===j&&e.jsxs("div",{className:i.stepContent,children:[e.jsx("p",{className:i.stepDescription,children:"Select the type of experiment you want to conduct in space. Each experiment type has different requirements and capabilities."}),e.jsx("div",{className:i.experimentGrid,children:E.map((t=>e.jsxs("div",{className:`${i.experimentCard} ${r===t.id?i.selected:""}`,onClick:()=>l(t.id),children:[e.jsx("div",{className:i.experimentIcon,children:t.icon}),e.jsx("h3",{className:i.experimentName,children:t.name}),e.jsx("p",{className:i.experimentDescription,children:t.description}),e.jsxs("div",{className:i.experimentMeta,children:[e.jsxs("span",{className:i.duration,children:["Duration: ",t.duration]}),e.jsx("span",{className:`${i.complexity} ${i[t.complexity.toLowerCase()]}`,children:t.complexity})]})]},t.id)))})]}),2===j&&e.jsxs("div",{className:i.stepContent,children:[e.jsx("p",{className:i.stepDescription,children:"Choose the mission platform that best suits your experiment requirements and budget."}),e.jsx("div",{className:i.missionGrid,children:G.map((t=>e.jsxs("div",{className:`${i.missionCard} ${c===t.id?i.selected:""}`,onClick:()=>m(t.id),children:[e.jsx("h3",{className:i.missionName,children:t.name}),e.jsxs("div",{className:i.missionDetails,children:[e.jsxs("div",{className:i.missionDetail,children:[e.jsx("span",{className:i.detailLabel,children:"Duration:"}),e.jsx("span",{className:i.detailValue,children:t.duration})]}),e.jsxs("div",{className:i.missionDetail,children:[e.jsx("span",{className:i.detailLabel,children:"Altitude:"}),e.jsx("span",{className:i.detailValue,children:t.altitude})]}),e.jsxs("div",{className:i.missionDetail,children:[e.jsx("span",{className:i.detailLabel,children:"Microgravity:"}),e.jsx("span",{className:i.detailValue,children:t.microgravity})]}),e.jsxs("div",{className:i.missionDetail,children:[e.jsx("span",{className:i.detailLabel,children:"Cost:"}),e.jsx("span",{className:i.detailValue,children:t.cost})]})]})]},t.id)))})]}),3===j&&e.jsxs("div",{className:i.stepContent,children:[e.jsx("p",{className:i.stepDescription,children:"Provide details about your research objectives and experiment duration preferences."}),e.jsxs("div",{className:i.detailsForm,children:[e.jsxs("div",{className:i.inputGroup,children:[e.jsx("label",{className:i.label,children:"Experiment Duration"}),e.jsxs("select",{value:d,onChange:e=>p(e.target.value),className:i.select,children:[e.jsx("option",{value:"",children:"Select duration..."}),e.jsx("option",{value:"1-3-hours",children:"1-3 hours"}),e.jsx("option",{value:"1-day",children:"1 day"}),e.jsx("option",{value:"3-days",children:"3 days"}),e.jsx("option",{value:"1-week",children:"1 week"}),e.jsx("option",{value:"2-weeks",children:"2 weeks"}),e.jsx("option",{value:"1-month",children:"1 month"}),e.jsx("option",{value:"custom",children:"Custom duration"})]})]}),e.jsxs("div",{className:i.inputGroup,children:[e.jsx("label",{className:i.label,children:"Research Goals & Objectives"}),e.jsx("textarea",{value:u,onChange:e=>h(e.target.value),className:i.textarea,rows:"6",placeholder:"Describe your research objectives, expected outcomes, and any specific requirements for your space experiment..."})]})]})]}),4===j&&e.jsxs("div",{className:i.stepContent,children:[e.jsx("p",{className:i.stepDescription,children:"Review your AI-generated project proposal below, then provide your contact information to proceed."}),C&&e.jsxs("div",{className:i.proposalSection,children:[e.jsxs("div",{className:i.proposalHeader,children:[e.jsx("h3",{className:i.proposalTitle,children:S?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:i.loadingSpinner,children:"⚡"}),"Generating Your Project Proposal..."]}):"🚀 Your AI-Generated Project Proposal"}),!S&&e.jsx("button",{onClick:R,className:i.regenerateButton,disabled:S,children:"🔄 Regenerate"})]}),e.jsx("div",{className:i.proposalContent,children:e.jsx("pre",{className:i.proposalText,children:b})})]}),e.jsxs("div",{className:i.contactForm,children:[e.jsxs("div",{className:i.formRow,children:[e.jsxs("div",{className:i.inputGroup,children:[e.jsx("label",{className:i.label,children:"Full Name *"}),e.jsx("input",{type:"text",value:_.name,onChange:e=>x({..._,name:e.target.value}),className:i.input,placeholder:"Your full name"})]}),e.jsxs("div",{className:i.inputGroup,children:[e.jsx("label",{className:i.label,children:"Email Address *"}),e.jsx("input",{type:"email",value:_.email,onChange:e=>x({..._,email:e.target.value}),className:i.input,placeholder:"<EMAIL>"})]})]}),e.jsxs("div",{className:i.formRow,children:[e.jsxs("div",{className:i.inputGroup,children:[e.jsx("label",{className:i.label,children:"Organization"}),e.jsx("input",{type:"text",value:_.organization,onChange:e=>x({..._,organization:e.target.value}),className:i.input,placeholder:"University, Company, or Institution"})]}),e.jsxs("div",{className:i.inputGroup,children:[e.jsx("label",{className:i.label,children:"Phone Number"}),e.jsx("input",{type:"tel",value:_.phone,onChange:e=>x({..._,phone:e.target.value}),className:i.input,placeholder:"+****************"})]})]}),e.jsxs("div",{className:i.summarySection,children:[e.jsx("h3",{className:i.summaryTitle,children:"Proposal Summary"}),e.jsxs("div",{className:i.summaryContent,children:[e.jsxs("div",{className:i.summaryItem,children:[e.jsx("span",{className:i.summaryLabel,children:"Experiment:"}),e.jsx("span",{className:i.summaryValue,children:(null==(n=E.find((e=>e.id===r)))?void 0:n.name)||"Not selected"})]}),e.jsxs("div",{className:i.summaryItem,children:[e.jsx("span",{className:i.summaryLabel,children:"Mission:"}),e.jsx("span",{className:i.summaryValue,children:(null==(o=G.find((e=>e.id===c)))?void 0:o.name)||"Not selected"})]}),e.jsxs("div",{className:i.summaryItem,children:[e.jsx("span",{className:i.summaryLabel,children:"Duration:"}),e.jsx("span",{className:i.summaryValue,children:d||"Not specified"})]})]})]})]}),$&&e.jsxs("div",{className:i.emailOptionsSection,children:[e.jsx("h3",{className:i.emailOptionsTitle,children:"📧 Send Your Proposal"}),e.jsx("p",{className:i.emailOptionsDescription,children:"Choose how you'd like to send your proposal:"}),e.jsxs("div",{className:i.emailOptionsButtons,children:[e.jsx("a",{href:k,className:i.emailButton,target:"_blank",rel:"noopener noreferrer",children:"📧 Open Email Client"}),e.jsx("button",{onClick:()=>{L&&a.copyToClipboard(L)?O("Proposal copied to clipboard!","success"):O("Unable to copy to clipboard","error")},className:i.copyButton,children:"📋 Copy to Clipboard"})]}),e.jsxs("div",{className:i.emailInstructions,children:[e.jsx("p",{children:e.jsx("strong",{children:"Email Instructions:"})}),e.jsxs("ul",{children:[e.jsx("li",{children:'Click "Open Email Client" to compose an email with your proposal'}),e.jsx("li",{children:"Or copy the proposal and paste it into your preferred email application"}),e.jsxs("li",{children:["Send to: ",e.jsx("strong",{children:"<EMAIL>"})]})]})]})]})]}),e.jsxs("div",{className:i.navigationButtons,children:[j>1&&e.jsx("button",{onClick:()=>{j>1&&y(j-1)},className:i.previousButton,children:"← Previous"}),j<4?e.jsx("button",{onClick:async()=>{1!==j||r?2!==j||c?(3===j&&await R(),j<4&&y(j+1)):O("Please select a mission type.","error"):O("Please select an experiment type.","error")},className:i.nextButton,children:"Next →"}):e.jsx("button",{onClick:async()=>{if(_.name.trim())if(_.email.trim()&&/^\S+@\S+\.\S+$/.test(_.email)){D(!0);try{const t=E.find((e=>e.id===r)),s={experiment:t,mission:G.find((e=>e.id===c)),duration:d,researchGoals:u,contactInfo:_,generatedProposal:b,timestamp:(new Date).toISOString()};try{if(!(await a.sendEmails(s)).success)throw new Error("Email sending failed");O(`Thank you ${_.name}! Your SpaceXperiment proposal has been submitted successfully. Confirmation emails have been sent to both you and our team. We'll contact you within 24 hours.`,"success")}catch(e){const t=a.generateMailtoLink(s);O(`Thank you ${_.name}! Your proposal has been prepared. Please click the button below to send it via your email client, or copy the proposal details.`,"info"),T(!0),B(t),I(s)}}catch(t){O("There was an error submitting your proposal. Please try again.","error")}finally{D(!1)}}else O("Please enter a valid email address.","error");else O("Please enter your full name.","error")},className:i.submitButton,disabled:P,children:P?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:i.loadingSpinner,children:"⚡"}),"Submitting..."]}):"Submit Proposal"})]})]})})})]})]})};export{n as default};
