import{j as e,H as t}from"./main-CgIzQBkX.js";import"./vendor-react-DkG9wxl6.js";const a=({title:a,description:n,keywords:o=[],ogImage:s="/src/assets/images/og-image.jpg",ogType:i="website",canonical:r,structuredData:c,author:m="ResearchSat Team",publishedAt:p,modifiedAt:l,noindex:g=!1,locale:d="en_US",alternateLanguages:h=[],breadcrumbs:x=[],faqData:j=[],organizationData:y=null})=>{const w="ResearchSat",f="https://researchsat.space",u=a?`${a} | ${w}`:`${w} - Space Biology & Microgravity Research Solutions`,b=n||"ResearchSat provides cutting-edge microgravity research solutions for space biology, offering custom satellite payloads and mission services worldwide.",S=["space biology research","microgravity research solutions","satellite payloads","space missions","space biotechnology","orbital research","ISS experiments","protein crystallization space","cell culture microgravity","space medicine research",...o].join(", "),v=r||("undefined"!=typeof window?window.location.href:f),k={"@context":"https://schema.org","@type":"Organization",name:"ResearchSat",url:f,logo:`${f}/src/assets/images/new-logo.svg`,description:b,foundingDate:"2020",industry:"Space Technology",address:{"@type":"PostalAddress",addressCountry:"US"},contactPoint:{"@type":"ContactPoint",contactType:"customer service",email:"<EMAIL>"},sameAs:["https://www.linkedin.com/company/researchsat/","https://twitter.com/researchsat","https://www.facebook.com/researchsat/","https://www.instagram.com/researchsat/"]};return e.jsxs(t,{children:[e.jsx("title",{children:u}),e.jsx("meta",{name:"description",content:b}),e.jsx("meta",{name:"keywords",content:S}),e.jsx("link",{rel:"canonical",href:v}),h.map((t=>e.jsx("link",{rel:"alternate",hreflang:t.hreflang,href:t.href},t.hreflang))),e.jsx("meta",{property:"og:type",content:i}),e.jsx("meta",{property:"og:url",content:v}),e.jsx("meta",{property:"og:title",content:u}),e.jsx("meta",{property:"og:description",content:b}),e.jsx("meta",{property:"og:image",content:s}),e.jsx("meta",{property:"og:image:width",content:"1200"}),e.jsx("meta",{property:"og:image:height",content:"630"}),e.jsx("meta",{property:"og:site_name",content:w}),e.jsx("meta",{property:"og:locale",content:d}),e.jsx("meta",{name:"twitter:card",content:"summary_large_image"}),e.jsx("meta",{name:"twitter:site",content:"@researchsat"}),e.jsx("meta",{name:"twitter:creator",content:"@researchsat"}),e.jsx("meta",{name:"twitter:title",content:u}),e.jsx("meta",{name:"twitter:description",content:b}),e.jsx("meta",{name:"twitter:image",content:s}),e.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1, shrink-to-fit=no"}),e.jsx("meta",{name:"author",content:m}),e.jsx("meta",{name:"robots",content:g?"noindex, nofollow":"index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"}),e.jsx("meta",{name:"googlebot",content:"index, follow"}),e.jsx("meta",{name:"bingbot",content:"index, follow"}),e.jsx("meta",{name:"theme-color",content:"#17242D"}),e.jsx("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),e.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"black-translucent"}),e.jsx("meta",{name:"apple-mobile-web-app-title",content:w}),e.jsx("meta",{name:"application-name",content:w}),e.jsx("meta",{name:"msapplication-TileColor",content:"#17242D"}),e.jsx("meta",{name:"msapplication-config",content:"/browserconfig.xml"}),e.jsx("meta",{httpEquiv:"X-UA-Compatible",content:"IE=edge"}),e.jsx("meta",{name:"format-detection",content:"telephone=no"}),e.jsx("meta",{name:"referrer",content:"origin-when-cross-origin"}),p&&e.jsx("meta",{property:"article:published_time",content:p}),l&&e.jsx("meta",{property:"article:modified_time",content:l}),e.jsx("script",{type:"application/ld+json",children:JSON.stringify(y||k)}),c&&e.jsx("script",{type:"application/ld+json",children:JSON.stringify(c)}),x.length>0&&e.jsx("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"BreadcrumbList",itemListElement:x.map(((e,t)=>({"@type":"ListItem",position:t+1,name:e.name,item:e.url})))})}),j.length>0&&e.jsx("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"FAQPage",mainEntity:j.map((e=>({"@type":"Question",name:e.question,acceptedAnswer:{"@type":"Answer",text:e.answer}})))})}),e.jsx("html",{lang:"en"})]})};export{a as S};
