import{j as e}from"./main-CgIzQBkX.js";import{b as s,L as a}from"./vendor-react-DkG9wxl6.js";import"./vendor-utils-DOb1KAbh.js";const i=()=>(s.useEffect((()=>{document.title="Privacy Policy | ResearchSat",window.scrollTo(0,0)}),[]),e.jsxs("div",{className:"privacy-policy-page",children:[e.jsx("header",{id:"header",className:"ex-header",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-md-12",children:e.jsx("h1",{children:"Privacy Policy"})})})})}),e.jsx("div",{className:"container",children:e.jsx("hr",{className:"mt-5 mb-5",style:{borderColor:"#ef3b47"}})}),e.jsx("div",{className:"ex-basic-2",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-lg-10 offset-lg-1",children:[e.jsxs("div",{className:"text-container",children:[e.jsx("h3",{children:"Private Data We Receive And Collect"}),e.jsx("p",{children:"ResearchSat also automatically collects and receives certain information from your computer or mobile device, including the activities you perform on our Website, the Platforms, and the Applications, the type of hardware and software you are using (for example, your operating system or browser), and information obtained from cookies. For example, each time you visit the Website or otherwise use the Services, we automatically collect your IP address, browser and device type, access times, the web page from which you came, the regions from which you navigate the web page, and the web page(s) you access (as applicable)."}),e.jsxs("p",{children:["When you first register for a ResearchSat account, and when you use the Services, we collect some ",e.jsx(a,{className:"turquoise",to:"#your-link",children:"Personal Information"})," about you such as:"]}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-md-6",children:e.jsxs("ul",{className:"list-unstyled li-space-lg indent",children:[e.jsxs("li",{className:"media",children:[e.jsx("i",{className:"fas fa-square"}),e.jsx("div",{className:"media-body",children:"The geographic area where you use your computer and mobile devices"})]}),e.jsxs("li",{className:"media",children:[e.jsx("i",{className:"fas fa-square"}),e.jsx("div",{className:"media-body",children:"Your full name, username, and email address and other contact details"})]}),e.jsxs("li",{className:"media",children:[e.jsx("i",{className:"fas fa-square"}),e.jsx("div",{className:"media-body",children:"A unique ResearchSat user ID (an alphanumeric string) which is assigned to you upon registration"})]}),e.jsxs("li",{className:"media",children:[e.jsx("i",{className:"fas fa-square"}),e.jsx("div",{className:"media-body",children:"Other optional information as part of your account profile"})]}),e.jsxs("li",{className:"media",children:[e.jsx("i",{className:"fas fa-square"}),e.jsx("div",{className:"media-body",children:"Your IP Address and, when applicable, timestamp related to your consent and confirmation of consent"})]}),e.jsxs("li",{className:"media",children:[e.jsx("i",{className:"fas fa-square"}),e.jsx("div",{className:"media-body",children:"Other information submitted by you or your organizational representatives via various methods"})]})]})}),e.jsx("div",{className:"col-md-6",children:e.jsxs("ul",{className:"list-unstyled li-space-lg indent",children:[e.jsxs("li",{className:"media",children:[e.jsx("i",{className:"fas fa-square"}),e.jsx("div",{className:"media-body",children:"Your billing address and any necessary other information to complete any financial transaction, and when making purchases through the Services, we may also collect your credit card or PayPal information"})]}),e.jsxs("li",{className:"media",children:[e.jsx("i",{className:"fas fa-square"}),e.jsx("div",{className:"media-body",children:"User generated content (such as messages, posts, comments, pages, profiles, images, feeds or communications exchanged on the Supported Platforms)"})]}),e.jsxs("li",{className:"media",children:[e.jsx("i",{className:"fas fa-square"}),e.jsx("div",{className:"media-body",children:"Images or other files that you may publish via our Services"})]}),e.jsxs("li",{className:"media",children:[e.jsx("i",{className:"fas fa-square"}),e.jsx("div",{className:"media-body",children:"Information (such as messages, posts, comments, pages, profiles, images) we may receive relating to communications you send us, such as queries or comments concerning"})]})]})})]})]}),e.jsxs("div",{className:"text-container",children:[e.jsx("h3",{children:"How We Use ResearchSat Website Data"}),e.jsx("p",{children:"ResearchSat website uses visitors' data for the following general purposes:"}),e.jsxs("ol",{className:"li-space-lg",children:[e.jsx("li",{children:"To identify you when you login to your account"}),e.jsx("li",{children:"To enable us to operate the Services and provide them to you"}),e.jsx("li",{children:"To verify your transactions and for purchase confirmation, billing, security, and authentication (including security tokens for communication with installed)"}),e.jsx("li",{children:"To analyze the Website or the other Services and information about our visitors and users, including research into our user demographics and user behaviour in order to improve our content and Services"}),e.jsx("li",{children:"To contact you about your account and provide customer service support, including responding to your comments and questions"}),e.jsx("li",{children:"To share aggregate (non-identifiable) statistics about users of the Services to prospective advertisers and partners"}),e.jsx("li",{children:"To keep you informed about the Services, features, surveys, newsletters, offers, surveys, newsletters, offers, contests and events we think you may find useful or which you have requested from us"}),e.jsx("li",{children:"To sell or market ResearchSat products and services to you"}),e.jsx("li",{children:"To better understand your needs and the needs of users in the aggregate, diagnose problems, analyze trends, improve the features and usability of the Services, and better understand and market to our customers and users"}),e.jsx("li",{children:"To keep the Services safe and secure"}),e.jsx("li",{children:"We also use non-identifiable information gathered for statistical purposes to keep track of the number of visits to the Services with a view to introducing improvements and improving usability of the Services."})]})]}),e.jsxs("div",{className:"text-container",children:[e.jsx("h3",{children:"Information Security And Accuracy"}),e.jsx("p",{children:"We implement appropriate security measures to protect your information from unauthorized access, alteration, disclosure, or destruction. However, no internet or email transmission is ever fully secure or error-free. In particular, email sent to or from the Services may not be secure. Therefore, you should take special care in deciding what information you send to us via email. Please keep this in mind when disclosing any information to ResearchSat via the Internet. In addition, we are not responsible for circumvention of any privacy settings or security measures contained on the Website, or third party websites."}),e.jsx("p",{children:"We take all reasonable steps to ensure that the data we collect is reliable for its intended use, accurate, complete and up to date. You can always contact us in order to update your information, request access to it, or request its deletion."})]}),e.jsxs("div",{className:"text-container",children:[e.jsx("h3",{children:"Changes to this Privacy Policy"}),e.jsx("p",{children:"We may update this Privacy Policy from time to time. If we make significant changes in the way we treat your personal information, or to the Privacy Policy, we will provide notice to you on the Services or by some other means, such as email. The most current version of the Privacy Policy will govern our use of your information and will be available on the Website. By continuing to use the Services after those changes become effective, you agree to be bound by the revised Privacy Policy."})]}),e.jsxs("div",{className:"text-container last",children:[e.jsx("h3",{children:"Contact Us"}),e.jsxs("p",{children:["If you have any questions or concerns about this Privacy Policy or its implementation, please contact us at ",e.jsx("a",{href:"mailto:<EMAIL>",className:"turquoise",children:"<EMAIL>"}),"."]}),e.jsxs("p",{children:["This Privacy Policy was last updated on ",e.jsx("strong",{children:"January 1, 2023"}),"."]})]})]})})})})]}));export{i as default};
