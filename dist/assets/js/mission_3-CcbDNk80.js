import{j as s}from"./main-yODui1Lp.js";import"./vendor-react-DkG9wxl6.js";import{C as e}from"./CalButton-Tnfz923M.js";const a="_scheduleMeetingSection_vkql0_1",c="_container_vkql0_7",l="_backgroundImage_vkql0_14",n="_contentWrapper_vkql0_25",i="_leftContent_vkql0_38",t="_messageContainer_vkql0_48",_="_grayText_vkql0_57",r="_whiteText_vkql0_61",o="_scheduleButton_vkql0_65",d="_rightContent_vkql0_94",h="_reachUs_vkql0_104",m="_contactInfo_vkql0_114",g="_emailLink_vkql0_122",j="_phoneNumber_vkql0_140",v=()=>s.jsx("div",{className:a,children:s.jsxs("div",{className:c,children:[s.jsx("img",{className:l,src:"/assets/png/schedule-meeting-bg-DqMETq78.png",alt:"Schedule a meeting background"}),s.jsxs("div",{className:n,children:[s.jsxs("div",{className:i,children:[s.jsxs("div",{className:t,children:[s.jsxs("span",{className:_,children:["Nothing beats a good old-fashioned face-to-face chat.",s.jsx("br",{}),s.jsx("br",{})]}),s.jsx("span",{className:r,children:"Schedule a call now—we know you've got a packed schedule!"})]}),s.jsx(e,{className:o,calLink:"researchsat-2023/30min",namespace:"30min",config:{layout:"month_view"},children:"Schedule Meeting"})]}),s.jsxs("div",{className:d,children:[s.jsx("div",{className:h,children:"Reach us @"}),s.jsxs("div",{className:m,children:[s.jsx("a",{href:"mailto:<EMAIL>",className:g,children:"<EMAIL>"}),s.jsx("div",{className:j,children:"+61 4525 94883"})]})]})]})]})}),p="/assets/jpg/mission_1-CgTiVdg6.jpg",k="/assets/jpg/mission_2-DZ6ghLhy.jpg",x="/assets/jpg/mission_3-BKHhCZit.jpg";export{v as S,k as a,x as b,p as m};
